document.addEventListener('DOMContentLoaded', function () {
    
    // DOM元素
    const storageForm = document.getElementById('storageForm');
    const brand = document.getElementById('brand');
    const model = document.getElementById('model');
    const type = document.getElementById('type');
    const capacity = document.getElementById('capacity');
    const formFactor = document.getElementById('formFactor');
    const interface = document.getElementById('interface');
    const readSpeed = document.getElementById('readSpeed');
    const writeSpeed = document.getElementById('writeSpeed');
    const cacheSize = document.getElementById('cacheSize');
    const tbw = document.getElementById('tbw');
    const mtbf = document.getElementById('mtbf');
    const price = document.getElementById('price');
    const storageImage = document.getElementById('storageImage');
    const imagePreview = document.getElementById('imagePreview');
    const imagePreviewContainer = document.getElementById('imagePreviewContainer');
    const removeImageBtn = document.getElementById('removeImageBtn');
    const notes = document.getElementById('notes');
    const storageTableBody = document.getElementById('storageTableBody');
    const storageSearch = document.getElementById('storageSearch');
    const brandFilter = document.getElementById('brandFilter');
    const typeFilter = document.getElementById('typeFilter');
    const resetFilterBtn = document.getElementById('resetFilterBtn');
    const totalCount = document.getElementById('totalCount');
    const prevPage = document.getElementById('prevPage');
    const nextPage = document.getElementById('nextPage');
    const pageInfo = document.getElementById('pageInfo');
    const storageModal = document.getElementById('storageModal');
    const closeModal = document.getElementById('closeModal');
    const closeModalBtn = document.getElementById('closeModalBtn');
    const storageDetails = document.getElementById('storageDetails');
    const editBtn = document.getElementById('editBtn');
    const deleteBtn = document.getElementById('deleteBtn');

    // 全局变量
    let currentPage = 1;
    const pageSize = 10;
    let totalRecords = 0;
    let storages = [];
    let imageFile = null;
    let currentStorageId = null;
    let isEditing = false;
    // 默认为亮色模式，只有当localStorage中明确设置为'true'时才启用暗夜模式
    let isDarkMode = localStorage.getItem('darkMode') === 'true';

    // 在全局变量下添加API端点常量
    const API_ENDPOINTS = {
        STORAGES: '/api/storages',
        STORAGE: (id) => `/api/storages/${id}`
    };

    // 初始化
    init();

    // 初始化函数
    function init() {
        setupEventListeners();
        loadStorages();
        initDarkMode();
        // 立即附加图片点击事件
        attachImageClickHandlers();
        // 添加权限初始化
        initPermissions();
    }

    // 初始化暗夜模式
    function initDarkMode() {
        // 检查本地存储中的暗夜模式设置
        if (isDarkMode) {
            document.body.classList.add('dark-mode');
            updateDarkModeIcon(true);
        } else {
            document.body.classList.remove('dark-mode');
            updateDarkModeIcon(false);
        }
    }
    
    // 更新暗夜模式图标
    function updateDarkModeIcon(isDark) {
        const darkModeToggle = document.getElementById('darkModeToggle');
        if (darkModeToggle) {
            if (isDark) {
                darkModeToggle.innerHTML = '<i class="fas fa-sun"></i>';
                darkModeToggle.classList.remove('bg-gray-200', 'hover:bg-gray-300', 'text-gray-700');
                darkModeToggle.classList.add('bg-yellow-400', 'hover:bg-yellow-500', 'text-yellow-900');
                darkModeToggle.title = '切换至亮色模式';
                darkModeToggle.style.boxShadow = '0 0 10px rgba(251, 191, 36, 0.5)';
            } else {
                darkModeToggle.innerHTML = '<i class="fas fa-moon"></i>';
                darkModeToggle.classList.remove('bg-yellow-400', 'hover:bg-yellow-500', 'text-yellow-900');
                darkModeToggle.classList.add('bg-gray-200', 'hover:bg-gray-300', 'text-gray-700');
                darkModeToggle.title = '切换至暗夜模式';
                darkModeToggle.style.boxShadow = 'none';
            }
        }
    }
    
    // 切换暗夜模式
    function toggleDarkMode() {
        isDarkMode = !isDarkMode;
        localStorage.setItem('darkMode', isDarkMode);
        
        if (isDarkMode) {
            document.body.classList.add('dark-mode');
        } else {
            document.body.classList.remove('dark-mode');
        }
        
        updateDarkModeIcon(isDarkMode);
        
        // 在暗夜模式切换后刷新数据显示，以应用新的样式
        if (window.innerWidth < 640) {
            loadStorages();  // 重新加载数据，更新移动端卡片样式
        }
    }

    // 设置事件监听器
    function setupEventListeners() {
        // 暗夜模式切换
        const darkModeToggle = document.getElementById('darkModeToggle');
        if (darkModeToggle) {
            darkModeToggle.addEventListener('click', toggleDarkMode);
        }
        
        // 图片上传
        if (storageImage) {
            storageImage.addEventListener('change', handleImageUpload);
        }

        if (removeImageBtn) {
            removeImageBtn.addEventListener('click', removeImage);
        }

        // 表单提交
        if (storageForm) {
            storageForm.addEventListener('submit', handleFormSubmit);
        }

        // 搜索和筛选
        if (storageSearch) {
            storageSearch.addEventListener('input', debounce(() => {
                currentPage = 1;
                loadStorages();
            }, 300));
        }

        if (brandFilter) {
            brandFilter.addEventListener('change', () => {
                currentPage = 1;
                loadStorages();
            });
        }

        if (typeFilter) {
            typeFilter.addEventListener('change', () => {
                currentPage = 1;
                loadStorages();
            });
        }

        if (resetFilterBtn) {
            resetFilterBtn.addEventListener('click', () => {
                storageSearch.value = '';
                brandFilter.value = 'all';
                typeFilter.value = 'all';
                currentPage = 1;
                loadStorages();
            });
        }

        // 分页
        if (prevPage) {
            prevPage.addEventListener('click', () => changePage(currentPage - 1));
        }

        if (nextPage) {
            nextPage.addEventListener('click', () => changePage(currentPage + 1));
        }
        
        // 首页和尾页
        const firstPageBtn = document.getElementById('firstPage');
        if (firstPageBtn) {
            firstPageBtn.addEventListener('click', () => changePage(1));
        }

        const lastPageBtn = document.getElementById('lastPage');
        if (lastPageBtn) {
            lastPageBtn.addEventListener('click', () => {
                const totalPages = Math.ceil(totalRecords / pageSize) || 1;
                changePage(totalPages);
            });
        }

        // 页码跳转
        const goToPageBtn = document.getElementById('goToPage');
        if (goToPageBtn) {
            goToPageBtn.addEventListener('click', () => {
                const pageJumpInput = document.getElementById('pageJump');
                if (pageJumpInput) {
                    let pageNum = parseInt(pageJumpInput.value);
                    const totalPages = Math.ceil(totalRecords / pageSize) || 1;
                    
                    if (isNaN(pageNum) || pageNum < 1) {
                        pageNum = 1;
                    } else if (pageNum > totalPages) {
                        pageNum = totalPages;
                    }
                    
                    changePage(pageNum);
                    pageJumpInput.value = '';
                }
            });
        }

        // 监听页码输入框的回车事件
        const pageJumpInput = document.getElementById('pageJump');
        if (pageJumpInput) {
            pageJumpInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    const goToPageBtn = document.getElementById('goToPage');
                    if (goToPageBtn) {
                        goToPageBtn.click();
                    }
                }
            });
        }

        // 模态框
        if (closeModal) {
            closeModal.addEventListener('click', () => {
                if (storageModal) {
                    storageModal.classList.add('hidden');
                } else {
                    const detailModal = document.getElementById('storageDetailModal');
                    if (detailModal) {
                        detailModal.classList.add('hidden');
                    }
                }
            });
        }

        if (closeModalBtn) {
            closeModalBtn.addEventListener('click', () => {
                if (storageModal) {
                    storageModal.classList.add('hidden');
                } else {
                    const detailModal = document.getElementById('storageDetailModal');
                    if (detailModal) {
                        detailModal.classList.add('hidden');
                    }
                }
            });
        }

        if (editBtn) {
            editBtn.addEventListener('click', handleEdit);
        }

        if (deleteBtn) {
            deleteBtn.addEventListener('click', () => {
                if (currentStorageId) {
                    confirmDeleteStorage(currentStorageId);
                }
            });
        }

        document.getElementById('autoFillBtn').addEventListener('click', parseStorageInfo);
    }

    // 设置API密钥UI - 移除整个函数或者保留空函数
    function setupAIApiKeyUI() {
        // 函数保留但内部逻辑移除
        console.log('AI功能已禁用');
    }

    // 处理图片上传
    function handleImageUpload(e) {
        const file = e.target.files[0];
        if (!file) return;

        // 验证文件类型
        const validTypes = ['image/jpeg', 'image/png', 'image/gif'];
        if (!validTypes.includes(file.type)) {
            alert('请上传JPG、PNG或GIF格式的图片');
            return;
        }

        // 验证文件大小
        if (file.size > 5 * 1024 * 1024) { // 5MB
            alert('图片大小不能超过5MB');
            return;
        }

        imageFile = file;
        const reader = new FileReader();
        reader.onload = function (event) {
            imagePreview.src = event.target.result;
            imagePreviewContainer.classList.remove('hidden');

            // 添加点击预览功能
            imagePreview.onclick = () => {
                openImageFullscreen(event.target.result);
            };
            imagePreview.style.cursor = 'pointer';
            imagePreview.title = '点击查看大图';
            
            // 添加WebP转换提示
            if (supportWebP) {
                const fileSize = (file.size / 1024).toFixed(1);
                const estimatedWebPSize = (file.size * 0.7 / 1024).toFixed(1); // 估计WebP大约为原图70%大小
                console.log(`[图片优化] 文件大小: ${fileSize}KB, 转换后预估: ${estimatedWebPSize}KB (WebP格式)`);
                
                // 显示转换提示
                const uploadHint = document.createElement('p');
                uploadHint.className = 'text-xs text-green-600 mt-1 upload-notification';
                uploadHint.innerHTML = `<i class="fas fa-info-circle mr-1"></i>图片将在上传时自动转换为WebP格式 (预计可减小约${Math.round((1 - 0.7) * 100)}%)`;
                
                // 如果已有提示则替换，否则添加
                const existingHint = imagePreviewContainer.querySelector('.upload-notification');
                if (existingHint) {
                    existingHint.replaceWith(uploadHint);
                } else {
                    imagePreviewContainer.appendChild(uploadHint);
                }
            }
        };
        reader.readAsDataURL(file);
    }

    // 移除图片
    function removeImage() {
        storageImage.value = '';
        imagePreview.src = '';
        imagePreviewContainer.classList.add('hidden');
        imageFile = null;
    }

    // 处理表单提交
    function handleFormSubmit(e) {
        e.preventDefault();

        const formData = new FormData();
        formData.append('brand', brand.value);
        formData.append('model', model.value);
        formData.append('type', type.value);
        formData.append('capacity', capacity.value);
        formData.append('form_factor', formFactor.value);
        formData.append('interface', interface.value);
        formData.append('read_speed', readSpeed.value);
        formData.append('write_speed', writeSpeed.value);
        formData.append('cacheSize', cacheSize.value); // 修正键名
        formData.append('tbw', tbw.value);             // 修正键名
        formData.append('mtbf', mtbf.value);           // 修正键名
        formData.append('price', price.value);
        formData.append('notes', notes.value);
        
        if (imageFile) {
            formData.append('image', imageFile);
        }

        // 提交表单
        const url = isEditing ? `/api/storages/${currentStorageId}` : '/api/storages';
        const method = isEditing ? 'PUT' : 'POST';

        // 显示进度条并禁用提交按钮
        showUploadProgress();
        disableSubmitButton(true, isEditing);

        // 使用XMLHttpRequest来支持上传进度
        uploadWithProgress(url, method, formData)
        .then(data => {
            showSuccessMessage(isEditing ? '硬盘信息更新成功' : '硬盘信息添加成功');
            resetForm();
            loadStorages();
        })
        .catch(error => {
            showErrorMessage(error.message || '操作失败');
        })
        .finally(() => {
            // 隐藏进度条并恢复提交按钮
            hideUploadProgress();
            disableSubmitButton(false, isEditing);
        });
    }

    // 重置表单
    function resetForm() {
        storageForm.reset();
        removeImage();
        isEditing = false;
        currentStorageId = null;
        // 更改提交按钮文本
        const submitBtn = storageForm.querySelector('button[type="submit"]');
        submitBtn.innerHTML = '<i class="fas fa-save mr-1 sm:mr-2"></i> 保存硬盘信息';
    }

    // 加载硬盘列表
    function loadStorages() {
        const searchTerm = storageSearch ? storageSearch.value.trim() : '';
        const selectedBrand = brandFilter ? brandFilter.value : 'all';
        const selectedType = typeFilter ? typeFilter.value : 'all';

        // 构建API URL
        let url = `${API_ENDPOINTS.STORAGES}?page=${currentPage}&limit=${pageSize}`;

        if (searchTerm) {
            url += `&search=${encodeURIComponent(searchTerm)}`;
        }

        if (selectedBrand && selectedBrand !== 'all') {
            url += `&brand=${encodeURIComponent(selectedBrand)}`;
        }

        if (selectedType && selectedType !== 'all') {
            url += `&type=${encodeURIComponent(selectedType)}`;
        }

        fetchWithAuth(url)
            .then(response => {
                if (!response || !response.ok) {
                    throw new Error('加载硬盘列表失败');
                }
                return response.json();
            })
            .then(data => {
                // 保存数据到全局变量
                storages = data.storages || [];
                totalRecords = data.total || 0;
                
                // 渲染表格
                renderStorageTable(storages);
                
                // 更新分页
                updatePagination();
            })
            .catch(error => {
                console.error('Error:', error);
                showErrorMessage(error.message);
            });
    }

    // 渲染硬盘表格
    function renderStorageTable(data) {
        console.log('[DEBUG] Rendering storage table with', data.length, 'records');
        storageTableBody.innerHTML = '';

        if (!data || data.length === 0) {
            console.log('[DEBUG] No data to display');
            const emptyRow = document.createElement('tr');
            const emptyCell = document.createElement('td');
            emptyCell.colSpan = 7;
            emptyCell.className = 'px-3 py-4 text-center text-gray-500';
            emptyCell.textContent = '暂无数据';
            emptyRow.appendChild(emptyCell);
            storageTableBody.appendChild(emptyRow);
            return;
        }

        // 检测是否为移动设备
        const isMobile = window.innerWidth < 640;
        
        // 根据设备类型选择渲染方法
        if (isMobile) {
            renderMobileCards(data);
                } else {
            // PC端表格布局
            data.forEach((storage, index) => {
                const row = document.createElement('tr');
                // 添加淡入动画和悬停效果
                row.className = 'hover:bg-gray-50 transition-colors border-b border-gray-200';
                row.style.animation = 'fadeIn 0.3s ease-in-out';
                row.style.animationFillMode = 'both';
                row.style.animationDelay = `${index * 0.05}s`;

                // 图片列
                const imageCell = document.createElement('td');
                imageCell.className = 'px-2 py-2 sm:px-3 sm:py-3 hidden sm:table-cell text-center';
                
                const imgContainer = document.createElement('div');
                imgContainer.className = 'w-10 h-10 mx-auto relative group cursor-pointer';
                
                const defaultImage = '/images/default-storage.png';
                const imgElement = document.createElement('img');
                imgElement.src = storage.image_url || defaultImage;
                imgElement.alt = storage.model || '硬盘图片';
                imgElement.className = `w-10 h-10 object-contain mx-auto rounded-md border border-gray-200 ${!storage.image_url ? 'opacity-60' : ''} transition-transform group-hover:scale-110 cursor-pointer`;
                
                // 使用更直接的事件绑定方式
                const imageUrl = storage.image_url || defaultImage;
                imgElement.setAttribute('data-src', imageUrl);
                imgElement.addEventListener('click', function() {
                    console.log('Image clicked:', this.getAttribute('data-src'));
                    if (typeof openImageFullscreen === 'function') {
                        openImageFullscreen(this.getAttribute('data-src'));
                    } else {
                        console.error('openImageFullscreen function not available');
                    }
                });
                
                // 图片容器也添加点击事件
                imgContainer.addEventListener('click', function() {
                    console.log('Image container clicked:', imageUrl);
                    if (typeof openImageFullscreen === 'function') {
                        openImageFullscreen(imageUrl);
            } else {
                        console.error('openImageFullscreen function not available');
                    }
                });
                
                imgElement.onerror = () => {
                    console.log('[DEBUG] Image failed to load, using default');
                    imgElement.src = defaultImage;
                    imgElement.setAttribute('data-src', defaultImage);
                };
                
                // 添加查看图标提示
                const viewOverlay = document.createElement('div');
                viewOverlay.className = 'absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 rounded-md flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity';
                viewOverlay.innerHTML = '<i class="fas fa-search-plus text-white text-opacity-800"></i>';
                
                imgContainer.appendChild(imgElement);
                imgContainer.appendChild(viewOverlay);
                imageCell.appendChild(imgContainer);
                row.appendChild(imageCell);

                // 型号列
                const modelCell = document.createElement('td');
                modelCell.className = 'px-2 py-2 sm:px-3 sm:py-3';
                
                const modelText = document.createElement('div');
                modelText.className = 'text-sm font-medium text-gray-900 text-truncate flex items-center';
                modelText.title = storage.model || '-';
                
                // 添加硬盘类型图标
                const typeIconContainer = document.createElement('span');
                typeIconContainer.className = 'inline-block mr-2';
                if (storage.type === 'SSD') {
                    typeIconContainer.innerHTML = '<i class="fas fa-solid fa-database text-green-600"></i>';
                } else if (storage.type === 'HDD') {
                    typeIconContainer.innerHTML = '<i class="fas fa-hdd text-blue-600"></i>';
                } else if (storage.type.includes('NVMe')) {
                    typeIconContainer.innerHTML = '<i class="fas fa-memory text-amber-600"></i>';
            } else {
                    typeIconContainer.innerHTML = '<i class="fas fa-hdd text-gray-500"></i>';
                }
                modelText.appendChild(typeIconContainer);
                
                // 型号文本
                const modelName = document.createElement('span');
                modelName.textContent = storage.model || '-';
                modelText.appendChild(modelName);
                
                // 添加品牌信息（如果有）
                if (storage.brand) {
                    const brandInfo = document.createElement('div');
                    brandInfo.className = 'text-xs text-gray-500 mt-0.5';
                    brandInfo.textContent = storage.brand;
                    modelCell.appendChild(brandInfo);
                }
                
                modelCell.insertBefore(modelText, modelCell.firstChild);
                row.appendChild(modelCell);

                // 品牌列
                const brandCell = document.createElement('td');
                brandCell.className = 'px-2 py-2 sm:px-3 sm:py-3 hidden sm:table-cell';
                
                // 品牌标签
                const brandBadge = document.createElement('span');
                brandBadge.className = 'storage-badge transition-transform hover:scale-105';
                brandBadge.textContent = storage.brand || '-';
                brandCell.appendChild(brandBadge);
                
                row.appendChild(brandCell);

                // 容量/类型列
                const capacityCell = document.createElement('td');
                capacityCell.className = 'px-2 py-2 sm:px-3 sm:py-3';
                
                const capacityElement = document.createElement('div');
                capacityElement.className = 'flex flex-col';
                
                // 容量显示
                const capacity = document.createElement('span');
                capacity.className = 'font-medium';
                capacity.textContent = storage.capacity ? `${storage.capacity} GB` : '-';
                capacityElement.appendChild(capacity);
                
                // 类型标签
                const typeBadgeClass = storage.type === 'SSD' ? 'ssd-storage' : 
                                      storage.type === 'HDD' ? 'hdd-storage' : 
                                      storage.type.includes('NVMe') ? 'nvme-storage' : '';
                
                const typeBadge = document.createElement('span');
                typeBadge.className = `spec-tag ${typeBadgeClass}`;
                typeBadge.textContent = storage.type || '-';
                capacityElement.appendChild(typeBadge);
                
                capacityCell.appendChild(capacityElement);
                row.appendChild(capacityCell);

                // 颗粒列
                const mtbfCell = document.createElement('td');
                mtbfCell.className = 'px-2 py-2 sm:px-3 sm:py-3 hidden sm:table-cell';
                mtbfCell.textContent = storage.mtbf || '-';
                row.appendChild(mtbfCell);

                // 读写速度列
                const speedCell = document.createElement('td');
                speedCell.className = 'px-2 py-2 sm:px-3 sm:py-3 hidden sm:table-cell';
                
                if (storage.read_speed || storage.write_speed) {
                    const speedIcon = document.createElement('span');
                    speedIcon.className = 'mr-1 text-purple-600';
                    speedIcon.innerHTML = '<i class="fas fa-tachometer-alt"></i>';
                    
                    const speedTag = document.createElement('span');
                    speedTag.className = 'spec-tag freq inline-flex items-center transition-all hover:shadow-sm';
                    speedTag.appendChild(speedIcon);
                    
                    const speedText = document.createElement('span');
                    speedText.textContent = storage.read_speed ? 
                        (storage.write_speed ? `${storage.read_speed}/${storage.write_speed} MB/s` : `${storage.read_speed} MB/s`) : 
                        (storage.write_speed ? `${storage.write_speed} MB/s` : '-');
                    speedTag.appendChild(speedText);
                    
                    speedCell.appendChild(speedTag);
                } else {
                    speedCell.textContent = '-';
                }
                
                row.appendChild(speedCell);

                // 操作列
                const actionCell = document.createElement('td');
                actionCell.className = 'px-2 py-2 sm:px-3 sm:py-3 text-center';

                // 操作按钮容器
                const actionContainer = document.createElement('div');
                actionContainer.className = 'flex justify-center space-x-2';

                // 查看按钮
                const viewButton = document.createElement('button');
                viewButton.className = 'p-1 text-purple-600 hover:text-purple-800 hover:bg-purple-50 rounded-full w-7 h-7 flex items-center justify-center transition-all';
                viewButton.innerHTML = '<i class="fas fa-eye"></i>';
                viewButton.title = '查看详情';
                viewButton.dataset.id = storage.id;
                viewButton.addEventListener('click', () => viewStorageDetails(storage.id));
                viewButton.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-2px)';
                });
                viewButton.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                });
                actionContainer.appendChild(viewButton);

                // 编辑按钮
                const editButton = document.createElement('button');
                editButton.className = 'p-1 text-green-600 hover:text-green-800 hover:bg-green-50 rounded-full w-7 h-7 flex items-center justify-center transition-all';
                editButton.innerHTML = '<i class="fas fa-edit"></i>';
                editButton.title = '编辑';
                editButton.dataset.id = storage.id;
                editButton.addEventListener('click', () => editStorage(storage.id));
                editButton.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-2px)';
                });
                editButton.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                });
                actionContainer.appendChild(editButton);

                // 删除按钮
                const deleteButton = document.createElement('button');
                deleteButton.className = 'p-1 text-red-600 hover:text-red-800 hover:bg-red-50 rounded-full w-7 h-7 flex items-center justify-center transition-all';
                deleteButton.innerHTML = '<i class="fas fa-trash"></i>';
                deleteButton.title = '删除';
                deleteButton.dataset.id = storage.id;
                deleteButton.addEventListener('click', () => {
                    confirmDeleteStorage(storage.id);
                });
                deleteButton.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-2px)';
                });
                deleteButton.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                });
                actionContainer.appendChild(deleteButton);

                actionCell.appendChild(actionContainer);
                row.appendChild(actionCell);

                storageTableBody.appendChild(row);
            });
        }
    }
    
    // 移动端卡片式布局渲染
    function renderMobileCards(data) {
        storageTableBody.innerHTML = '';

        const tableElement = storageTableBody.closest('table');
        if (tableElement) {
            tableElement.style.display = 'block';
            tableElement.style.width = '100%';
            tableElement.style.maxWidth = '100%';
            tableElement.style.borderCollapse = 'collapse';
            tableElement.style.borderSpacing = '0';
            const theadElement = tableElement.querySelector('thead');
            if (theadElement) {
                theadElement.style.display = 'none';
            }
        }

        storageTableBody.style.display = 'block';
        storageTableBody.style.width = '100%';
        storageTableBody.style.maxWidth = '100%';

        // 检查用户权限
        isAdmin().then(isAdminUser => {
            data.forEach((storage, index) => {
                const cardOuterContainer = document.createElement('div');
                cardOuterContainer.className = 'storage-card-outer-container';
                cardOuterContainer.style.animation = `fadeIn 0.3s ease-in-out ${index * 0.05}s both`;

                const card = document.createElement('div');
                card.className = 'storage-card';
                card.style.transition = 'transform 0.2s ease, box-shadow 0.2s ease';

                card.addEventListener('touchstart', function() {
                    this.style.transform = 'scale(0.99)';
                    this.style.boxShadow = document.body.classList.contains('dark-mode') ? 
                        '0 2px 8px rgba(0,0,0,0.4)' : '0 2px 8px rgba(0,0,0,0.05)';
                });
                card.addEventListener('touchend', function() {
                    this.style.transform = 'scale(1)';
                    this.style.boxShadow = document.body.classList.contains('dark-mode') ? 
                        '0 5px 15px rgba(0,0,0,0.3)' : '0 5px 15px rgba(0,0,0,0.07)';
                });

                let headerBgColor = '#f8f9fa'; // Default header
                let borderColor = 'rgba(203, 213, 225, 0.5)'; // Default border

                // 根据硬盘类型设置不同的颜色主题
                if (storage.type === 'SSD') {
                    headerBgColor = document.body.classList.contains('dark-mode') ? 
                        'rgba(5, 150, 105, 0.1)' : 'rgba(5, 150, 105, 0.05)'; // 绿色主题
                    borderColor = document.body.classList.contains('dark-mode') ? 
                        'rgba(5, 150, 105, 0.3)' : 'rgba(5, 150, 105, 0.15)';
                } else if (storage.type === 'HDD') {
                    headerBgColor = document.body.classList.contains('dark-mode') ? 
                        'rgba(79, 70, 229, 0.1)' : 'rgba(79, 70, 229, 0.05)'; // 紫色主题
                    borderColor = document.body.classList.contains('dark-mode') ? 
                        'rgba(79, 70, 229, 0.3)' : 'rgba(79, 70, 229, 0.15)';
                } else if (storage.type && storage.type.includes('NVMe')) {
                    headerBgColor = document.body.classList.contains('dark-mode') ? 
                        'rgba(217, 119, 6, 0.1)' : 'rgba(217, 119, 6, 0.05)'; // 琥珀色主题
                    borderColor = document.body.classList.contains('dark-mode') ? 
                        'rgba(217, 119, 6, 0.3)' : 'rgba(217, 119, 6, 0.15)';
                } else {
                    // 默认暗色模式下的颜色
                    headerBgColor = document.body.classList.contains('dark-mode') ? 
                        'rgba(0, 0, 0, 0.2)' : headerBgColor;
                    borderColor = document.body.classList.contains('dark-mode') ? 
                        'rgba(255, 255, 255, 0.05)' : borderColor;
                }

                // 创建卡片头部
                const cardHeader = document.createElement('div');
                cardHeader.className = 'storage-card-header';
                cardHeader.style.backgroundColor = headerBgColor;
                cardHeader.style.borderBottom = `1px solid ${borderColor}`;

                const modelName = document.createElement('div');
                modelName.textContent = storage.model || '未知型号';
                modelName.style.fontSize = '1rem';
                modelName.style.fontWeight = document.body.classList.contains('dark-mode') ? '600' : 'bold';
                modelName.style.color = document.body.classList.contains('dark-mode') ? 
                    '#ffffff' : '#1a202c'; // 暗夜模式下使用纯白色增强可见性
                modelName.style.wordBreak = 'break-word';
                modelName.style.flexGrow = '1';
                modelName.style.marginRight = '8px';

                // 硬盘类型标签
                const typeBadgeClass = storage.type === 'SSD' ? 'ssd-storage' : 
                                      storage.type === 'HDD' ? 'hdd-storage' : 
                                      storage.type.includes('NVMe') ? 'nvme-storage' : '';
                
                const typeBadge = document.createElement('span');
                typeBadge.className = `storage-badge ${typeBadgeClass}`;
                
                // 增强硬盘类型标签在暗夜模式下的可见性
                if (document.body.classList.contains('dark-mode')) {
                    if (storage.type === 'SSD') {
                        typeBadge.style.backgroundColor = 'rgba(5, 150, 105, 0.3)';
                        typeBadge.style.color = '#34d399'; // 亮绿色
                        typeBadge.style.borderColor = 'rgba(5, 150, 105, 0.4)';
                    } else if (storage.type === 'HDD') {
                        typeBadge.style.backgroundColor = 'rgba(79, 70, 229, 0.3)';
                        typeBadge.style.color = '#818cf8'; // 亮蓝色
                        typeBadge.style.borderColor = 'rgba(79, 70, 229, 0.4)';
                    } else if (storage.type && storage.type.includes('NVMe')) {
                        typeBadge.style.backgroundColor = 'rgba(217, 119, 6, 0.3)';
                        typeBadge.style.color = '#fbbf24'; // 亮黄色
                        typeBadge.style.borderColor = 'rgba(217, 119, 6, 0.4)';
                    }
                    typeBadge.style.fontWeight = '600';
                    typeBadge.style.textShadow = '0 0 1px rgba(0,0,0,0.3)'; // 添加文字阴影增强可读性
                }
                
                typeBadge.textContent = storage.type || '未知';
                
                cardHeader.appendChild(modelName);
                cardHeader.appendChild(typeBadge);

                // 创建卡片主体
                const cardBody = document.createElement('div');
                cardBody.className = 'storage-card-body';
                cardBody.style.backgroundColor = document.body.classList.contains('dark-mode') ? 
                    'var(--bg-secondary)' : 'white';

                // 图片容器
                const imgContainer = document.createElement('div');
                imgContainer.className = 'storage-card-img-container cursor-pointer';
                imgContainer.style.backgroundColor = document.body.classList.contains('dark-mode') ? 
                    '#1a1a1a' : 'white';
                imgContainer.style.borderColor = document.body.classList.contains('dark-mode') ? 
                    '#333' : '#eee';
                
                const imgElement = document.createElement('img');
                imgElement.className = 'storage-card-img';
                imgElement.alt = storage.model || '硬盘图片';
                
                const defaultImage = '/images/default-storage.png';
                if (storage.image_url) {
                    imgElement.src = storage.image_url;
                    
                    // 为图片容器添加更可靠的点击事件
                    const imageUrl = storage.image_url;
                    imgElement.setAttribute('data-src', imageUrl);
                    imgElement.addEventListener('click', function(e) {
                        e.stopPropagation(); // 阻止事件冒泡
                        console.log('Mobile image clicked:', imageUrl);
                        if (typeof openImageFullscreen === 'function') {
                            openImageFullscreen(imageUrl);
                        } else {
                            console.error('openImageFullscreen function not available');
                        }
                    });
                    
                    // 为图片容器也添加点击事件
                    imgContainer.addEventListener('click', function() {
                        console.log('Mobile image container clicked:', imageUrl);
                        if (typeof openImageFullscreen === 'function') {
                            openImageFullscreen(imageUrl);
                        } else {
                            console.error('openImageFullscreen function not available');
                        }
                    });
                } else {
                    imgContainer.textContent = '硬盘图片';
                    imgContainer.style.fontSize = '0.75rem';
                    imgContainer.style.color = document.body.classList.contains('dark-mode') ? 
                        'var(--text-secondary)' : '#6b7280';
                    imgContainer.style.textAlign = 'center';
                }
                
                imgElement.onerror = function() {
                    this.onerror = null; 
                    imgContainer.innerHTML = '';
                    const placeholderText = document.createElement('span');
                    placeholderText.textContent = '硬盘图片';
                    placeholderText.style.fontSize = '0.75rem';
                    placeholderText.style.color = document.body.classList.contains('dark-mode') ? 
                        'var(--text-secondary)' : '#6b7280';
                    placeholderText.style.textAlign = 'center';
                    imgContainer.appendChild(placeholderText);
                    this.style.display = 'none';
                };
                
                if (storage.image_url) imgContainer.appendChild(imgElement);

                // 信息容器
                const infoContainer = document.createElement('div');
                infoContainer.className = 'storage-card-info';

                // 品牌信息
                if (storage.brand) {
                    const brandText = document.createElement('div');
                    brandText.className = 'text-sm text-gray-600';
                    brandText.style.color = document.body.classList.contains('dark-mode') ? 
                        '#a0aec0' : '#4b5563'; // 更亮的文字颜色
                    brandText.textContent = `品牌: ${storage.brand}`;
                    infoContainer.appendChild(brandText);
                }

                // 容量信息
                if (storage.capacity) {
                    const capacityText = document.createElement('div');
                    capacityText.style.fontWeight = document.body.classList.contains('dark-mode') ? '600' : 'bold'; // 暗模式下字体更粗
                    capacityText.style.color = document.body.classList.contains('dark-mode') ? 
                        '#f5f5f5' : '#374151'; // 更亮的白色
                    capacityText.style.fontSize = document.body.classList.contains('dark-mode') ? '1.05rem' : 'inherit'; // 暗模式下字体稍大
                    capacityText.textContent = `容量: ${storage.capacity} GB`;
                    infoContainer.appendChild(capacityText);
                }

                // 规格信息
                if (storage.form_factor) {
                    const formFactorText = document.createElement('div');
                    formFactorText.className = 'text-sm text-gray-600';
                    formFactorText.style.color = document.body.classList.contains('dark-mode') ? 
                        '#a0aec0' : '#4b5563'; // 更亮的文字颜色
                    formFactorText.textContent = `规格: ${storage.form_factor}`;
                    infoContainer.appendChild(formFactorText);
                }

                // 标签组
                const tagGroup = document.createElement('div');
                tagGroup.className = 'tag-group';

                // 接口标签
                if (storage.interface) {
                    const interfaceTag = document.createElement('span');
                    interfaceTag.className = 'spec-tag socket';
                    
                    // 暗夜模式下增强标签可见性
                    if (document.body.classList.contains('dark-mode')) {
                        interfaceTag.style.backgroundColor = 'rgba(180, 83, 9, 0.25)';
                        interfaceTag.style.color = '#fbbf24'; // 明亮的黄色
                        interfaceTag.style.borderColor = 'rgba(180, 83, 9, 0.4)';
                        interfaceTag.style.fontWeight = '500';
                    }
                    
                    interfaceTag.textContent = storage.interface;
                    tagGroup.appendChild(interfaceTag);
                }

                // 速度标签
                if (storage.read_speed || storage.write_speed) {
                    const speedTag = document.createElement('span');
                    speedTag.className = 'spec-tag freq';
                    
                    // 暗夜模式下增强标签可见性
                    if (document.body.classList.contains('dark-mode')) {
                        speedTag.style.backgroundColor = 'rgba(76, 29, 149, 0.25)';
                        speedTag.style.color = '#a78bfa'; // 明亮的紫色
                        speedTag.style.borderColor = 'rgba(76, 29, 149, 0.4)';
                        speedTag.style.fontWeight = '500';
                    }
                    
                    let speedText = '';
                    if (storage.read_speed && storage.write_speed) {
                        speedText = `${storage.read_speed}/${storage.write_speed}MB/s`;
                    } else if (storage.read_speed) {
                        speedText = `读:${storage.read_speed}MB/s`;
                    } else if (storage.write_speed) {
                        speedText = `写:${storage.write_speed}MB/s`;
                    }
                    
                    speedTag.textContent = speedText;
                    tagGroup.appendChild(speedTag);
                }

                // TBW标签
                if (storage.tbw) {
                    const tbwTag = document.createElement('span');
                    tbwTag.className = 'spec-tag tdp';
                    
                    // 暗夜模式下增强标签可见性
                    if (document.body.classList.contains('dark-mode')) {
                        tbwTag.style.backgroundColor = 'rgba(194, 65, 12, 0.25)';
                        tbwTag.style.color = '#f97316'; // 明亮的橙色
                        tbwTag.style.borderColor = 'rgba(194, 65, 12, 0.4)';
                        tbwTag.style.fontWeight = '500';
                    }
                    
                    tbwTag.textContent = `TBW: ${storage.tbw}TB`;
                    tagGroup.appendChild(tbwTag);
                }
                
                // 颗粒类型标签
                if (storage.mtbf) {
                    const mtbfTag = document.createElement('span');
                    mtbfTag.className = 'spec-tag process';
                    
                    // 暗夜模式下增强标签可见性
                    if (document.body.classList.contains('dark-mode')) {
                        mtbfTag.style.backgroundColor = 'rgba(6, 95, 70, 0.25)';
                        mtbfTag.style.color = '#10b981'; // 明亮的绿色
                        mtbfTag.style.borderColor = 'rgba(6, 95, 70, 0.4)';
                        mtbfTag.style.fontWeight = '500';
                    }
                    
                    mtbfTag.textContent = storage.mtbf;
                    tagGroup.appendChild(mtbfTag);
                }

                if (tagGroup.children.length > 0) {
                    infoContainer.appendChild(tagGroup);
                }

                cardBody.appendChild(imgContainer);
                cardBody.appendChild(infoContainer);

                // 创建卡片底部
                const cardFooter = document.createElement('div');
                cardFooter.className = 'storage-card-footer';
                cardFooter.style.backgroundColor = document.body.classList.contains('dark-mode') ? 
                    'rgba(0, 0, 0, 0.1)' : '#fdfdff';
                cardFooter.style.borderTop = document.body.classList.contains('dark-mode') ? 
                    '1px solid rgba(255, 255, 255, 0.05)' : `1px solid ${borderColor}`;

                // 创建操作按钮
                const viewButton = createActionButton('<i class="fas fa-eye mr-1"></i>查看', '查看', 
                    document.body.classList.contains('dark-mode') ? '#a855f7' : '#9333ea', 
                    () => viewStorageDetails(storage.id));
                    
                // 编辑和删除按钮根据权限处理
                const editButton = createActionButton('<i class="fas fa-edit mr-1"></i>编辑', '编辑', 
                    document.body.classList.contains('dark-mode') ? '#34d399' : '#10B981', 
                    () => editStorage(storage.id));
                    
                const deleteButton = createActionButton('<i class="fas fa-trash mr-1"></i>删除', '删除', 
                    document.body.classList.contains('dark-mode') ? '#f87171' : '#EF4444', 
                    () => confirmDeleteStorage(storage.id));
                    
                // 非管理员用户的按钮处理
                if (!isAdminUser) {
                    // 编辑按钮权限控制
                    editButton.addEventListener('click', function(e) {
                        e.preventDefault();
                        e.stopPropagation();
                        showErrorMessage('权限不足，只有管理员可以编辑数据');
                        return false;
                    }, true);
                    
                    // 删除按钮权限控制
                    deleteButton.addEventListener('click', function(e) {
                        e.preventDefault();
                        e.stopPropagation();
                        showErrorMessage('权限不足，只有管理员可以删除数据');
                        return false;
                    }, true);
                }
                
                if (document.body.classList.contains('dark-mode')) {
                    viewButton.addEventListener('touchstart', function() { this.style.backgroundColor = 'rgba(168, 85, 247, 0.2)'; });
                    viewButton.addEventListener('touchend', function() { this.style.backgroundColor = 'transparent'; });
                    
                    editButton.addEventListener('touchstart', function() { this.style.backgroundColor = 'rgba(52, 211, 153, 0.2)'; });
                    editButton.addEventListener('touchend', function() { this.style.backgroundColor = 'transparent'; });
                    
                    deleteButton.addEventListener('touchstart', function() { this.style.backgroundColor = 'rgba(248, 113, 113, 0.2)'; });
                    deleteButton.addEventListener('touchend', function() { this.style.backgroundColor = 'transparent'; });
                }
                
                cardFooter.appendChild(viewButton);
                cardFooter.appendChild(editButton);
                cardFooter.appendChild(deleteButton);

                // 组装卡片
                card.appendChild(cardHeader);
                card.appendChild(cardBody);
                card.appendChild(cardFooter);
                cardOuterContainer.appendChild(card);
                storageTableBody.appendChild(cardOuterContainer);
            });
        });
    }

    // Helper function:创建操作按钮
    function createActionButton(innerHTML, title, textColor, onClick) {
        const button = document.createElement('button');
        button.className = 'storage-card-action-button';
        button.style.color = textColor;
        button.innerHTML = innerHTML;
        button.title = title;
        button.addEventListener('click', onClick);
        
        button.addEventListener('mouseenter', function() { this.style.backgroundColor = `${textColor}1A`; });
        button.addEventListener('mouseleave', function() { this.style.backgroundColor = 'transparent'; });
        
        button.addEventListener('touchstart', function() { this.style.backgroundColor = `${textColor}33`; });
        button.addEventListener('touchend', function() { this.style.backgroundColor = 'transparent'; });
        
        return button;
    }

    // 查看硬盘详情
    function viewStorageDetails(id) {
        if (!id) {
            showErrorMessage('无效的硬盘ID');
            return;
        }
        
        console.log('[DEBUG] Viewing storage details for ID:', id);
        
        // 查看详情时，显示加载状态
        if (storageDetails) {
            storageDetails.innerHTML = `
                <div class="flex justify-center items-center h-40">
                    <i class="fas fa-spinner fa-spin mr-2"></i> 加载数据中...
                </div>
            `;
        }
        
        if (storageModal) {
            storageModal.classList.remove('hidden');
        }
        
        currentStorageId = id;
        
        // 从已有数据中查找硬盘信息
        const storage = storages.find(s => s.id === id || s.id === parseInt(id));
        if (storage) {
            renderStorageDetails(storage);
            
            // 检查用户权限并处理详情页面的按钮
            isAdmin().then(isAdminUser => {
                if (!isAdminUser) {
                    const modalEditBtn = document.getElementById('editBtn');
                    const modalDeleteBtn = document.getElementById('deleteBtn');
                    
                    if (modalEditBtn) {
                        modalEditBtn.removeEventListener('click', handleEdit);
                        modalEditBtn.addEventListener('click', function(e) {
                            e.preventDefault();
                            e.stopPropagation();
                            showErrorMessage('权限不足，只有管理员可以编辑数据');
                            return false;
                        }, true);
                    }
                    
                    if (modalDeleteBtn) {
                        modalDeleteBtn.removeEventListener('click', () => confirmDeleteStorage(currentStorageId));
                        modalDeleteBtn.addEventListener('click', function(e) {
                            e.preventDefault();
                            e.stopPropagation();
                            showErrorMessage('权限不足，只有管理员可以删除数据');
                            return false;
                        }, true);
                    }
                }
            });
            
            return;
        }
        
        // 如果本地没有缓存，则从API获取
        fetchWithAuth(`/api/storages/${id}`)
            .then(response => {
                if (!response || !response.ok) {
                    throw new Error('API请求失败');
                }
                return response.json();
            })
            .then(storage => {
                if (storage) {
                    renderStorageDetails(storage);
                    
                    // 检查用户权限并处理详情页面的按钮
                    isAdmin().then(isAdminUser => {
                        if (!isAdminUser) {
                            const modalEditBtn = document.getElementById('editBtn');
                            const modalDeleteBtn = document.getElementById('deleteBtn');
                            
                            if (modalEditBtn) {
                                modalEditBtn.removeEventListener('click', handleEdit);
                                modalEditBtn.addEventListener('click', function(e) {
                                    e.preventDefault();
                                    e.stopPropagation();
                                    showErrorMessage('权限不足，只有管理员可以编辑数据');
                                    return false;
                                }, true);
                            }
                            
                            if (modalDeleteBtn) {
                                modalDeleteBtn.removeEventListener('click', () => confirmDeleteStorage(currentStorageId));
                                modalDeleteBtn.addEventListener('click', function(e) {
                                    e.preventDefault();
                                    e.stopPropagation();
                                    showErrorMessage('权限不足，只有管理员可以删除数据');
                                    return false;
                                }, true);
                            }
                        }
                    });
                }
            })
            .catch(error => {
                console.error('[ERROR] 获取硬盘详细信息失败:', error);
                showErrorMessage('获取详情失败: ' + error.message);
            });
    }

    // 渲染硬盘详情
    function renderStorageDetails(storage) {
        if (!storageDetails) {
            // 尝试获取正确的元素
            const detailContent = document.getElementById('storageDetailContent');
            if (!detailContent) {
                console.error('Error: Storage details element not found');
                return;
            }

            // 容量格式化
            let capacityDisplay = '';
            if (storage.capacity) {
                if (storage.capacity >= 1000) {
                    capacityDisplay = `${(storage.capacity / 1000).toFixed(1)} TB`;
                } else {
                    capacityDisplay = `${storage.capacity} GB`;
                }
            } else {
                capacityDisplay = '-';
            }

            let imgHtml = '';
            if (storage.image_url) {
                imgHtml = `
                <div class="w-full md:w-1/3">
                    <div class="bg-gray-50 p-2 rounded-lg shadow-md h-full flex items-center justify-center">
                        <img src="${storage.image_url}" alt="${storage.model}" 
                            class="w-full rounded-lg cursor-pointer hover:opacity-90 detail-preview-img" 
                            data-src="${storage.image_url}"
                            style="max-height: 220px; object-fit: contain;">
                    </div>
                    </div>
                `;
            }
            
            // 之后在页面插入详情HTML后，为图片添加事件监听器
            setTimeout(() => {
                const detailImages = document.querySelectorAll('.detail-preview-img');
                detailImages.forEach(img => {
                    img.addEventListener('click', function() {
                        const src = this.getAttribute('data-src');
                        console.log('Detail image clicked:', src);
                        openImageFullscreen(src);
                    });
                });
            }, 100);

            const details = `
            <!-- 主要信息区：图片和重要信息并排显示 -->
            <div class="flex flex-col md:flex-row gap-6">
                <!-- 左侧硬盘图片 -->
                ${imgHtml ? imgHtml : `
                <div class="md:w-1/3">
                    <div class="bg-gray-100 p-6 rounded-lg h-48 flex items-center justify-center shadow-inner">
                        <div class="text-center text-gray-400">
                            <i class="fas fa-hdd text-5xl mb-2"></i>
                            <p>暂无图片</p>
                        </div>
                        </div>
                    </div>
                `}
                
                <!-- 右侧重要信息 -->
                <div class="md:w-2/3">
                    <!-- 标题和价格区域 -->
                    <div class="border-b pb-3 mb-4">
                        <h2 class="text-xl font-bold text-gray-800 mb-1">${storage.model || '未知型号'}</h2>
                        <div class="flex items-center justify-between">
                            <p class="text-gray-600">${storage.brand || '-'} | ${storage.type || '-'}</p>
                            <p class="text-xl font-bold text-purple-600">${storage.price ? `¥${storage.price}` : '价格未知'}</p>
                </div>
            </div>

                    <!-- 关键信息区域 -->
                    <div class="grid grid-cols-2 gap-4 mb-4">
                        <div class="bg-purple-50 p-3 rounded-lg">
                            <h4 class="font-semibold text-md mb-2 text-purple-700">容量</h4>
                            <p class="text-2xl font-bold text-purple-700">${capacityDisplay}</p>
                            <p class="text-sm text-gray-600">${storage.form_factor || '-'}</p>
                        </div>
                        <div class="bg-blue-50 p-3 rounded-lg">
                            <h4 class="font-semibold text-md mb-2 text-blue-700">性能</h4>
                            <p class="text-2xl font-bold text-blue-700">${storage.read_speed || '-'}<span class="text-sm"> MB/s</span></p>
                            <p class="text-sm text-gray-600">写入: ${storage.write_speed ? `${storage.write_speed} MB/s` : '-'}</p>
                        </div>
                    </div>
                    
                    <!-- 接口信息 -->
                    <div class="grid grid-cols-2 gap-4 mb-4">
                        <div>
                            <h5 class="text-sm font-medium text-gray-700">接口类型</h5>
                            <p class="font-semibold">${storage.interface || '-'}</p>
                        </div>
                        <div>
                            <h5 class="text-sm font-medium text-gray-700">颗粒类型</h5>
                            <p class="font-semibold">${storage.mtbf || '-'}</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 分隔线 -->
            <div class="border-t my-6"></div>
            
            <!-- 详细规格区域 -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <div class="border border-gray-200 rounded-lg p-4 bg-white shadow-sm">
                    <h4 class="font-semibold text-lg mb-2 text-purple-700 border-b border-gray-200 pb-2">基本信息</h4>
                        <div class="space-y-2">
                            <p><span class="font-medium">型号:</span> ${storage.model || '-'}</p>
                            <p><span class="font-medium">品牌:</span> ${storage.brand || '-'}</p>
                            <p><span class="font-medium">类型:</span> ${storage.type || '-'}</p>
                        <p><span class="font-medium">容量:</span> ${capacityDisplay}</p>
                            <p><span class="font-medium">规格:</span> ${storage.form_factor || '-'}</p>
                            <p><span class="font-medium">接口类型:</span> ${storage.interface || '-'}</p>
                        </div>
                    </div>
                <div class="border border-gray-200 rounded-lg p-4 bg-white shadow-sm">
                    <h4 class="font-semibold text-lg mb-2 text-purple-700 border-b border-gray-200 pb-2">性能参数</h4>
                        <div class="space-y-2">
                            <p><span class="font-medium">读取速度:</span> ${storage.read_speed ? `${storage.read_speed} MB/s` : '-'}</p>
                            <p><span class="font-medium">写入速度:</span> ${storage.write_speed ? `${storage.write_speed} MB/s` : '-'}</p>
                        <p><span class="font-medium">缓存:</span> ${storage.cacheSize !== null && storage.cacheSize !== undefined ? `${storage.cacheSize} MB` : '-'}</p>
                            <p><span class="font-medium">总写入量:</span> ${storage.tbw ? `${storage.tbw} TB` : '-'}</p>
                        <p><span class="font-medium">颗粒类型:</span> ${storage.mtbf || '-'}</p>
                        </div>
                    </div>
                </div>
            <div class="border border-gray-200 rounded-lg p-4 bg-white shadow-sm mb-4">
                <h4 class="font-semibold text-lg mb-2 text-purple-700 border-b border-gray-200 pb-2">其他信息</h4>
                    <div class="space-y-2">
                        <p><span class="font-medium">价格:</span> ${storage.price ? `¥${storage.price}` : '-'}</p>
                        <p><span class="font-medium">备注:</span> ${storage.notes || '-'}</p>
                        <p><span class="font-medium">添加时间:</span> ${new Date(storage.created_at).toLocaleString()}</p>
                        <p><span class="font-medium">最后更新:</span> ${new Date(storage.updated_at).toLocaleString()}</p>
                    </div>
                </div>
            `;

            detailContent.innerHTML = details;
        } else {
            // 原来的代码
            // 容量格式化
            let capacityDisplay = '';
            if (storage.capacity) {
                if (storage.capacity >= 1000) {
                    capacityDisplay = `${(storage.capacity / 1000).toFixed(1)} TB`;
                } else {
                    capacityDisplay = `${storage.capacity} GB`;
                }
            } else {
                capacityDisplay = '-';
            }

            let imgHtml = '';
            if (storage.image_url) {
                imgHtml = `
                <div class="w-full md:w-1/3">
                    <div class="bg-gray-50 p-2 rounded-lg shadow-md h-full flex items-center justify-center">
                        <img src="${storage.image_url}" alt="${storage.model}" class="w-full rounded-lg cursor-pointer hover:opacity-90" 
                            onclick="openImageFullscreen('${storage.image_url}')" style="max-height: 220px; object-fit: contain;">
                    </div>
                </div>
            `;
            }

            const details = `
            <!-- 主要信息区：图片和重要信息并排显示 -->
            <div class="flex flex-col md:flex-row gap-6">
                <!-- 左侧硬盘图片 -->
                ${imgHtml ? imgHtml : `
                <div class="md:w-1/3">
                    <div class="bg-gray-100 p-6 rounded-lg h-48 flex items-center justify-center shadow-inner">
                        <div class="text-center text-gray-400">
                            <i class="fas fa-hdd text-5xl mb-2"></i>
                            <p>暂无图片</p>
                        </div>
                        </div>
                    </div>
                `}
                
                <!-- 右侧重要信息 -->
                <div class="md:w-2/3">
                    <!-- 标题和价格区域 -->
                    <div class="border-b pb-3 mb-4">
                        <h2 class="text-xl font-bold text-gray-800 mb-1">${storage.model || '未知型号'}</h2>
                        <div class="flex items-center justify-between">
                            <p class="text-gray-600">${storage.brand || '-'} | ${storage.type || '-'}</p>
                            <p class="text-xl font-bold text-purple-600">${storage.price ? `¥${storage.price}` : '价格未知'}</p>
                </div>
            </div>

                    <!-- 关键信息区域 -->
                    <div class="grid grid-cols-2 gap-4 mb-4">
                        <div class="bg-purple-50 p-3 rounded-lg">
                            <h4 class="font-semibold text-md mb-2 text-purple-700">容量</h4>
                            <p class="text-2xl font-bold text-purple-700">${capacityDisplay}</p>
                            <p class="text-sm text-gray-600">${storage.form_factor || '-'}</p>
                        </div>
                        <div class="bg-blue-50 p-3 rounded-lg">
                            <h4 class="font-semibold text-md mb-2 text-blue-700">性能</h4>
                            <p class="text-2xl font-bold text-blue-700">${storage.read_speed || '-'}<span class="text-sm"> MB/s</span></p>
                            <p class="text-sm text-gray-600">写入: ${storage.write_speed ? `${storage.write_speed} MB/s` : '-'}</p>
                        </div>
                    </div>
                    
                    <!-- 接口信息 -->
                    <div class="grid grid-cols-2 gap-4 mb-4">
                        <div>
                            <h5 class="text-sm font-medium text-gray-700">接口类型</h5>
                            <p class="font-semibold">${storage.interface || '-'}</p>
                        </div>
                        <div>
                            <h5 class="text-sm font-medium text-gray-700">颗粒类型</h5>
                            <p class="font-semibold">${storage.mtbf || '-'}</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 分隔线 -->
            <div class="border-t my-6"></div>
            
            <!-- 详细规格区域 -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <div class="border border-gray-200 rounded-lg p-4 bg-white shadow-sm">
                    <h4 class="font-semibold text-lg mb-2 text-purple-700 border-b border-gray-200 pb-2">基本信息</h4>
                    <div class="space-y-2">
                        <p><span class="font-medium">型号:</span> ${storage.model || '-'}</p>
                        <p><span class="font-medium">品牌:</span> ${storage.brand || '-'}</p>
                        <p><span class="font-medium">类型:</span> ${storage.type || '-'}</p>
                        <p><span class="font-medium">容量:</span> ${capacityDisplay}</p>
                        <p><span class="font-medium">规格:</span> ${storage.form_factor || '-'}</p>
                        <p><span class="font-medium">接口类型:</span> ${storage.interface || '-'}</p>
                    </div>
                </div>
                <div class="border border-gray-200 rounded-lg p-4 bg-white shadow-sm">
                    <h4 class="font-semibold text-lg mb-2 text-purple-700 border-b border-gray-200 pb-2">性能参数</h4>
                    <div class="space-y-2">
                        <p><span class="font-medium">读取速度:</span> ${storage.read_speed ? `${storage.read_speed} MB/s` : '-'}</p>
                        <p><span class="font-medium">写入速度:</span> ${storage.write_speed ? `${storage.write_speed} MB/s` : '-'}</p>
                        <p><span class="font-medium">缓存:</span> ${storage.cacheSize !== null && storage.cacheSize !== undefined ? `${storage.cacheSize} MB` : '-'}</p>
                        <p><span class="font-medium">总写入量:</span> ${storage.tbw ? `${storage.tbw} TB` : '-'}</p>
                        <p><span class="font-medium">颗粒类型:</span> ${storage.mtbf || '-'}</p>
                    </div>
                </div>
            </div>
            <div class="border border-gray-200 rounded-lg p-4 bg-white shadow-sm mb-4">
                <h4 class="font-semibold text-lg mb-2 text-purple-700 border-b border-gray-200 pb-2">其他信息</h4>
                <div class="space-y-2">
                    <p><span class="font-medium">价格:</span> ${storage.price ? `¥${storage.price}` : '-'}</p>
                    <p><span class="font-medium">备注:</span> ${storage.notes || '-'}</p>
                    <p><span class="font-medium">添加时间:</span> ${new Date(storage.created_at).toLocaleString()}</p>
                    <p><span class="font-medium">最后更新:</span> ${new Date(storage.updated_at).toLocaleString()}</p>
                </div>
            </div>
        `;

            storageDetails.innerHTML = details;
        }
    }

    // 编辑硬盘信息
    window.editStorage = function (id) {
        fetchWithAuth(`${API_ENDPOINTS.STORAGE(id)}`)
            .then(response => {
                if (!response || !response.ok) {
                    throw new Error('获取存储设备详情失败');
                }
                return response.json();
            })
            .then(storage => {
                // 填充表单
                currentStorageId = storage.id;
                isEditing = true;

                brand.value = storage.brand || '';
                model.value = storage.model || '';
                type.value = storage.type || '';
                capacity.value = storage.capacity || '';
                formFactor.value = storage.form_factor || '';
                interface.value = storage.interface || '';
                readSpeed.value = storage.read_speed || '';
                writeSpeed.value = storage.write_speed || '';
                cacheSize.value = storage.cacheSize !== null && storage.cacheSize !== undefined ? storage.cacheSize : '';
                tbw.value = storage.tbw || '';
                mtbf.value = storage.mtbf || '';
                price.value = storage.price || '';
                notes.value = storage.notes || '';

                // 更改提交按钮文本
                const submitBtn = storageForm.querySelector('button[type="submit"]');
                if (submitBtn) {
                    submitBtn.innerHTML = '<i class="fas fa-save mr-1 sm:mr-2"></i> 更新存储设备信息';
                }

                // 如果有图片，显示图片预览
                if (storage.image_url) {
                    imagePreview.src = storage.image_url;
                    imagePreviewContainer.classList.remove('hidden');

                    // 添加点击预览功能
                    imagePreview.onclick = () => {
                        openImageFullscreen(storage.image_url);
                    };
                    imagePreview.style.cursor = 'pointer';
                    imagePreview.title = '点击查看大图';
                } else {
                    imagePreviewContainer.classList.add('hidden');
                }

                // 滚动到表单顶部
                storageForm.scrollIntoView({ behavior: 'smooth' });
            })
            .catch(error => {
                showErrorMessage(error.message);
            });
    };

    // 确认删除
    window.confirmDeleteStorage = function (id) {
        currentStorageId = id;
        if (confirm('确定要删除这个存储设备吗？此操作不可撤销。')) {
            deleteStorage(id);
        }
    };

    // 删除硬盘信息
    function deleteStorage(id) {
        fetchWithAuth(`${API_ENDPOINTS.STORAGE(id)}`, {
            method: 'DELETE'
        })
        .then(response => {
            if (!response || !response.ok) {
                // 对于失败的响应，我们假设它有JSON错误信息
                return response.json().then(data => {
                    throw new Error(data.message || '删除失败');
                });
            }
            
            // 对于成功的响应，先检查是否有内容再解析
            const contentType = response.headers.get("content-type");
            if (contentType && contentType.indexOf("application/json") !== -1) {
                return response.json();
            } else {
                // 如果没有JSON内容 (例如 204 No Content)，返回一个空对象
                return {}; 
            }
        })
        .then(data => {
            showSuccessMessage(data.message || '硬盘信息删除成功');

            // 如果正在编辑该记录，重置表单
            if (currentStorageId === id) {
                resetForm();
            }

            // 关闭模态框
            storageModal.classList.add('hidden');

            // 重新加载列表
            loadStorages();
        })
        .catch(error => {
            console.error('Error:', error);
            showErrorMessage(error.message);
        });
    }

    // 处理编辑按钮点击
    function handleEdit() {
        editStorage(currentStorageId);
    }

    // 显示成功消息
    function showSuccessMessage(message) {
        const alertElement = document.createElement('div');
        alertElement.className = 'fixed top-4 right-4 bg-green-50 border-l-4 border-green-500 p-4 opacity-0 transition-opacity duration-300 shadow-md fade-in';
        alertElement.innerHTML = `
            <div class="flex items-center">
                <div class="flex-shrink-0 text-green-500">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="ml-3">
                    <p class="text-sm text-green-700">${message}</p>
                </div>
            </div>
        `;
        document.body.appendChild(alertElement);

        setTimeout(() => alertElement.classList.add('opacity-100'), 10);
        setTimeout(() => {
            alertElement.classList.remove('opacity-100');
            setTimeout(() => document.body.removeChild(alertElement), 300);
        }, 3000);
    }

    // 显示错误消息
    function showErrorMessage(message) {
        const alertElement = document.createElement('div');
        alertElement.className = 'fixed top-4 right-4 bg-red-50 border-l-4 border-red-500 p-4 opacity-0 transition-opacity duration-300 shadow-md fade-in';
        alertElement.innerHTML = `
            <div class="flex items-center">
                <div class="flex-shrink-0 text-red-500">
                    <i class="fas fa-exclamation-circle"></i>
                </div>
                <div class="ml-3">
                    <p class="text-sm text-red-700">${message}</p>
                </div>
            </div>
        `;
        document.body.appendChild(alertElement);

        setTimeout(() => alertElement.classList.add('opacity-100'), 10);
        setTimeout(() => {
            alertElement.classList.remove('opacity-100');
            setTimeout(() => document.body.removeChild(alertElement), 300);
        }, 3000);
    }

    // 防抖函数
    function debounce(func, delay) {
        let timeout;
        return function () {
            const context = this;
            const args = arguments;
            clearTimeout(timeout);
            timeout = setTimeout(() => func.apply(context, args), delay);
        };
    }

    // 修改parseStorageInfo，完全移除AI解析相关代码
    async function parseStorageInfo() {
        let input = document.getElementById('smartInput').value.trim();
        if (!input) return showToast('请输入硬盘参数', 'red');

        document.getElementById('autoFillBtn').disabled = true;
        document.getElementById('autoFillBtn').innerHTML = '<i class="fas fa-sync fa-spin mr-2"></i>正在解析...';

        try {
            console.log(`[Parse Start] Initial Input: "${input}"`);

            // 直接使用正则表达式解析
            console.log('[Parse] Using regex parsing');
            const regexResult = parseWithRegex(input);
            
            if (regexResult) {
                console.log('[Parse] Using regex parsing result:', regexResult);
                fillFormWithData(regexResult);
                document.getElementById('autoFillBtn').disabled = false;
                document.getElementById('autoFillBtn').innerHTML = '<i class="fas fa-magic mr-2"></i>智能识别';
                console.log('[Parse End] Parsing process finished.');
                return;
            }

            // 如果正则解析失败，回退到本地解析
            console.log('[Parse] Regex parsing failed, falling back to local parsing');

            // 本地解析逻辑
        const result = {
            brand: '',
            model: '',
            type: 'SSD',
            capacity: '',
                formFactor: '',
                interface: '',
            readSpeed: '',
            writeSpeed: '',
                cacheSize: '',
                tbw: '',
                mtbf: '',
            price: '',
            notes: ''
        };

            const originalInputForNotes = input;
            let remainingInput = input;
            console.log(`[Parse Detail] Initial remainingInput: "${remainingInput}"`);

            function extractAndRemove(regex, targetField, processFunc) {
                console.log(`[Parse Detail] Attempting to extract '${targetField}' with regex: ${regex}`);
                const match = remainingInput.match(regex);
                if (match && match[1]) {
                    let value = match[1].trim();
                    console.log(`[Parse Detail] Regex Matched for '${targetField}'. Raw matched value: "${match[0]}", Extracted part: "${value}"`);
                    if (processFunc) {
                        const processedValue = processFunc(value, match);
                        console.log(`[Parse Detail] Processed value for '${targetField}': "${processedValue}" (Original: "${value}")`);
                        value = processedValue;
                    }
                    result[targetField] = value;
                    const oldRemainingInput = remainingInput;
                    remainingInput = remainingInput.replace(match[0], '').trim();
                    console.log(`[Parse Detail] Successfully extracted '${targetField}': "${value}". Remaining input: "${remainingInput}" (was: "${oldRemainingInput}")`);
                    return true;
                } else {
                    console.log(`[Parse Detail] No match for '${targetField}' with regex: ${regex} on input: "${remainingInput}"`);
                }
                return false;
            }

            const labeledFields = {
                '硬盘型号': { field: 'model' },
                '品牌': { field: 'brand' },
                '类型': { field: 'type' },
                '容量(?:\\s*\\(GB\\))?': {
                    field: 'capacity',
                    process: (val) => {
                        const numMatch = val.match(/(\d+(?:\.\d+)?)/);
                        if (numMatch) {
                            let numVal = parseFloat(numMatch[1]);
                            if (val.toLowerCase().includes('tb') || val.toLowerCase().includes('t')) return numVal * 1000;
                            return numVal;
                        }
                        return val;
                    }
                },
                '规格': { field: 'formFactor' },
                '接口类型': { field: 'interface' },
                '读取速度(?:\\s*\\(MB\\/s\\))?': { field: 'readSpeed', process: (val) => val.match(/(\d+)/)?.[1] || val },
                '写入速度(?:\\s*\\(MB\\/s\\))?': { field: 'writeSpeed', process: (val) => val.match(/(\d+)/)?.[1] || val },
                '缓存(?:\\s*\\(MB\\))?': { field: 'cacheSize', process: (val) => val.match(/(\d+)/)?.[1] || val },
                '总写入量(?:\\s*\\(TB\\))?': { field: 'tbw', process: (val) => val.match(/(\d+)/)?.[1] || val },
                '颗粒类型': { field: 'mtbf' },
                '价格': { field: 'price' },
                '备注': { field: 'notes' }
            };

            console.log('[Parse Phase] Starting Labeled Field Extraction.');
            for (const labelKey in labeledFields) {
                const { field, process } = labeledFields[labelKey];
                const escapedLabelKey = labelKey.replace(/[.*+?^${}()|[\]\\]/g, '\\\\$&');
                const regex = new RegExp(`${escapedLabelKey}[：:]\\s*([^、，,。]+)`, 'i');
                extractAndRemove(regex, field, process);
            }

            // 最后使用fillFormWithData函数更新UI
            fillFormWithData(result);

            document.getElementById('autoFillBtn').disabled = false;
            document.getElementById('autoFillBtn').innerHTML = '<i class="fas fa-magic mr-2"></i>智能识别';
            console.log('[Parse End] Parsing process finished.');
        } catch (error) {
            console.error('[Parse Error] Error during parsing:', error);
            showToast('解析参数时发生错误: ' + error.message, 'red');
            document.getElementById('autoFillBtn').disabled = false;
            document.getElementById('autoFillBtn').innerHTML = '<i class="fas fa-magic mr-2"></i>智能识别';
            console.log('[Parse End] Parsing process finished.');
        }
    }

    // 添加fillFormWithData函数替代updateUIWithRecognizedData
    function fillFormWithData(data) {
        console.log('[UI Update] Updating UI with recognized data:', data);
        
        // 遍历识别出的数据，更新表单字段
        Object.entries(data).forEach(([field, value]) => {
            if (value && value !== '暂无数据') {
                const element = document.getElementById(field);
                if (element) {
                    // 对于select元素，需要特殊处理
                    if (element.tagName === 'SELECT') {
                        // 尝试找到匹配的选项
                        const options = Array.from(element.options);
                        const option = options.find(opt => 
                            opt.value.toLowerCase() === value.toLowerCase() || 
                            opt.textContent.toLowerCase().includes(value.toLowerCase())
                        );
                        
                        if (option) {
                            element.value = option.value;
                        } else {
                            console.log(`[UI Update] No matching option found for ${field}: ${value}`);
                        }
                    } else {
                        element.value = value;
                    }
                    console.log(`[UI Update] Set field ${field} to ${value}`);
                } else {
                    console.log(`[UI Update] Element not found for field: ${field}`);
                }
            }
        });
        
        showToast('参数识别完成！', '#4CAF50');
    }

    // 使用正则表达式解析硬盘参数
    function parseWithRegex(inputText) {
        try {
            console.log('[Regex Parse] Starting regex parsing with input:', inputText);

            // 预处理输入：替换特定替换字符
            let processedInput = inputText
                .replace(/：/g, ':') // 统一冒号
                .replace(/，/g, '、') // 统一分隔符
                .replace(/,/g, '、');

            // 初始化结果对象
            const result = {
                brand: '',
                model: '',
                type: 'SSD',
                capacity: '',
                formFactor: '',
                interface: '',
                readSpeed: '',
                writeSpeed: '',
                cacheSize: '',
                tbw: '',
                mtbf: '',
                price: '',
                notes: ''
            };

            // 分割键值对（处理备注中的中文顿号）
            const pairs = processedInput.split(/、(?=[^:]+:)/);
            console.log('[Regex Parse] Split pairs:', pairs);

            // 字段映射表 - 使用正则表达式来匹配可能带单位的字段名
            const fieldMappings = [
                { regex: /^品牌$/i, field: 'brand' },
                { regex: /^(硬盘)?型号$/i, field: 'model' },
                { regex: /^类型$/i, field: 'type' },
                { regex: /^容量(\s*\(GB\))?$/i, field: 'capacity' },
                { regex: /^规格$/i, field: 'formFactor' },
                { regex: /^接口(类型)?$/i, field: 'interface' },
                { regex: /^读取(速度)?(\s*\(MB\/s\))?$/i, field: 'readSpeed' },
                { regex: /^写入(速度)?(\s*\(MB\/s\))?$/i, field: 'writeSpeed' },
                { regex: /^缓存(\s*\(MB\))?$/i, field: 'cacheSize' },
                { regex: /^(总)?写入量(\s*\(TB\))?$|^TBW$/i, field: 'tbw' },
                { regex: /^颗粒(类型)?$/i, field: 'mtbf' },
                { regex: /^价格$/i, field: 'price' },
                { regex: /^备注$/i, field: 'notes' }
            ];

            // 解析每个字段
            pairs.forEach(pair => {
                // 对于没有冒号的部分（可能是型号或备注）
                if (!pair.includes(':')) {
                    // 如果很短，可能是型号
                    if (pair.trim().length < 20 && !result.model) {
                        result.model = pair.trim();
                        console.log(`[Regex Parse] Found standalone model: "${result.model}"`);
                    }
                    // 否则可能是一个备注
                    else if (!result.notes) {
                        result.notes = pair.trim();
                        console.log(`[Regex Parse] Found standalone notes: "${result.notes}"`);
                    }
                    return;
                }

                // 处理正常的键值对
                const match = pair.match(/([^:]+):\s*(.*)/);
                if (match) {
                    const key = match[1].trim();
                    const value = match[2].trim();

                    // 使用正则表达式匹配字段
                    let field = null;
                    for (const mapping of fieldMappings) {
                        if (mapping.regex.test(key)) {
                            field = mapping.field;
                            console.log(`[Regex Parse] Field "${key}" matched to "${field}" via regex`);
                            break;
                        }
                    }

                    if (field) {
                        // 处理容量的特殊情况
                        if (field === 'capacity') {
                            // 如果包含TB，转换为GB
                            if (value.toLowerCase().includes('tb') || value.toLowerCase().includes('t')) {
                                const numMatch = value.match(/(\d+(?:\.\d+)?)/);
                                if (numMatch) {
                                    result[field] = String(parseFloat(numMatch[1]) * 1000);
                                    console.log(`[Regex Parse] Converted capacity from TB to GB: ${result[field]}`);
                                } else {
                                    result[field] = value;
                                }
                            }
                            // 如果已经是GB或没有单位
                            else {
                                const numMatch = value.match(/(\d+(?:\.\d+)?)/);
                                if (numMatch) {
                                    result[field] = String(parseFloat(numMatch[1]));
        } else {
                                    result[field] = value;
                                }
                            }
                        }
                        // 处理数值类型字段，提取数字部分
                        else if (['readSpeed', 'writeSpeed', 'cacheSize', 'tbw', 'price'].includes(field)) {
                            const numMatch = value.match(/(\d+(?:\.\d+)?)/);
                            result[field] = numMatch ? String(parseFloat(numMatch[1])) : value;
                        }
                        // 其他字段直接赋值
                        else {
                            result[field] = value;
                        }
                        console.log(`[Regex Parse] Mapped "${key}" to field "${field}" with value: "${result[field]}"`);
        } else {
                        console.log(`[Regex Parse] Unknown field: "${key}" with value: "${value}"`);
                        // 如果是未知字段且备注为空，添加到备注中
                        if (!result.notes) {
                            result.notes = `${key}: ${value}`;
                        } else {
                            result.notes += `; ${key}: ${value}`;
                        }
                    }
                }
            });

            // 特殊情况：如果没有型号但有完整文本的第一行，可能是型号
            if (!result.model && inputText) {
                const firstLine = inputText.split(/[\r\n,，、]/)[0].trim();
                if (firstLine && firstLine.length < 30) {
                    result.model = firstLine;
                    console.log(`[Regex Parse] Using first line as model: "${result.model}"`);
                }
            }

            // 验证至少有一些基本信息被提取
            let filledFields = 0;
            for (const key in result) {
                if (result[key] && result[key].trim() !== '') {
                    filledFields++;
                }
            }

            if (filledFields < 2) {
                console.log('[Regex Parse] Not enough fields were parsed, result may be incomplete');
            }

            console.log('[Regex Parse] Final result:', JSON.parse(JSON.stringify(result)));
            return result;
        } catch (error) {
            console.error('[Regex Parse] Error during regex parsing:', error);
            return null; // 解析失败返回null
        }
    }

    // 更新：增强版Toast，支持长时间显示
    function showToast(message, color = '#4F46E5', autoRemove = true) {
        const toast = document.createElement('div');
        toast.className = 'fixed bottom-4 right-4 px-4 py-2 rounded-md text-white shadow-lg z-50';
        toast.style.backgroundColor = color;
        toast.textContent = message;
        document.body.appendChild(toast);

        if (autoRemove) {
        setTimeout(() => toast.remove(), 3000);
        }

        return toast;
    }

    // 暴露需要全局访问的函数
    window.viewStorageDetails = viewStorageDetails;
    window.editStorage = editStorage;
    window.confirmDeleteStorage = confirmDeleteStorage;
    window.openImageFullscreen = openImageFullscreen;

    // 更新分页控件
    function updatePagination() {
        const totalPages = Math.ceil(totalRecords / pageSize) || 1;
        
        // 更新总记录数显示
        if (totalCount) {
            totalCount.textContent = `共 ${totalRecords} 条记录`;
        }
        
        // 更新当前页码显示
        const currentPageDisplay = document.getElementById('currentPageDisplay');
        if (currentPageDisplay) {
            currentPageDisplay.textContent = currentPage;
        }
        
        // 更新总页数显示
        const totalPagesDisplay = document.getElementById('totalPagesDisplay');
        if (totalPagesDisplay) {
            totalPagesDisplay.textContent = totalPages;
        }
        
        // 更新上一页、下一页按钮状态
        if (prevPage) {
            prevPage.disabled = currentPage <= 1;
            prevPage.classList.toggle('opacity-50', currentPage <= 1);
        }
        
        if (nextPage) {
            nextPage.disabled = currentPage >= totalPages;
            nextPage.classList.toggle('opacity-50', currentPage >= totalPages);
        }
        
        // 更新首页、尾页按钮状态
        const firstPageBtn = document.getElementById('firstPage');
        if (firstPageBtn) {
            firstPageBtn.disabled = currentPage <= 1;
            firstPageBtn.classList.toggle('opacity-50', currentPage <= 1);
        }
        
        const lastPageBtn = document.getElementById('lastPage');
        if (lastPageBtn) {
            lastPageBtn.disabled = currentPage >= totalPages;
            lastPageBtn.classList.toggle('opacity-50', currentPage >= totalPages);
        }
        
        // 生成页码按钮
        const pageNumbers = document.getElementById('pageNumbers');
        if (pageNumbers) {
            pageNumbers.innerHTML = '';
            
            // 确定要显示的页码范围
            let startPage = Math.max(1, currentPage - 2);
            let endPage = Math.min(totalPages, startPage + 4);
            
            // 调整起始页，确保始终显示5个页码（如果有足够多的页）
            if (endPage - startPage < 4 && totalPages > 5) {
                startPage = Math.max(1, endPage - 4);
            }
            
            // 添加页码按钮
            for (let i = startPage; i <= endPage; i++) {
                const pageButton = document.createElement('button');
                pageButton.textContent = i;
                pageButton.className = `px-2 py-1 border rounded-md text-sm ${i === currentPage ? 'bg-purple-600 text-white' : 'bg-white text-gray-700 hover:bg-gray-50'}`;
                pageButton.addEventListener('click', () => changePage(i));
                pageNumbers.appendChild(pageButton);
            }
        }
    }

    // 切换页码
    function changePage(pageNum) {
        if (pageNum < 1 || pageNum > Math.ceil(totalRecords / pageSize) || pageNum === currentPage) {
            return;
        }
        
        currentPage = pageNum;
        loadStorages();
    }

    // 添加图片点击事件处理函数
    function attachImageClickHandlers() {
        console.log('Attaching image click handlers');
        
        // 查找所有表格中的图片
        const tableImages = document.querySelectorAll('.w-10.h-10.object-contain.mx-auto.rounded-md');
        tableImages.forEach(img => {
            img.addEventListener('click', function() {
                console.log('Table image clicked:', this.src);
                openImageFullscreen(this.src);
            });
        });
        
        // 查找所有详情页的图片
        const detailImages = document.querySelectorAll('#storageDetails img, #storageDetailContent img');
        detailImages.forEach(img => {
            img.addEventListener('click', function() {
                console.log('Detail image clicked:', this.src);
                openImageFullscreen(this.src);
            });
        });
    }
    
    // 定期检查并附加点击事件
    setInterval(attachImageClickHandlers, 1000);
});

// 打开全屏图片 - 移至闭包外部成为全局函数
    function openImageFullscreen(src) {
    console.log('Opening image:', src);
    
    // 安全检查：如果没有src或src不是字符串，使用默认图片
    if (!src || typeof src !== 'string') {
        console.error('Invalid image source:', src);
        src = '/images/default-storage.png';
    }
    
    try {
        // 创建一个临时的图片容器
        const container = document.createElement('div');
        container.className = 'viewer-container';
        container.style.display = 'none';
        document.body.appendChild(container);

        // 创建图片元素
        const img = document.createElement('img');
        img.src = src;
        
        // 添加加载失败处理
        img.onerror = function() {
            console.error('Failed to load image:', src);
            this.src = '/images/default-storage.png';
        };
        
        container.appendChild(img);

        // 初始化 Viewer
        const viewer = new Viewer(img, {
            backdrop: true,          // 启用背景遮罩
            button: true,           // 显示关闭按钮
            navbar: false,          // 隐藏底部导航栏（只有一张图片时不需要）
            title: false,           // 不显示标题
            toolbar: {              // 自定义工具栏
                zoomIn: true,       // 放大按钮
                zoomOut: true,      // 缩小按钮
                oneToOne: true,     // 1:1 尺寸按钮
                reset: true,        // 重置按钮
                prev: false,        // 上一张（隐藏，因为只有一张图片）
                play: false,        // 播放按钮（隐藏）
                next: false,        // 下一张（隐藏）
                rotateLeft: true,   // 向左旋转
                rotateRight: true,  // 向右旋转
                flipHorizontal: true, // 水平翻转
                flipVertical: true,  // 垂直翻转
            },
            viewed() {
                // 图片加载完成后自动打开查看器
                if (window.innerWidth < 640) {
                    viewer.zoomTo(0.8);  // 移动设备使用较小的初始缩放比例
                } else {
                    viewer.zoomTo(1);    // 桌面设备使用正常的缩放比例
                }
                console.log('Image viewed successfully');
            },
            hidden() {
                // 查看器关闭后移除临时元素
                console.log('Viewer closed, cleaning up');
                viewer.destroy();
                document.body.removeChild(container);
            },
            maxZoomRatio: 5,        // 最大缩放比例
            minZoomRatio: 0.1,      // 最小缩放比例
            transition: true,       // 启用过渡效果
            keyboard: true,         // 启用键盘支持
        });

        // 显示查看器
        viewer.show();
        console.log('Viewer show() called');
    } catch (error) {
        console.error('Error in openImageFullscreen:', error);
    }
}










// 初始化权限控制
function initPermissions() {
    console.log('初始化权限控制...');
    
    // 如果没有权限控制模块，发出警告
    if (typeof setupPermissionBasedUI !== 'function') {
        console.warn('警告：权限控制模块未加载！请确保已引入permission-helper.js');
        return;
    }
    
    // 执行权限控制初始化
    setupPermissionBasedUI().then(() => {
        // 检查编辑按钮
        const editButtons = document.querySelectorAll('.btn-edit, [data-action="edit"], button[title="编辑"]');
        const deleteButtons = document.querySelectorAll('.btn-delete, [data-action="delete"], button[title="删除"]');
        
        // 根据用户角色处理按钮
        isAdmin().then(isAdminUser => {
            if (!isAdminUser) {
                // 禁用表单
                const form = document.querySelector('form');
                if (form) {
                    const inputs = form.querySelectorAll('input, select, textarea');
                    inputs.forEach(input => {
                        input.setAttribute('disabled', 'disabled');
                    });
                    
                    const submitBtn = form.querySelector('button[type="submit"]');
                    if (submitBtn) {
                        submitBtn.setAttribute('disabled', 'disabled');
                        submitBtn.title = '只有管理员可以添加或修改数据';
                    }
                }
                
                // 隐藏或禁用编辑、删除按钮
                editButtons.forEach(btn => {
                    btn.style.display = 'none';
                });
                
                deleteButtons.forEach(btn => {
                    btn.style.display = 'none';
                });
                
                // 特别处理详情模态框中的按钮
                const handleDetailModalButtons = () => {
                    const modalEditBtn = document.getElementById('editBtn');
                    const modalDeleteBtn = document.getElementById('deleteBtn');
                    
                    if (modalEditBtn) {
                        // 对于模态框中的编辑按钮，保留显示但添加权限检查
                        modalEditBtn.style.display = 'inline-flex';
                        modalEditBtn.addEventListener('click', function(e) {
                            e.preventDefault();
                            e.stopPropagation();
                            showErrorMessage('权限不足，只有管理员可以编辑数据');
                            return false;
                        }, true);
                    }
                    
                    if (modalDeleteBtn) {
                        // 对于模态框中的删除按钮，保留显示但添加权限检查
                        modalDeleteBtn.style.display = 'inline-flex';
                        modalDeleteBtn.addEventListener('click', function(e) {
                            e.preventDefault();
                            e.stopPropagation();
                            showErrorMessage('权限不足，只有管理员可以删除数据');
                            return false;
                        }, true);
                    }
                };
                
                // 初始化时执行一次
                handleDetailModalButtons();
                
                // 每次模态框打开时也执行
                const storageDetailModal = document.getElementById('storageModal');
                if (storageDetailModal) {
                    // 创建一个MutationObserver来监视模态框的显示状态
                    const observer = new MutationObserver(function(mutations) {
                        mutations.forEach(function(mutation) {
                            if (mutation.attributeName === 'class' && 
                                !storageDetailModal.classList.contains('hidden')) {
                                // 模态框显示了，处理按钮
                                handleDetailModalButtons();
                            }
                        });
                    });
                    
                    // 开始观察模态框
                    observer.observe(storageDetailModal, { attributes: true });
                }
                
                // 为所有删除操作添加权限验证拦截器
                interceptDeleteOperations();
            } else {
                // 添加管理员标识
                const header = document.querySelector('h1, h2');
                if (header) {
                    // 检查是否已经添加过徽章
                    if (!header.querySelector('.admin-badge')) {
                        const adminBadge = document.createElement('span');
                        adminBadge.className = 'bg-green-500 text-white text-xs px-2 py-1 rounded ml-2 admin-badge';
                        adminBadge.innerText = '管理员';
                        header.appendChild(adminBadge);
                    }
                }
            }
        });
    });
}

// 拦截所有删除操作的请求
function interceptDeleteOperations() {
    // 保存原始的fetch函数
    const originalFetch = window.fetch;
    
    // 重写fetch函数以拦截删除请求
    window.fetch = async function(url, options) {
        // 检查是否为删除操作
        if (options && options.method === 'DELETE') {
            console.log('拦截到删除操作请求:', url);
            
            // 检查用户权限
            const isAdminUser = await isAdmin();
            if (!isAdminUser) {
                console.warn('权限不足，普通用户无法执行删除操作');
                // 返回权限错误响应
                return Promise.resolve(new Response(JSON.stringify({
                    error: '权限不足',
                    message: '只有管理员可以删除数据'
                }), {
                    status: 403,
                    headers: {
                        'Content-Type': 'application/json'
                    }
                }));
            }
        }
        
        // 对于非删除请求或管理员的删除请求，正常执行
        return originalFetch.apply(this, arguments);
    };
    
    // 添加全局点击事件拦截
    document.addEventListener('click', async function(event) {
        // 查找可能触发删除操作的元素
        const deleteButton = event.target.closest('.btn-delete, [data-action="delete"], button[title="删除"]');
        if (deleteButton) {
            const isAdminUser = await isAdmin();
            if (!isAdminUser) {
                // 阻止事件传播
                event.preventDefault();
                event.stopPropagation();
                console.warn('权限不足，普通用户无法执行删除操作');
                
                // 显示提示消息
                if (typeof showToast === 'function') {
                    showToast('只有管理员可以删除数据', 'red');
                } else if (typeof showErrorMessage === 'function') {
                    showErrorMessage('权限不足，只有管理员可以删除数据');
                } else {
                    console.error('权限不足，只有管理员可以删除数据');
                }
                
                return false;
            }
        }
    }, true);
}

// 页面加载完成后执行权限初始化
document.addEventListener('DOMContentLoaded', initPermissions);
