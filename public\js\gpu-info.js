// 定义一个变量追踪当前是否有活跃的预览
let activeViewer = null;

document.addEventListener('DOMContentLoaded', function() {
    // DOM元素
    const gpuForm = document.getElementById('gpuForm');
    const brand = document.getElementById('brand');
    const model = document.getElementById('model');
    const chipset = document.getElementById('chipset');
    const memorySize = document.getElementById('memorySize');
    const memoryType = document.getElementById('memoryType');
    const memoryBus = document.getElementById('memoryBus');
    const coreClock = document.getElementById('coreClock');
    const boostClock = document.getElementById('boostClock');
    const tdp = document.getElementById('tdp');
    const dimensions = document.getElementById('dimensions');
    const displayPorts = document.getElementById('displayPorts');
    const hdmiPorts = document.getElementById('hdmiPorts');
    const powerConnectors = document.getElementById('powerConnectors');
    const recommendedPsu = document.getElementById('recommendedPsu');
    const price = document.getElementById('price');
    const gpuImage = document.getElementById('gpuImage');
    const imagePreview = document.getElementById('imagePreview');
    const imagePreviewContainer = document.getElementById('imagePreviewContainer');
    const removeImageBtn = document.getElementById('removeImageBtn');
    const notes = document.getElementById('notes');
    const gpuTableBody = document.getElementById('gpuTableBody');
    const gpuSearch = document.getElementById('gpuSearch');
    const brandFilter = document.getElementById('brandFilter');
    const chipsetFilter = document.getElementById('chipsetFilter');
    const resetFilterBtn = document.getElementById('resetFilterBtn');
    const totalCount = document.getElementById('totalCount');
    const prevPage = document.getElementById('prevPage');
    const nextPage = document.getElementById('nextPage');
    const pageInfo = document.getElementById('pageInfo');
    const gpuModal = document.getElementById('gpuModal');
    const closeModal = document.getElementById('closeModal');
    const closeModalBtn = document.getElementById('closeModalBtn');
    const gpuDetails = document.getElementById('gpuDetails');
    const editBtn = document.getElementById('editBtn');
    const deleteBtn = document.getElementById('deleteBtn');
    const smartInput = document.getElementById('smartInput');
    const autoFillBtn = document.getElementById('autoFillBtn');

    // 全局变量
    let currentPage = 1;
    const pageSize = 10;
    let totalRecords = 0;
    let gpus = [];
    let imageFile = null;
    let currentGpuId = null;
    let isEditing = false;
    let isDarkMode = false; // 默认为亮色模式
    let allBrands = []; // 存储所有品牌列表
    let allChipsets = []; // 存储所有芯片组列表
    const maxPageButtons = 5; // 最多显示的页码按钮数

    // 在全局变量下添加API端点常量
    const API_ENDPOINTS = {
        GPUS: '/api/gpus',
        GPU: (id) => `/api/gpus/${id}`,
        BRANDS: '/api/gpus/brands',
        CHIPSETS: '/api/gpus/chipsets'
    };
    
    // 默认图片路径
    const DEFAULT_GPU_IMAGE = '/images/default-gpu.png';

    // 初始化
    init();

    // 初始化函数
    function init() {
        initDarkMode(); // 首先初始化暗夜模式
        setupEventListeners();
        loadBrandFilter(); // 先加载品牌列表
        loadChipsetFilter(); // 加载芯片组列表
        loadGpus(); // 然后加载显卡数据
        initPermissions(); // 初始化权限控制
        
        // 打印初始化信息
        console.log('GPU信息页面初始化完成');
        setTimeout(() => {
            console.log('页面状态:', {
                '搜索框': gpuSearch ? gpuSearch.value : '未找到元素',
                '品牌筛选': brandFilter ? brandFilter.value : '未找到元素',
                '当前页码': currentPage,
                '每页数量': pageSize,
                '暗夜模式': isDarkMode ? '开启' : '关闭'
            });
        }, 1000);
    }

    // 初始化暗夜模式
    function initDarkMode() {
        // 检查本地存储中的暗夜模式设置
        isDarkMode = localStorage.getItem('darkMode') === 'true'; // 只有当明确设置为true时才使用暗夜模式
        if (isDarkMode) {
            document.body.classList.add('dark-mode');
            updateDarkModeIcon(true);
        } else {
            document.body.classList.remove('dark-mode');
            updateDarkModeIcon(false);
        }
    }

    // 切换暗夜模式
    function toggleDarkMode() {
        isDarkMode = !isDarkMode;
        localStorage.setItem('darkMode', isDarkMode);
        
        if (isDarkMode) {
            document.body.classList.add('dark-mode');
        } else {
            document.body.classList.remove('dark-mode');
        }
        
        updateDarkModeIcon(isDarkMode);
        
        // 在暗夜模式切换后刷新数据显示，以应用新的样式
        if (window.innerWidth < 640) {
            loadGpus();  // 重新加载数据，更新移动端卡片样式
        }
    }

    // 更新暗夜模式图标
    function updateDarkModeIcon(isDark) {
        const darkModeToggle = document.getElementById('darkModeToggle');
        if (!darkModeToggle) return;
        
        if (isDark) {
            darkModeToggle.innerHTML = '<i class="fas fa-sun"></i>';
            darkModeToggle.classList.remove('bg-gray-200', 'hover:bg-gray-300', 'text-gray-700');
            darkModeToggle.classList.add('bg-yellow-400', 'hover:bg-yellow-500', 'text-yellow-900');
            darkModeToggle.title = '切换至亮色模式';
            darkModeToggle.style.boxShadow = '0 0 10px rgba(251, 191, 36, 0.5)';
        } else {
            darkModeToggle.innerHTML = '<i class="fas fa-moon"></i>';
            darkModeToggle.classList.remove('bg-yellow-400', 'hover:bg-yellow-500', 'text-yellow-900');
            darkModeToggle.classList.add('bg-gray-200', 'hover:bg-gray-300', 'text-gray-700');
            darkModeToggle.title = '切换至暗夜模式';
            darkModeToggle.style.boxShadow = 'none';
        }
    }

    // 加载品牌过滤器
    function loadBrandFilter() {
        if (!brandFilter) {
            console.error('品牌筛选元素不存在');
            return;
        }
        
        console.log('开始加载品牌列表...');
        
        // 初始请求，保留"所有品牌"的选项
        const firstOption = document.createElement('option');
        firstOption.value = 'all';
        firstOption.textContent = '所有品牌';
        brandFilter.innerHTML = '';
        brandFilter.appendChild(firstOption);
        
        // 从API获取品牌列表
        fetchWithAuth(API_ENDPOINTS.BRANDS)
            .then(response => {
                if (!response || !response.ok) {
                    throw new Error('获取品牌列表失败');
                }
                return response.json();
            })
            .then(data => {
                console.log('获取到品牌列表:', data);
                
                // 保存品牌列表到全局变量
                allBrands = data.brands || [];
                
                // 按字母顺序排序
                allBrands.sort((a, b) => a.localeCompare(b, 'zh-CN'));
                
                // 添加到下拉框
                allBrands.forEach(brandName => {
                    const option = document.createElement('option');
                    option.value = brandName;
                    option.textContent = brandName;
                    brandFilter.appendChild(option);
                });
                
                console.log('品牌列表加载完成，共', allBrands.length, '个品牌');
            })
            .catch(error => {
                console.error('获取品牌列表失败:', error);
                // 添加一些常见的品牌作为后备
                const defaultBrands = ['华硕', '技嘉', '微星', '索泰', '七彩虹', '影驰', '映众', '蓝宝石', '迪兰恒进', '耕升', '讯景', '其他'];
                defaultBrands.forEach(brandName => {
                    const option = document.createElement('option');
                    option.value = brandName;
                    option.textContent = brandName;
                    brandFilter.appendChild(option);
                });
                
                console.log('使用默认品牌列表作为后备');
            });
    }

    // 加载芯片组过滤器
    function loadChipsetFilter() {
        if (!chipsetFilter) {
            console.error('芯片组筛选元素不存在');
            return;
        }

        console.log('开始加载芯片组列表...');

        // 初始请求，保留"所有芯片组"的选项
        const firstOption = document.createElement('option');
        firstOption.value = 'all';
        firstOption.textContent = '所有芯片组';
        chipsetFilter.innerHTML = '';
        chipsetFilter.appendChild(firstOption);

        // 从API获取芯片组列表
        fetchWithAuth(API_ENDPOINTS.CHIPSETS)
            .then(response => {
                if (!response || !response.ok) {
                    throw new Error('获取芯片组列表失败');
                }
                return response.json();
            })
            .then(data => {
                console.log('获取到芯片组列表:', data);

                // 保存芯片组列表到全局变量
                allChipsets = data.chipsets || [];

                // 按字母顺序排序
                allChipsets.sort((a, b) => a.localeCompare(b, 'zh-CN'));

                // 添加到下拉框
                allChipsets.forEach(chipsetName => {
                    const option = document.createElement('option');
                    option.value = chipsetName;
                    // 在移动端截断过长的芯片组名称
                    const displayName = window.innerWidth <= 640 && chipsetName.length > 15
                        ? chipsetName.substring(0, 12) + '...'
                        : chipsetName;
                    option.textContent = displayName;
                    option.title = chipsetName; // 完整名称作为tooltip
                    chipsetFilter.appendChild(option);
                });

                console.log('芯片组列表加载完成，共', allChipsets.length, '个芯片组');

                // 格式化选项显示
                formatChipsetOptions();
            })
            .catch(error => {
                console.error('获取芯片组列表失败:', error);
                // 添加一些常见的芯片组作为后备
                const defaultChipsets = ['RTX 4090', 'RTX 4080', 'RTX 4070 Ti', 'RTX 4070', 'RTX 4060 Ti', 'RTX 4060', 'RTX 3090', 'RTX 3080', 'RTX 3070', 'RTX 3060 Ti', 'RTX 3060', 'RX 7900 XTX', 'RX 7900 XT', 'RX 7800 XT', 'RX 7700 XT', 'RX 7600', '其他'];
                defaultChipsets.forEach(chipsetName => {
                    const option = document.createElement('option');
                    option.value = chipsetName;
                    // 在移动端截断过长的芯片组名称
                    const displayName = window.innerWidth <= 640 && chipsetName.length > 15
                        ? chipsetName.substring(0, 12) + '...'
                        : chipsetName;
                    option.textContent = displayName;
                    option.title = chipsetName; // 完整名称作为tooltip
                    chipsetFilter.appendChild(option);
                });

                console.log('使用默认芯片组列表作为后备');

                // 格式化选项显示
                formatChipsetOptions();
            });
    }

    // 格式化芯片组选项显示文本
    function formatChipsetOptions() {
        if (!chipsetFilter) return;

        const isMobile = window.innerWidth <= 640;
        const options = chipsetFilter.querySelectorAll('option');

        options.forEach(option => {
            if (option.value === 'all') return; // 跳过"所有芯片组"选项

            const fullName = option.title || option.value;
            const displayName = isMobile && fullName.length > 15
                ? fullName.substring(0, 12) + '...'
                : fullName;

            option.textContent = displayName;
            option.title = fullName;
        });
    }

    // 设置事件监听器
    function setupEventListeners() {
        // 暗夜模式切换按钮
        const darkModeToggle = document.getElementById('darkModeToggle');
        if (darkModeToggle) {
            darkModeToggle.addEventListener('click', toggleDarkMode);
        }
        
        // 智能解析按钮
        if (autoFillBtn) {
            autoFillBtn.addEventListener('click', parseGpuInfo);
        }
        
        // 图片上传
        if (gpuImage) {
            gpuImage.addEventListener('change', handleImageUpload);
        }
        
        if (removeImageBtn) {
            removeImageBtn.addEventListener('click', removeImage);
        }

        // 表单提交
        if (gpuForm) {
            gpuForm.addEventListener('submit', handleFormSubmit);
        }

        // 搜索功能
        if (gpuSearch) {
            // 使用input事件实时搜索（带防抖动）
            gpuSearch.addEventListener('input', debounce(() => {
                console.log('搜索框输入:', gpuSearch.value);
                currentPage = 1;
                loadGpus();
            }, 500)); // 增加防抖时间到500ms
            
            // 添加回车键搜索功能
            gpuSearch.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    console.log('搜索框回车:', gpuSearch.value);
                    currentPage = 1;
                    loadGpus();
                }
            });
        }
        
        // 品牌筛选功能
        if (brandFilter) {
            brandFilter.addEventListener('change', () => {
                console.log('品牌筛选变化:', brandFilter.value);
                currentPage = 1;
                loadGpus();
                // 当用户选择了品牌筛选时，保持焦点在搜索框中
                if (gpuSearch) {
                    gpuSearch.focus();
                }
            });

            // 确保品牌筛选器中的选项正常显示
            brandFilter.addEventListener('click', () => {
                console.log('点击了品牌筛选器，当前值:', brandFilter.value);
                console.log('当前选项数量:', brandFilter.options.length);

                // 如果没有选项，尝试重新加载
                if (brandFilter.options.length <= 1) {
                    console.log('正在重新加载品牌列表...');
                    loadBrandFilter();
                }
            });
        }

        // 芯片组筛选功能
        if (chipsetFilter) {
            chipsetFilter.addEventListener('change', () => {
                console.log('芯片组筛选变化:', chipsetFilter.value);
                currentPage = 1;
                loadGpus();
                // 当用户选择了芯片组筛选时，保持焦点在搜索框中
                if (gpuSearch) {
                    gpuSearch.focus();
                }
            });

            // 确保芯片组筛选器中的选项正常显示
            chipsetFilter.addEventListener('click', () => {
                console.log('点击了芯片组筛选器，当前值:', chipsetFilter.value);
                console.log('当前选项数量:', chipsetFilter.options.length);

                // 如果没有选项，尝试重新加载
                if (chipsetFilter.options.length <= 1) {
                    console.log('正在重新加载芯片组列表...');
                    loadChipsetFilter();
                }
            });
        }

        // 重置按钮
        if (resetFilterBtn) {
            resetFilterBtn.addEventListener('click', () => {
                console.log('点击了重置按钮');
                // 重置所有筛选条件
                if (gpuSearch) gpuSearch.value = '';
                if (brandFilter) brandFilter.value = 'all';
                if (chipsetFilter) chipsetFilter.value = 'all';
                currentPage = 1;
                loadGpus();

                // 重置后将焦点放在搜索框上
                if (gpuSearch) {
                    gpuSearch.focus();
                }

                // 显示提示消息
                showSuccessMessage('已重置所有筛选条件');
            });
        }

        // 分页
        if (prevPage) {
            prevPage.addEventListener('click', () => changePage(currentPage - 1));
        }
        
        if (nextPage) {
            nextPage.addEventListener('click', () => changePage(currentPage + 1));
        }

        // 首页和尾页
        const firstPageBtn = document.getElementById('firstPage');
        if (firstPageBtn) {
            firstPageBtn.addEventListener('click', () => changePage(1));
        }

        const lastPageBtn = document.getElementById('lastPage');
        if (lastPageBtn) {
            lastPageBtn.addEventListener('click', () => {
                const totalPages = Math.ceil(totalRecords / pageSize) || 1;
                changePage(totalPages);
            });
        }

        // 页码跳转
        const goToPageBtn = document.getElementById('goToPage');
        if (goToPageBtn) {
            goToPageBtn.addEventListener('click', () => {
                const pageJumpInput = document.getElementById('pageJump');
                if (pageJumpInput) {
                    let pageNum = parseInt(pageJumpInput.value);
                    const totalPages = Math.ceil(totalRecords / pageSize) || 1;
                    
                    if (isNaN(pageNum) || pageNum < 1) {
                        pageNum = 1;
                    } else if (pageNum > totalPages) {
                        pageNum = totalPages;
                    }
                    
                    changePage(pageNum);
                    pageJumpInput.value = '';
                }
            });
        }

        // 监听页码输入框的回车事件
        const pageJumpInput = document.getElementById('pageJump');
        if (pageJumpInput) {
            pageJumpInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    const goToPageBtn = document.getElementById('goToPage');
                    if (goToPageBtn) {
                        goToPageBtn.click();
                    }
                }
            });
        }

        // 模态框
        if (closeModal) {
            closeModal.addEventListener('click', () => {
                if (gpuModal) {
                    gpuModal.classList.add('hidden');
                } else {
                    const detailModal = document.getElementById('gpuDetailModal');
                    if (detailModal) {
                        detailModal.classList.add('hidden');
                    }
                }
            });
        }
        
        if (closeModalBtn) {
            closeModalBtn.addEventListener('click', () => {
                if (gpuModal) {
                    gpuModal.classList.add('hidden');
                } else {
                    const detailModal = document.getElementById('gpuDetailModal');
                    if (detailModal) {
                        detailModal.classList.add('hidden');
                    }
                }
            });
        }
        
        if (editBtn) {
            editBtn.addEventListener('click', handleEdit);
        }
        
        if (deleteBtn) {
            deleteBtn.addEventListener('click', () => {
                if (currentGpuId) {
                    confirmDeleteGpu(currentGpuId);
                }
            });
        }

        // 为按钮添加事件监听
        document.querySelectorAll('.details-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const id = this.getAttribute('data-id');
                viewGpuDetails(id);
            });
        });
        
        document.querySelectorAll('.edit-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const id = this.getAttribute('data-id');
                editGpu(id);
            });
        });
        
        document.querySelectorAll('.delete-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const id = this.getAttribute('data-id');
                confirmDeleteGpu(id);
            });
        });
        
        // 添加全局事件委托，处理所有图片预览点击
        let isOpeningImage = false; // 添加标志防止重复触发
        
        document.addEventListener('click', function(e) {
            // 防止短时间内重复触发
            if (isOpeningImage) return;
            
            // 检查是否点击了带有js-preview-image类的元素
            if (e.target.classList.contains('js-preview-image')) {
                e.preventDefault();
                e.stopPropagation();
                
                isOpeningImage = true; // 设置标志
                const src = e.target.getAttribute('data-src') || e.target.getAttribute('src');
                console.log('全局图片点击事件委托，src:', src);
                
                if (src) {
                    openImageFullscreen(src);
                    // 1秒后重置标志，防止连续快速点击
                    setTimeout(() => {
                        isOpeningImage = false;
                    }, 1000);
                } else {
                    isOpeningImage = false; // 如果没有src，立即重置标志
                }
            }
            // 检查是否点击了图片覆盖层或其子元素
            else if (e.target.closest('.img-overlay') || e.target.classList.contains('fa-search-plus')) {
                e.preventDefault();
                e.stopPropagation();
                
                if (isOpeningImage) return;
                isOpeningImage = true; // 设置标志
                
                // 找到最近的图片容器
                const container = e.target.closest('.gpu-image-container');
                if (container) {
                    const img = container.querySelector('.js-preview-image');
                    if (img) {
                        const src = img.getAttribute('data-src') || img.getAttribute('src');
                        console.log('覆盖层点击事件，找到图片src:', src);
                        if (src) {
                            openImageFullscreen(src);
                            // 1秒后重置标志，防止连续快速点击
                            setTimeout(() => {
                                isOpeningImage = false;
                            }, 1000);
                        } else {
                            isOpeningImage = false; // 如果没有src，立即重置标志
                        }
                    } else {
                        isOpeningImage = false; // 如果没有找到图片，立即重置标志
                    }
                } else {
                    isOpeningImage = false; // 如果没有找到容器，立即重置标志
                }
            } else {
                // 点击了其他元素，不触发图片预览，无需设置标志
            }
        });
    }

    // 智能解析显卡信息
    function parseGpuInfo() {
        console.log('开始智能解析显卡参数');
        const smartInput = document.getElementById('smartInput');
        if (!smartInput || !smartInput.value.trim()) {
            showErrorMessage('请输入显卡参数文本');
            return;
        }
        
        const inputText = smartInput.value.trim();
        console.log('原始文本: ', inputText);
        
        // 使用正则表达式解析显卡参数
        const results = parseWithRegex(inputText);
        console.log('解析结果:', results);
        
        // 填充表单
        if (results.model) {
            const modelField = document.getElementById('model');
            if (modelField) modelField.value = results.model;
        }
        
        if (results.brand) {
            const brandField = document.getElementById('brand');
            if (brandField) brandField.value = results.brand;
        }
        
        if (results.chipset) {
            const chipsetField = document.getElementById('chipset');
            if (chipsetField) chipsetField.value = results.chipset;
        }
        
        if (results.memorySize) {
            const memorySizeField = document.getElementById('memorySize');
            if (memorySizeField) memorySizeField.value = results.memorySize;
        }
        
        if (results.memoryType) {
            const memoryTypeField = document.getElementById('memoryType');
            if (memoryTypeField) memoryTypeField.value = results.memoryType;
        }
        
        if (results.memoryBus) {
            const memoryBusField = document.getElementById('memoryBus');
            if (memoryBusField) memoryBusField.value = results.memoryBus;
        }
        
        if (results.coreClock) {
            const coreClockField = document.getElementById('coreClock');
            if (coreClockField) coreClockField.value = results.coreClock;
        }
        
        if (results.boostClock) {
            const boostClockField = document.getElementById('boostClock');
            if (boostClockField) boostClockField.value = results.boostClock;
        }
        
        if (results.tdp) {
            const tdpField = document.getElementById('tdp');
            if (tdpField) tdpField.value = results.tdp;
        }
        
        if (results.dimensions) {
            const dimensionsField = document.getElementById('dimensions');
            if (dimensionsField) dimensionsField.value = results.dimensions;
        }
        
        if (results.powerConnectors) {
            const powerConnectorsField = document.getElementById('powerConnectors');
            if (powerConnectorsField) powerConnectorsField.value = results.powerConnectors;
        }
        
        if (results.recommendedPsu) {
            const recommendedPsuField = document.getElementById('recommendedPsu');
            if (recommendedPsuField) recommendedPsuField.value = results.recommendedPsu;
        }
        
        if (results.displayPorts) {
            const displayPortsField = document.getElementById('displayPorts');
            if (displayPortsField) displayPortsField.value = results.displayPorts;
        }
        
        if (results.hdmiPorts) {
            const hdmiPortsField = document.getElementById('hdmiPorts');
            if (hdmiPortsField) hdmiPortsField.value = results.hdmiPorts;
        }
        
        if (results.price) {
            const priceField = document.getElementById('price');
            if (priceField) priceField.value = results.price;
        }
        
        if (results.notes) {
            const notesField = document.getElementById('notes');
            if (notesField) notesField.value = results.notes;
        }
        
        showSuccessMessage('显卡参数解析完成');
    }
    
    // 使用正则表达式解析显卡参数
    function parseWithRegex(inputText) {
        console.log('开始正则解析:', inputText);
        const results = {};
        
        // 规范化输入文本（将中文符号转为英文符号）
        const normalizedText = inputText
            .replace(/：/g, ':')
            .replace(/，/g, ',')
            .replace(/\s+/g, ' ');
        
        // 拆分为字段对
        const pairs = normalizedText.split(/、|,/);
        console.log('拆分后的字段对:', pairs);
        
        // 定义字段匹配模式
        const fieldPatterns = [
            { name: 'model', regex: /型号\s*:?\s*(.+)/i },
            { name: 'brand', regex: /品牌\s*:?\s*(.+)/i },
            { name: 'chipset', regex: /芯片组\s*:?\s*(.+)/i },
            { name: 'memorySize', regex: /显存大小\s*(?:\(GB\))?\s*:?\s*(\d+)/i },
            { name: 'memoryType', regex: /显存类型\s*:?\s*(.+)/i },
            { name: 'memoryBus', regex: /位宽\s*:?\s*(\d+)/i },
            { name: 'coreClock', regex: /(?:核心频率|基础频率)\s*(?:\(MHz\))?\s*:?\s*(\d+)/i },
            { name: 'boostClock', regex: /(?:加速频率|Boost)\s*(?:\(MHz\))?\s*:?\s*(\d+)/i },
            { name: 'tdp', regex: /(?:功耗|TDP)\s*(?:\(W\))?\s*:?\s*(\d+)/i },
            { name: 'dimensions', regex: /尺寸(?:\s*\(.*?\))?\s*:?\s*([\d\s×x*]+)/i },
            { name: 'powerConnectors', regex: /电源接口\s*:?\s*(.+)/i },
            { name: 'recommendedPsu', regex: /推荐电源\s*(?:\(W\))?\s*:?\s*([^、,]+)/i },
            { name: 'displayPorts', regex: /DisplayPort数量\s*:?\s*(\d+)/i },
            { name: 'hdmiPorts', regex: /HDMI接口数量\s*:?\s*(\d+)/i },
            { name: 'price', regex: /价格\s*:?\s*(\d+(?:\.\d+)?)/i },
            { name: 'notes', regex: /备注\s*:?\s*(.+)/i }
        ];

        // 用于收集备注的数组，可能需要合并多个部分
        let notesParts = [];
        let foundNotesField = false; // 标记是否找到了"备注:"字段

        // 尝试匹配每个字段
        for (const pair of pairs) {
            let matched = false;
            
            // 特别处理备注字段
            const notesMatch = pair.match(/备注\s*:?\s*(.+)/i);
            if (notesMatch && notesMatch[1]) {
                foundNotesField = true;
                notesParts.push(notesMatch[1].trim());
                matched = true;
                continue;
            }
            
            // 如果已经找到备注字段，而当前片段没有冒号（不是其他字段），则视为备注的延续
            if (foundNotesField && !/:/.test(pair)) {
                notesParts.push(pair.trim());
                matched = true;
                continue;
            }
            
            // 处理其他常规字段
            for (const pattern of fieldPatterns) {
                // 已经找到备注字段，跳过备注模式
                if (foundNotesField && pattern.name === 'notes') continue;
                
                const match = pair.match(pattern.regex);
                if (match && match[1]) {
                    let value = match[1].trim();
                    
                    // 对特定字段进行清理
                    if (pattern.name === 'brand' && value.includes('(')) {
                        // 提取品牌名，例如 "微星 (MSI)" -> "微星"
                        value = value.split('(')[0].trim();
                    }
                    
                    // 清理价格中的非数字字符
                    if (pattern.name === 'price') {
                        const priceMatch = value.match(/\d+(?:\.\d+)?/);
                        if (priceMatch) {
                            value = priceMatch[0];
                        }
                    }
                    
                    // 清理尺寸字段中可能的单位或额外文本
                    if (pattern.name === 'dimensions') {
                        // 只保留数字和分隔符（×、x、*）
                        value = value.replace(/[^0-9×x*\s]/g, '').trim();
                    }
                    
                    // 处理推荐电源的范围
                    if (pattern.name === 'recommendedPsu' && value.includes('-')) {
                        const range = value.split('-');
                        if (range.length === 2) {
                            // 使用范围的上限
                            const upperValue = parseInt(range[1].trim());
                            if (!isNaN(upperValue)) {
                                value = upperValue.toString();
                            }
                        }
                    }
                    
                    results[pattern.name] = value;
                    console.log(`提取到 ${pattern.name}:`, value);
                    matched = true;
                    break;
                }
            }
            
            // 如果没有匹配任何预定义模式，并且文本看起来像描述，可能是无标签的信息
            if (!matched && pair.trim().length > 0) {
                // 如果包含冒号但不匹配任何已知字段，则有可能是不规则的字段
                if (/:/.test(pair)) {
                    const customMatch = pair.match(/(.+?):\s*(.+)/);
                    if (customMatch && customMatch[1] && customMatch[2]) {
                        const fieldName = customMatch[1].trim().toLowerCase();
                        const value = customMatch[2].trim();
                        
                        // 检查是否是可能的note字段
                        if (fieldName.includes('备注') || fieldName.includes('note') || 
                            fieldName.includes('说明') || fieldName.includes('描述')) {
                            notesParts.push(value);
                            foundNotesField = true;
                        } else {
                            // 可以选择性地存储这些自定义字段
                            console.log(`发现自定义字段 ${fieldName}:`, value);
                        }
                    }
                } else if (foundNotesField) {
                    // 如果已经识别到备注字段，则无冒号的文本段落很可能是备注延续
                    notesParts.push(pair.trim());
                }
            }
        }
        
        // 合并备注部分
        if (notesParts.length > 0) {
            results.notes = notesParts.join('，');
            console.log('合并备注:', results.notes);
        }
        
        // 尝试从文本中提取GPU型号 (RTX 30xx/40xx)
        if (!results.chipset) {
            const gpuModelMatch = inputText.match(/(RTX|GTX|RX)\s*\d{4}(?:\s*(?:Ti|SUPER|XT))?/i);
            if (gpuModelMatch) {
                results.chipset = gpuModelMatch[0];
                console.log('从文本中提取芯片组:', results.chipset);
            }
        }
        
        return results;
    }

    // 处理图片上传
    function handleImageUpload(e) {
        const file = e.target.files[0];
        if (!file) return;

        // 验证文件类型
        const validTypes = ['image/jpeg', 'image/png', 'image/gif'];
        if (!validTypes.includes(file.type)) {
            alert('请上传JPG、PNG或GIF格式的图片');
            return;
        }

        // 验证文件大小
        if (file.size > 5 * 1024 * 1024) { // 5MB
            alert('图片大小不能超过5MB');
            return;
        }

        imageFile = file;
        const reader = new FileReader();
        reader.onload = function(event) {
            if (imagePreview) {
                imagePreview.src = event.target.result;
                if (imagePreviewContainer) {
                    imagePreviewContainer.classList.remove('hidden');
                }

                // 添加点击预览功能
                imagePreview.onclick = () => {
                    openImageFullscreen(event.target.result);
                };
                imagePreview.style.cursor = 'pointer';
                imagePreview.title = '点击查看大图';

                // 添加WebP转换提示
                if (supportWebP) {
                    const fileSize = (file.size / 1024).toFixed(1);
                    const estimatedWebPSize = (file.size * 0.7 / 1024).toFixed(1); // 估计WebP大约为原图70%大小
                    console.log(`[图片优化] 文件大小: ${fileSize}KB, 转换后预估: ${estimatedWebPSize}KB (WebP格式)`);
                    
                    // 显示转换提示
                    const uploadHint = document.createElement('p');
                    uploadHint.className = 'text-xs text-green-600 mt-1 upload-notification';
                    uploadHint.innerHTML = `<i class="fas fa-info-circle mr-1"></i>图片将在上传时自动转换为WebP格式 (预计可减小约${Math.round((1 - 0.7) * 100)}%)`;
                    
                    // 如果已有提示则替换，否则添加
                    const existingHint = imagePreviewContainer.querySelector('.upload-notification');
                    if (existingHint) {
                        existingHint.replaceWith(uploadHint);
                    } else {
                        imagePreviewContainer.appendChild(uploadHint);
                    }
                }
            }
        };
        reader.readAsDataURL(file);
    }

    // 移除图片
    function removeImage() {
        if (gpuImage) gpuImage.value = '';
        if (imagePreview) imagePreview.src = '';
        if (imagePreviewContainer) imagePreviewContainer.classList.add('hidden');
        imageFile = null;
    }

    // 处理表单提交
    function handleFormSubmit(e) {
        if (e) e.preventDefault();
        
        console.log('FormSubmit - 开始处理表单提交');
        
        if (!model.value.trim()) {
            showErrorMessage('请输入显卡型号');
            model.focus();
            return;
        }

        if (!brand.value.trim()) {
            showErrorMessage('请输入显卡品牌');
            brand.focus();
            return;
        }
        
        // 创建FormData对象
        const formData = new FormData();
        
        // 添加表单字段
        formData.append('model', model.value.trim());
        formData.append('brand', brand.value.trim());
        formData.append('chipset', chipset.value.trim());
        formData.append('memory_size', memorySize.value);
        formData.append('memory_type', memoryType.value);
        formData.append('memory_bus', memoryBus.value);
        formData.append('core_clock', coreClock.value);
        formData.append('boost_clock', boostClock.value);
        formData.append('tdp', tdp.value);
        formData.append('dimensions', dimensions.value.trim());
        formData.append('power_connectors', powerConnectors.value.trim());
        formData.append('recommended_psu', recommendedPsu.value);
        formData.append('display_ports', displayPorts.value);
        formData.append('hdmi_ports', hdmiPorts.value);
        formData.append('price', price.value);
        formData.append('notes', notes.value.trim());

        // 添加图片（如果有）
        if (imageFile) {
            formData.append('image', imageFile);
            // 添加调试信息
            console.log('[DEBUG] 正在上传显卡图片:', imageFile.name, '类型:', imageFile.type, '大小:', (imageFile.size / 1024).toFixed(1) + 'KB', '(将自动转换为WebP格式以优化加载速度)');
            
            // 更新上传提示
            const uploadHint = document.querySelector('.upload-notification');
            if (uploadHint) {
                uploadHint.innerHTML = '<i class="fas fa-cloud-upload-alt mr-1"></i>图片正在上传并转换为WebP格式...';
            }
        } else {
            // 如果用户未上传图片，告知后端使用默认图片
            console.log('[DEBUG] 用户未上传图片，将使用默认图片');
            formData.append('use_default_image', 'true');
        }
        
        // 确定提交方式（新增or编辑）
        const url = isEditing ? `/api/gpus/${currentGpuId}` : '/api/gpus';
        const method = isEditing ? 'PUT' : 'POST';
        
        console.log(`FormSubmit - 准备 ${isEditing ? '更新' : '添加'} 显卡数据`, {
            url,
            method,
            isEditing,
            id: currentGpuId
        });
        
        // 显示进度条并禁用提交按钮
        showUploadProgress();
        disableSubmitButton(true, isEditing);

        // 使用XMLHttpRequest来支持上传进度
        uploadWithProgress(url, method, formData)
        .then(data => {
            console.log('FormSubmit - 提交成功:', data);
            showSuccessMessage(data.message || (isEditing ? '显卡信息更新成功' : '显卡添加成功'));
            resetForm();
            loadGpus();
        })
        .catch(error => {
            console.error('FormSubmit - 错误:', error);
            showErrorMessage(error.message || '表单提交失败');
        })
        .finally(() => {
            // 隐藏进度条并恢复提交按钮
            hideUploadProgress();
            disableSubmitButton(false, isEditing);
        });
    }

    // 重置表单
    function resetForm() {
        if (!gpuForm) return;
        
        gpuForm.reset();
        removeImage();
        isEditing = false;
        currentGpuId = null;
        // 更改提交按钮文本
        const submitBtn = gpuForm.querySelector('button[type="submit"]');
        if (submitBtn) {
            submitBtn.innerHTML = '<i class="fas fa-save mr-1 sm:mr-2"></i> 保存显卡信息';
        }
    }

    // 加载显卡列表
    function loadGpus() {
        if (!gpuTableBody) return;

        const searchTerm = gpuSearch ? gpuSearch.value.trim() : '';
        const selectedBrand = brandFilter ? brandFilter.value : 'all';
        const selectedChipset = chipsetFilter ? chipsetFilter.value : 'all';

        console.log('请求参数:', {
            searchTerm,
            selectedBrand,
            selectedChipset,
            currentPage,
            pageSize
        });
        
        // 显示加载状态
        gpuTableBody.innerHTML = `
            <tr>
                <td colspan="6" class="px-3 py-4 text-center">
                    <div class="flex items-center justify-center">
                        <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-red-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <span>加载中...</span>
                    </div>
                </td>
            </tr>
        `;
        
        // 构建基本URL
        let url = `${API_ENDPOINTS.GPUS}?page=${currentPage}&limit=${pageSize}`;

        // 添加品牌筛选参数（首先添加）
        if (selectedBrand && selectedBrand !== 'all') {
            url += `&brand=${encodeURIComponent(selectedBrand)}`;
        }

        // 添加芯片组筛选参数
        if (selectedChipset && selectedChipset !== 'all') {
            url += `&chipset=${encodeURIComponent(selectedChipset)}`;
        }

        // 添加搜索参数（最后添加）
        if (searchTerm) {
            url += `&search=${encodeURIComponent(searchTerm)}`;
        }
        
        console.log('API请求URL:', url);
        
        // 显示Loading状态
        showLoading(true);
        
        fetchWithAuth(url)
            .then(response => {
                if (!response || !response.ok) {
                    return response.json().then(err => {
                        throw new Error(err.message || '加载显卡列表失败');
                    }).catch(e => {
                        throw new Error('加载显卡列表失败');
                    });
                }
                return response.json();
            })
            .then(data => {
                console.log('API返回数据:', data);
                gpus = data.gpus || [];
                totalRecords = data.total || 0;
                renderGpuTable(gpus);
                updatePagination();
                showLoading(false);
            })
            .catch(error => {
                console.error('Error:', error);
                showErrorMessage(error.message);
                
                // 显示错误状态
                if (gpuTableBody) {
                    gpuTableBody.innerHTML = `
                        <tr>
                            <td colspan="6" class="px-3 py-4 text-center text-red-500">
                                <div class="flex items-center justify-center">
                                    <i class="fas fa-exclamation-circle mr-2"></i>
                                    ${error.message || '加载数据失败'}
                                </div>
                            </td>
                        </tr>
                    `;
                }
                showLoading(false);
            });
    }

    // 显示或隐藏加载状态
    function showLoading(show) {
        const loadingEl = document.getElementById('loadingIndicator');
        if (!loadingEl) return;
        
        if (show) {
            loadingEl.classList.remove('hidden');
        } else {
            loadingEl.classList.add('hidden');
        }
    }

    // 渲染显卡表格
    function renderGpuTable(data) {
        if (!gpuTableBody) return;
        
        if (!Array.isArray(data) || !data.length) {
            gpuTableBody.innerHTML = `
                <tr>
                    <td colspan="6" class="px-4 py-4 text-center text-gray-500">暂无数据</td>
                </tr>
            `;
            if (totalCount) {
                totalCount.textContent = `共 0 条记录`;
            }
            return;
        }

        // 检测移动端
        const isMobile = window.innerWidth < 640;
        
        // 为每个GPU添加默认图片路径处理
        data.forEach(gpu => {
            if (!gpu.image_url) {
                // 设置默认图片路径为系统提供的默认图片
                gpu.is_default_image = true;
                gpu.image_url = DEFAULT_GPU_IMAGE;
            }
        });
        
        if (isMobile) {
            // 移动端卡片式布局
            renderMobileCards(data);
        } else {
            // PC端表格布局
            renderDesktopTable(data);
        }
        
        if (totalCount) {
            totalCount.textContent = `共 ${totalRecords} 条记录`;
        }
    }
    
    // 移动端卡片式布局渲染
    function renderMobileCards(data) {
        // 清空表格内容
        gpuTableBody.innerHTML = '';
        
        // 设置表格元素为块级显示
        const tableElement = gpuTableBody.closest('table');
        if (tableElement) {
            tableElement.style.display = 'block';
            tableElement.style.width = '100%';
            tableElement.style.maxWidth = '100%';
            tableElement.style.borderCollapse = 'collapse';
            tableElement.style.borderSpacing = '0';
            
            // 隐藏表头
            const theadElement = tableElement.querySelector('thead');
            if (theadElement) {
                theadElement.style.display = 'none';
            }
        }
        
        // 设置表格主体为块级显示
        gpuTableBody.style.display = 'block';
        gpuTableBody.style.width = '100%';
        gpuTableBody.style.maxWidth = '100%';
        
        // 检查用户权限
        isAdmin().then(isAdminUser => {
            // 遍历数据创建卡片
        data.forEach((gpu, index) => {
                // 创建卡片容器
                const cardOuterContainer = document.createElement('div');
                cardOuterContainer.className = 'gpu-card-outer-container';
                cardOuterContainer.style.width = '100%';
                cardOuterContainer.style.maxWidth = '100%';
                cardOuterContainer.style.padding = '0 4px';
                cardOuterContainer.style.marginBottom = '12px';
                cardOuterContainer.style.animation = `fadeIn 0.3s ease-in-out ${index * 0.05}s both`;
                
                // 创建卡片
            const card = document.createElement('div');
                card.className = 'gpu-card';
                card.style.width = '100%';
                card.style.maxWidth = '100%';
                card.style.borderRadius = '12px';
                card.style.overflow = 'hidden';
                card.style.backgroundColor = isDarkMode ? '#252525' : 'white';
                card.style.boxShadow = isDarkMode ? '0 4px 12px rgba(0,0,0,0.3)' : '0 4px 12px rgba(0,0,0,0.08)';
                card.style.display = 'flex';
                card.style.flexDirection = 'column';
                card.style.transition = 'transform 0.2s ease, box-shadow 0.2s ease';
                
                // 添加触摸反馈效果
                card.addEventListener('touchstart', function() {
                    this.style.transform = 'scale(0.99)';
                    this.style.boxShadow = isDarkMode ? 
                        '0 2px 8px rgba(0,0,0,0.4)' : '0 2px 8px rgba(0,0,0,0.05)';
                });
                
                card.addEventListener('touchend', function() {
                    this.style.transform = 'scale(1)';
                    this.style.boxShadow = isDarkMode ? 
                        '0 5px 15px rgba(0,0,0,0.3)' : '0 5px 15px rgba(0,0,0,0.07)';
                });
                
                // 根据品牌设置不同的颜色主题
                let headerBgColor = '#f8f9fa'; // 默认头部背景色
                let borderColor = 'rgba(203, 213, 225, 0.5)'; // 默认边框颜色
                
                if (gpu.brand === '华硕') {
                    headerBgColor = isDarkMode ? 'rgba(37, 99, 235, 0.1)' : 'rgba(37, 99, 235, 0.05)';
                    borderColor = isDarkMode ? 'rgba(37, 99, 235, 0.3)' : 'rgba(37, 99, 235, 0.15)';
                } else if (gpu.brand === '技嘉') {
                    headerBgColor = isDarkMode ? 'rgba(217, 119, 6, 0.1)' : 'rgba(217, 119, 6, 0.05)';
                    borderColor = isDarkMode ? 'rgba(217, 119, 6, 0.3)' : 'rgba(217, 119, 6, 0.15)';
                } else if (gpu.brand === '微星') {
                    headerBgColor = isDarkMode ? 'rgba(190, 24, 93, 0.1)' : 'rgba(190, 24, 93, 0.05)';
                    borderColor = isDarkMode ? 'rgba(190, 24, 93, 0.3)' : 'rgba(190, 24, 93, 0.15)';
                } else if (gpu.brand === '七彩虹' || gpu.brand === '影驰') {
                    headerBgColor = isDarkMode ? 'rgba(5, 150, 105, 0.1)' : 'rgba(5, 150, 105, 0.05)';
                    borderColor = isDarkMode ? 'rgba(5, 150, 105, 0.3)' : 'rgba(5, 150, 105, 0.15)';
                } else if (gpu.brand === '索泰') {
                    headerBgColor = isDarkMode ? 'rgba(79, 70, 229, 0.1)' : 'rgba(79, 70, 229, 0.05)';
                    borderColor = isDarkMode ? 'rgba(79, 70, 229, 0.3)' : 'rgba(79, 70, 229, 0.15)';
                } else {
                    // 默认暗色模式下的颜色
                    headerBgColor = isDarkMode ? 'rgba(0, 0, 0, 0.2)' : headerBgColor;
                    borderColor = isDarkMode ? 'rgba(255, 255, 255, 0.05)' : borderColor;
                }
                
                // 创建卡片头部
                const cardHeader = document.createElement('div');
                cardHeader.className = 'gpu-card-header';
                cardHeader.style.padding = '10px 14px';
                cardHeader.style.borderBottom = `1px solid ${borderColor}`;
                cardHeader.style.display = 'flex';
                cardHeader.style.justifyContent = 'space-between';
                cardHeader.style.alignItems = 'center';
                cardHeader.style.backgroundColor = headerBgColor;
                
                // 创建型号名称
                const modelName = document.createElement('div');
                modelName.textContent = gpu.model || '未知型号';
                modelName.style.fontSize = '1rem';
                modelName.style.fontWeight = isDarkMode ? '600' : 'bold';
                modelName.style.color = isDarkMode ? '#ffffff' : '#1a202c';
                modelName.style.wordBreak = 'break-word';
                modelName.style.flexGrow = '1';
                modelName.style.marginRight = '8px';
                
                // 创建芯片组标签
                const chipsetBadge = document.createElement('span');
                chipsetBadge.className = 'gpu-badge';
                chipsetBadge.style.padding = '4px 10px';
                chipsetBadge.style.fontWeight = '600';
                chipsetBadge.style.borderRadius = '4px';
                chipsetBadge.style.backgroundColor = isDarkMode ? 'rgba(124, 58, 237, 0.2)' : 'rgba(124, 58, 237, 0.1)';
                chipsetBadge.style.color = isDarkMode ? '#a78bfa' : '#7c3aed';
                chipsetBadge.style.border = isDarkMode ? '1px solid rgba(124, 58, 237, 0.3)' : '1px solid rgba(124, 58, 237, 0.2)';
                chipsetBadge.textContent = gpu.chipset || '未知';
                
                // 将元素添加到头部
                cardHeader.appendChild(modelName);
                cardHeader.appendChild(chipsetBadge);
                
                // 创建卡片主体
                const cardBody = document.createElement('div');
                cardBody.className = 'gpu-card-body';
                cardBody.style.padding = '12px 14px';
                cardBody.style.display = 'flex';
                cardBody.style.gap = '12px';
                cardBody.style.backgroundColor = isDarkMode ? '#1e1e1e' : 'white';
                
                // 创建图片容器
                const imgContainer = document.createElement('div');
                imgContainer.className = 'gpu-card-img-container';
                imgContainer.style.width = '60px';
                imgContainer.style.height = '60px';
                imgContainer.style.borderRadius = '6px';
                imgContainer.style.border = isDarkMode ? '1px solid #333' : '1px solid #eee';
                imgContainer.style.display = 'flex';
                imgContainer.style.alignItems = 'center';
                imgContainer.style.justifyContent = 'center';
                imgContainer.style.overflow = 'hidden';
                imgContainer.style.flexShrink = '0';
                imgContainer.style.backgroundColor = isDarkMode ? '#1a1a1a' : 'white';
                imgContainer.style.cursor = 'pointer';
                
                // 创建图片元素
                const imgElement = document.createElement('img');
                imgElement.className = 'gpu-card-img';
                imgElement.alt = gpu.model || '显卡图片';
                imgElement.style.width = '100%';
                imgElement.style.height = '100%';
                imgElement.style.objectFit = 'contain';
                
                // 设置图片源
                const defaultImage = DEFAULT_GPU_IMAGE;
            if (gpu.image_url) {
                    imgElement.src = gpu.image_url;
                    
                    // 为图片添加点击事件，实现全屏预览
                    const imageUrl = gpu.image_url;
                    imgElement.setAttribute('data-src', imageUrl);
                    imgElement.addEventListener('click', function(e) {
                        e.stopPropagation();
                        console.log('Mobile image clicked:', imageUrl);
                        openImageFullscreen(imageUrl);
                    });
                    
                    // 为图片容器也添加点击事件
                    imgContainer.addEventListener('click', function() {
                        console.log('Mobile image container clicked:', imageUrl);
                        openImageFullscreen(imageUrl);
                    });
            } else {
                    imgElement.src = defaultImage;
                    imgElement.style.opacity = '0.6';
                }
                
                // 图片加载失败时的处理
                imgElement.onerror = function() {
                    this.onerror = null; 
                    this.src = defaultImage;
                    this.style.opacity = '0.6';
                };
                
                // 将图片添加到容器
                imgContainer.appendChild(imgElement);
                
                // 创建信息容器
                const infoContainer = document.createElement('div');
                infoContainer.className = 'gpu-card-info';
                infoContainer.style.flexGrow = '1';
                infoContainer.style.display = 'flex';
                infoContainer.style.flexDirection = 'column';
                infoContainer.style.justifyContent = 'center';
                infoContainer.style.gap = '4px';
                
                // 添加品牌信息
                if (gpu.brand) {
                    const brandText = document.createElement('div');
                    brandText.className = 'text-sm text-gray-600';
                    brandText.style.color = isDarkMode ? '#a0aec0' : '#4b5563';
                    brandText.textContent = `品牌: ${gpu.brand}`;
                    infoContainer.appendChild(brandText);
                }
                
                // 添加显存信息
                if (gpu.memory_size) {
                    const memoryText = document.createElement('div');
                    memoryText.style.fontWeight = isDarkMode ? '600' : 'bold';
                    memoryText.style.color = isDarkMode ? '#f5f5f5' : '#374151';
                    memoryText.style.fontSize = isDarkMode ? '1.05rem' : 'inherit';
                    memoryText.textContent = `显存: ${gpu.memory_size} GB ${gpu.memory_type || ''}`;
                    infoContainer.appendChild(memoryText);
                }
                
                // 添加功耗信息
            if (gpu.tdp) {
                    const tdpText = document.createElement('div');
                    tdpText.className = 'text-sm text-gray-600';
                    tdpText.style.color = isDarkMode ? '#a0aec0' : '#4b5563';
                    tdpText.textContent = `功耗: ${gpu.tdp} W`;
                    infoContainer.appendChild(tdpText);
                }
                
                // 创建标签组
                const tagGroup = document.createElement('div');
                tagGroup.className = 'tag-group';
                tagGroup.style.display = 'flex';
                tagGroup.style.flexWrap = 'wrap';
                tagGroup.style.gap = '6px';
                tagGroup.style.marginTop = '8px';
                
                // 添加核心频率标签
                if (gpu.core_clock) {
            const addTag = (text, bgClass, textClass) => {
                const tag = document.createElement('span');
                        tag.className = 'spec-tag';
                        tag.style.padding = '1px 6px';
                        tag.style.fontSize = '0.7rem';
                        tag.style.borderRadius = '4px';
                        tag.style.backgroundColor = bgClass;
                        tag.style.color = textClass;
                        tag.style.border = isDarkMode ? '1px solid rgba(124, 58, 237, 0.3)' : '1px solid rgba(124, 58, 237, 0.2)';
                tag.textContent = text;
                        return tag;
                    };
                    
                    // 核心频率标签
                    const coreClockTag = addTag(
                        `${gpu.core_clock} MHz`, 
                        isDarkMode ? 'rgba(76, 29, 149, 0.25)' : 'rgba(76, 29, 149, 0.1)', 
                        isDarkMode ? '#a78bfa' : '#4c1d95'
                    );
                    tagGroup.appendChild(coreClockTag);
                    
                    // 加速频率标签
                    if (gpu.boost_clock) {
                        const boostClockTag = addTag(
                            `Boost: ${gpu.boost_clock} MHz`, 
                            isDarkMode ? 'rgba(6, 95, 70, 0.25)' : 'rgba(6, 95, 70, 0.1)', 
                            isDarkMode ? '#10b981' : '#065f46'
                        );
                        tagGroup.appendChild(boostClockTag);
                    }
                }
                
                // 添加标签组到信息容器
                if (tagGroup.children.length > 0) {
                    infoContainer.appendChild(tagGroup);
                }
                
                // 将图片容器和信息容器添加到卡片主体
                cardBody.appendChild(imgContainer);
                cardBody.appendChild(infoContainer);
                
                // 创建卡片底部
                const cardFooter = document.createElement('div');
                cardFooter.className = 'gpu-card-footer';
                cardFooter.style.padding = '8px 14px';
                cardFooter.style.borderTop = isDarkMode ? '1px solid rgba(255, 255, 255, 0.05)' : `1px solid ${borderColor}`;
                cardFooter.style.display = 'flex';
                cardFooter.style.justifyContent = 'space-around';
                cardFooter.style.alignItems = 'center';
                cardFooter.style.gap = '8px';
                cardFooter.style.backgroundColor = isDarkMode ? 'rgba(0, 0, 0, 0.1)' : '#fdfdff';
                
                // 创建操作按钮
                const createButton = (icon, text, color, onClick) => {
                    const button = document.createElement('button');
                    button.className = 'gpu-card-action-button';
                    button.style.padding = '4px 8px';
                    button.style.borderRadius = '6px';
                    button.style.display = 'inline-flex';
                    button.style.alignItems = 'center';
                    button.style.justifyContent = 'center';
                    button.style.backgroundColor = 'transparent';
                    button.style.color = color;
                    button.style.fontSize = '0.8rem';
                    button.style.fontWeight = '500';
                    button.style.transition = 'background-color 0.15s ease-out, color 0.15s ease-out';
                    button.innerHTML = `${icon} ${text}`;
                    button.addEventListener('click', onClick);
                    
                    // 添加触摸反馈
                    button.addEventListener('touchstart', function() { 
                        this.style.backgroundColor = `${color}33`; // 20% opacity
                    });
                    button.addEventListener('touchend', function() { 
                        this.style.backgroundColor = 'transparent';
                    });
                    
                    return button;
                };
                
                // 查看按钮
                const viewButton = createButton(
                    '<i class="fas fa-eye mr-1"></i>', 
                    '查看', 
                    isDarkMode ? '#a855f7' : '#9333ea', 
                    () => viewGpuDetails(gpu.id)
                );
                
                // 编辑按钮
                const editButton = createButton(
                    '<i class="fas fa-edit mr-1"></i>', 
                    '编辑', 
                    isDarkMode ? '#34d399' : '#10B981', 
                    () => editGpu(gpu.id)
                );
                
                // 删除按钮
                const deleteButton = createButton(
                    '<i class="fas fa-trash mr-1"></i>', 
                    '删除', 
                    isDarkMode ? '#f87171' : '#EF4444', 
                    () => confirmDeleteGpu(gpu.id)
                );
                
                // 非管理员用户的按钮处理
                if (!isAdminUser) {
                    // 编辑按钮权限控制
                    editButton.addEventListener('click', function(e) {
                        e.preventDefault();
                        e.stopPropagation();
                        showErrorMessage('权限不足，只有管理员可以编辑数据');
                        return false;
                    }, true);
                    
                    // 删除按钮权限控制
                    deleteButton.addEventListener('click', function(e) {
                        e.preventDefault();
                        e.stopPropagation();
                        showErrorMessage('权限不足，只有管理员可以删除数据');
                        return false;
                    }, true);
                }
                
                // 将按钮添加到底部
                cardFooter.appendChild(viewButton);
                cardFooter.appendChild(editButton);
                cardFooter.appendChild(deleteButton);
            
            // 组装卡片
                card.appendChild(cardHeader);
                card.appendChild(cardBody);
                card.appendChild(cardFooter);
                cardOuterContainer.appendChild(card);
                
                // 将卡片添加到表格主体
                gpuTableBody.appendChild(cardOuterContainer);
            });
        });
    }
    
    // 渲染桌面端表格布局
    function renderDesktopTable(data) {
        // 清空表格内容
        gpuTableBody.innerHTML = '';
        
        data.forEach((gpu, index) => {
            // 创建行元素
            const row = document.createElement('tr');
            row.className = 'hover:bg-gray-50 transition-colors';
            // 设置淡入效果
            row.style.animation = 'fadeIn 0.3s ease-in-out';
            row.style.animationDelay = `${index * 0.05}s`;
            row.style.animationFillMode = 'both';
            row.style.height = '4.5rem'; // 设置固定行高

            // 显卡型号列（含图片）
            const modelCell = document.createElement('td');
            modelCell.className = 'px-2 py-2 sm:px-3 sm:py-3';
            
            const modelContainer = document.createElement('div');
            modelContainer.className = 'flex items-center';
            
            // 图片容器
            const imgContainer = document.createElement('div');
            imgContainer.className = 'relative mr-3 group gpu-image-container';
            
            if (gpu.image_url) {
                const img = document.createElement('img');
                img.src = gpu.image_url;
                img.alt = gpu.model || 'GPU图片';
                img.className = 'h-10 w-10 rounded-md object-cover cursor-pointer transition-transform group-hover:scale-110 js-preview-image';
                img.dataset.src = gpu.image_url;
                
                const overlay = document.createElement('div');
                overlay.className = 'img-overlay rounded-md absolute inset-0 flex items-center justify-center bg-black bg-opacity-20 opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none';
                overlay.innerHTML = '<i class="fas fa-search-plus text-white text-opacity-80"></i>';
                
                imgContainer.appendChild(img);
                imgContainer.appendChild(overlay);
                
                // 标记默认图片
                if (gpu.is_default_image) {
                    const defaultBadge = document.createElement('div');
                    defaultBadge.className = 'absolute bottom-0 right-0 bg-gray-700 text-white text-xs px-0.5 rounded-tl-md';
                    defaultBadge.style.fontSize = '0.6rem';
                    defaultBadge.textContent = '默认';
                    imgContainer.appendChild(defaultBadge);
                }
            } else {
                // 使用默认图片
                const img = document.createElement('img');
                img.src = DEFAULT_GPU_IMAGE;
                img.alt = gpu.model || 'GPU图片';
                img.className = 'h-10 w-10 rounded-md object-cover cursor-pointer transition-transform group-hover:scale-110 js-preview-image';
                img.dataset.src = DEFAULT_GPU_IMAGE;
                
                const overlay = document.createElement('div');
                overlay.className = 'img-overlay rounded-md absolute inset-0 flex items-center justify-center bg-black bg-opacity-20 opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none';
                overlay.innerHTML = '<i class="fas fa-search-plus text-white text-opacity-80"></i>';
                
                // 标记默认图片
                const defaultBadge = document.createElement('div');
                defaultBadge.className = 'absolute bottom-0 right-0 bg-gray-700 text-white text-xs px-0.5 rounded-tl-md';
                defaultBadge.style.fontSize = '0.6rem';
                defaultBadge.textContent = '默认';
                
                imgContainer.appendChild(img);
                imgContainer.appendChild(overlay);
                imgContainer.appendChild(defaultBadge);
            }
            
            // 型号信息容器
            const textContainer = document.createElement('div');
            textContainer.className = 'overflow-hidden';
            
            const modelText = document.createElement('div');
            modelText.className = 'text-sm font-medium text-gray-900 truncate';
            modelText.textContent = gpu.model || '-';
            modelText.title = gpu.model || '';
            
            const dimensionsText = document.createElement('div');
            dimensionsText.className = 'text-xs text-gray-500 hidden sm:block truncate';
            dimensionsText.textContent = gpu.dimensions ? `尺寸: ${gpu.dimensions}` : '';
            
            textContainer.appendChild(modelText);
            textContainer.appendChild(dimensionsText);
            
            modelContainer.appendChild(imgContainer);
            modelContainer.appendChild(textContainer);
            modelCell.appendChild(modelContainer);
            row.appendChild(modelCell);

            // 品牌列
            const brandCell = document.createElement('td');
            brandCell.className = 'px-2 py-2 sm:px-3 sm:py-3 hidden sm:table-cell';
            
            // 确定品牌样式类
            let brandClass = '';
            if (gpu.brand) {
                if (gpu.brand.toLowerCase().includes('nvidia')) {
                    brandClass = 'brand-nvidia';
                } else if (gpu.brand.toLowerCase().includes('amd')) {
                    brandClass = 'brand-amd';
                } else if (gpu.brand.toLowerCase().includes('intel')) {
                    brandClass = 'brand-intel';
                }
            }

            const brandBadge = document.createElement('span');
            brandBadge.className = `brand-badge ${brandClass}`;
            brandBadge.textContent = gpu.brand || '-';
            brandCell.appendChild(brandBadge);
            row.appendChild(brandCell);

            // 芯片组列
            const chipsetCell = document.createElement('td');
            chipsetCell.className = 'px-2 py-2 sm:px-3 sm:py-3 hidden md:table-cell';
            
            const chipsetText = document.createElement('div');
            chipsetText.className = 'text-sm text-gray-900';
            chipsetText.textContent = gpu.chipset || '-';
            chipsetCell.appendChild(chipsetText);
            row.appendChild(chipsetCell);

            // 显存列
            const memoryCell = document.createElement('td');
            memoryCell.className = 'px-2 py-2 sm:px-3 sm:py-3';
            
            // 设置显存信息
            let memoryDisplay = '-';
            if (gpu.memory_size) {
                if (gpu.memory_size >= 1000) {
                    memoryDisplay = (gpu.memory_size / 1000).toFixed(1) + 'TB';
                } else {
                    memoryDisplay = gpu.memory_size + 'GB';
                }
                if (gpu.memory_type) {
                    memoryDisplay += ' ' + gpu.memory_type;
                }
            } else if (gpu.memory_type) {
                memoryDisplay = gpu.memory_type;
            }
            
            const memoryTag = document.createElement('span');
            memoryTag.className = 'memory-tag';
            memoryTag.textContent = memoryDisplay;
            memoryCell.appendChild(memoryTag);
            
            // 移动端显示芯片组信息
            const mobileChipset = document.createElement('div');
            mobileChipset.className = 'text-xs text-gray-500 sm:hidden truncate mt-1';
            mobileChipset.textContent = gpu.chipset || '';
            memoryCell.appendChild(mobileChipset);
            
            row.appendChild(memoryCell);

            // 功耗列
            const tdpCell = document.createElement('td');
            tdpCell.className = 'px-2 py-2 sm:px-3 sm:py-3 hidden sm:table-cell';
            
            const tdpTag = document.createElement('span');
            tdpTag.className = 'inline-flex items-center px-2.5 py-1 rounded-md text-xs font-medium bg-red-50 text-red-700';
            tdpTag.textContent = gpu.tdp ? `${gpu.tdp}W` : '-';
            tdpCell.appendChild(tdpTag);
            row.appendChild(tdpCell);

            // 操作列
            const actionCell = document.createElement('td');
            actionCell.className = 'px-2 py-2 sm:px-3 sm:py-3 text-center';
            
            const actionContainer = document.createElement('div');
            actionContainer.className = 'action-buttons';
            
            // 查看按钮
            const viewButton = document.createElement('button');
            viewButton.className = 'btn-view';
            viewButton.innerHTML = '<i class="fas fa-eye"></i>';
            viewButton.title = '查看详情';
            viewButton.dataset.id = gpu.id;
            viewButton.addEventListener('click', () => viewGpuDetails(gpu.id));
            actionContainer.appendChild(viewButton);
            
            // 编辑按钮
            const editButton = document.createElement('button');
            editButton.className = 'btn-edit';
            editButton.innerHTML = '<i class="fas fa-edit"></i>';
            editButton.title = '编辑';
            editButton.dataset.id = gpu.id;
            editButton.addEventListener('click', () => editGpu(gpu.id));
            actionContainer.appendChild(editButton);
            
            // 删除按钮
            const deleteButton = document.createElement('button');
            deleteButton.className = 'btn-delete';
            deleteButton.innerHTML = '<i class="fas fa-trash"></i>';
            deleteButton.title = '删除';
            deleteButton.dataset.id = gpu.id;
            deleteButton.addEventListener('click', () => confirmDeleteGpu(gpu.id));
            actionContainer.appendChild(deleteButton);
            
            actionCell.appendChild(actionContainer);
            row.appendChild(actionCell);

            // 将行添加到表格
            gpuTableBody.appendChild(row);
        });
    }

    // 更新分页信息
    function updatePagination() {
        const currentPageDisplay = document.getElementById('currentPageDisplay');
        const totalPagesDisplay = document.getElementById('totalPagesDisplay');
        const pageNumbers = document.getElementById('pageNumbers');
        const firstPageBtn = document.getElementById('firstPage');
        const prevPageBtn = document.getElementById('prevPage');
        const nextPageBtn = document.getElementById('nextPage');
        const lastPageBtn = document.getElementById('lastPage');
        const pageJumpInput = document.getElementById('pageJump');
        
        if (!currentPageDisplay || !totalPagesDisplay) return;
        
        const totalPages = Math.ceil(totalRecords / pageSize) || 1;
        
        // 更新当前页和总页数显示
        currentPageDisplay.textContent = currentPage;
        totalPagesDisplay.textContent = totalPages;
        
        // 启用或禁用页码按钮
        if (prevPageBtn) {
            prevPageBtn.disabled = currentPage <= 1;
            prevPageBtn.classList.toggle('opacity-50', currentPage <= 1);
        }
        
        if (nextPageBtn) {
            nextPageBtn.disabled = currentPage >= totalPages;
            nextPageBtn.classList.toggle('opacity-50', currentPage >= totalPages);
        }
        
        if (firstPageBtn) {
            firstPageBtn.disabled = currentPage <= 1;
            firstPageBtn.classList.toggle('opacity-50', currentPage <= 1);
        }
        
        if (lastPageBtn) {
            lastPageBtn.disabled = currentPage >= totalPages;
            lastPageBtn.classList.toggle('opacity-50', currentPage >= totalPages);
        }
        
        if (pageJumpInput) {
            pageJumpInput.max = totalPages;
        }
        
        // 生成页码按钮
        if (pageNumbers) {
            pageNumbers.innerHTML = '';
            
            // 确定要显示的页码范围
            let startPage = Math.max(1, currentPage - Math.floor(maxPageButtons / 2));
            let endPage = startPage + maxPageButtons - 1;
            
            if (endPage > totalPages) {
                endPage = totalPages;
                startPage = Math.max(1, endPage - maxPageButtons + 1);
            }
            
            // 生成页码按钮
            for (let i = startPage; i <= endPage; i++) {
                const pageButton = document.createElement('button');
                pageButton.type = 'button';
                pageButton.className = `px-3 py-1 border rounded-md text-sm ${
                    i === currentPage
                        ? 'bg-red-600 text-white border-red-600'
                        : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
                }`;
                pageButton.textContent = i;
                pageButton.addEventListener('click', () => changePage(i));
                
                pageNumbers.appendChild(pageButton);
            }
        }
    }

    // 更改页面
    function changePage(newPage) {
        const totalPages = Math.ceil(totalRecords / pageSize) || 1;
        
        if (newPage < 1 || newPage > totalPages) return;
        
        currentPage = newPage;
        loadGpus();
    }
    
    // 查看显卡详情
    function viewGpuDetails(id) {
        console.log('查看显卡详情:', id);
        
        if (!id) {
            showErrorMessage('无效的显卡ID');
            return;
        }
        
        // 显示加载状态
        if (gpuDetails) {
            gpuDetails.innerHTML = `
                <div class="flex justify-center items-center h-40">
                    <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-500"></div>
                </div>
            `;
        }
        
        if (gpuModal) {
            gpuModal.classList.remove('hidden');
        }
        
        currentGpuId = id;
        
        // 从已加载的数据中查找
        const gpu = gpus.find(g => g.id === id || g.id === parseInt(id));
        if (gpu) {
            renderGpuDetails(gpu);
            
            // 检查用户权限并处理详情页面的按钮
            isAdmin().then(isAdminUser => {
                if (!isAdminUser) {
                    const modalEditBtn = document.getElementById('editBtn');
                    const modalDeleteBtn = document.getElementById('deleteBtn');
                    
                    if (modalEditBtn) {
                        modalEditBtn.removeEventListener('click', handleEdit);
                        modalEditBtn.addEventListener('click', function(e) {
                            e.preventDefault();
                            e.stopPropagation();
                            showErrorMessage('权限不足，只有管理员可以编辑数据');
                            return false;
                        }, true);
                    }
                    
                    if (modalDeleteBtn) {
                        // 移除原有的点击事件
                        const newDeleteBtn = modalDeleteBtn.cloneNode(true);
                        if (modalDeleteBtn.parentNode) {
                            modalDeleteBtn.parentNode.replaceChild(newDeleteBtn, modalDeleteBtn);
                        }
                        
                        newDeleteBtn.addEventListener('click', function(e) {
                            e.preventDefault();
                            e.stopPropagation();
                            showErrorMessage('权限不足，只有管理员可以删除数据');
                            return false;
                        }, true);
                    }
                }
            });
            
            return;
        }
        
        // 如果本地没有，从API获取
        fetchWithAuth(`${API_ENDPOINTS.GPU(id)}`)
            .then(response => {
                if (!response || !response.ok) {
                    throw new Error('获取显卡详情失败');
                }
                return response.json();
            })
            .then(gpu => {
                renderGpuDetails(gpu);
                
                // 检查用户权限并处理详情页面的按钮
                isAdmin().then(isAdminUser => {
                    if (!isAdminUser) {
                        const modalEditBtn = document.getElementById('editBtn');
                        const modalDeleteBtn = document.getElementById('deleteBtn');
                        
                        if (modalEditBtn) {
                            modalEditBtn.removeEventListener('click', handleEdit);
                            modalEditBtn.addEventListener('click', function(e) {
                                e.preventDefault();
                                e.stopPropagation();
                                showErrorMessage('权限不足，只有管理员可以编辑数据');
                                return false;
                            }, true);
                        }
                        
                        if (modalDeleteBtn) {
                            // 移除原有的点击事件
                            const newDeleteBtn = modalDeleteBtn.cloneNode(true);
                            if (modalDeleteBtn.parentNode) {
                                modalDeleteBtn.parentNode.replaceChild(newDeleteBtn, modalDeleteBtn);
                            }
                            
                            newDeleteBtn.addEventListener('click', function(e) {
                                e.preventDefault();
                                e.stopPropagation();
                                showErrorMessage('权限不足，只有管理员可以删除数据');
                                return false;
                            }, true);
                        }
                    }
                });
            })
            .catch(error => {
                console.error('Error:', error);
                showErrorMessage(error.message);
            });
    }

    // 渲染显卡详情
    function renderGpuDetails(gpu) {
        if (!gpu) return;
        
        const detailsElement = gpuDetails || document.getElementById('gpuDetailContent');
        if (!detailsElement) {
            console.error('Error: GPU details element not found');
            return;
        }

        // 显示容量逻辑：大于等于1000GB时显示为TB
        let memoryDisplay = '-';
        if (gpu.memory_size) {
            if (gpu.memory_size >= 1000) {
                // 转换为TB并保留一位小数
                const tbSize = (gpu.memory_size / 1000).toFixed(1);
                memoryDisplay = `${tbSize} TB`;
            } else {
                memoryDisplay = `${gpu.memory_size} GB`;
            }
        }

        // 如果没有图片则使用默认图片
        const imageUrl = gpu.image_url || DEFAULT_GPU_IMAGE;
        const isDefaultImage = !gpu.image_url || gpu.is_default_image;

        const details = `
            <!-- 主要信息区：图片和重要信息并排显示 -->
            <div class="flex flex-col md:flex-row gap-6">
                <!-- 左侧显卡图片 -->
                <div class="md:w-1/3">
                    <div class="bg-gray-50 p-2 rounded-lg shadow-md h-full flex items-center justify-center relative">
                        <img src="${imageUrl}" alt="${gpu.model}" class="w-full rounded-lg cursor-pointer hover:opacity-90 js-preview-image" 
                            data-src="${imageUrl}" style="max-height: 280px; object-fit: contain;">
                        ${isDefaultImage ? `<div class="absolute bottom-2 right-2 bg-gray-700 text-white text-xs px-1.5 py-0.5 rounded-md opacity-80">默认图片</div>` : ''}
                    </div>
                </div>
                
                <!-- 右侧重要信息 -->
                <div class="md:w-2/3">
                    <!-- 标题和价格区域 -->
                    <div class="border-b pb-3 mb-4">
                        <h2 class="text-xl font-bold text-gray-800 mb-1">${gpu.model || '未知型号'}</h2>
                        <div class="flex items-center justify-between">
                            <p class="text-gray-600">${gpu.brand || '-'} | ${gpu.chipset || '-'}</p>
                            <p class="text-xl font-bold text-red-600">${gpu.price ? `¥${gpu.price}` : '价格未知'}</p>
            </div>
                    </div>
                    
                    <!-- 关键信息区域 -->
                    <div class="grid grid-cols-2 gap-4 mb-4">
                        <div class="bg-red-50 p-3 rounded-lg">
                            <h4 class="font-semibold text-md mb-2 text-red-700">显存</h4>
                            <p class="text-2xl font-bold text-red-700">${memoryDisplay}</p>
                            <p class="text-sm text-gray-600">${gpu.memory_type || '-'} | ${gpu.memory_bus ? `${gpu.memory_bus}bit` : '-'}</p>
                        </div>
                        <div class="bg-orange-50 p-3 rounded-lg">
                            <h4 class="font-semibold text-md mb-2 text-orange-700">功耗</h4>
                            <p class="text-2xl font-bold text-orange-700">${gpu.tdp ? `${gpu.tdp}W` : '-'}</p>
                            <p class="text-sm text-gray-600">推荐电源: ${gpu.recommended_psu ? `${gpu.recommended_psu}W` : '-'}</p>
                        </div>
                    </div>
                    
                    <!-- 频率信息 -->
                    <div class="grid grid-cols-2 gap-4 mb-4">
                <div>
                            <h5 class="text-sm font-medium text-gray-700">核心频率</h5>
                            <p class="font-semibold">${gpu.core_clock ? `${gpu.core_clock} MHz` : '-'}</p>
                    </div>
                        <div>
                            <h5 class="text-sm font-medium text-gray-700">加速频率</h5>
                            <p class="font-semibold">${gpu.boost_clock ? `${gpu.boost_clock} MHz` : '-'}</p>
                </div>
                    </div>
                    
                    <!-- 接口信息 -->
                    <div class="grid grid-cols-2 gap-4 mb-4">
                <div>
                            <h5 class="text-sm font-medium text-gray-700">DisplayPort</h5>
                            <p class="font-semibold">${gpu.display_ports || '-'}</p>
                    </div>
                        <div>
                            <h5 class="text-sm font-medium text-gray-700">HDMI接口</h5>
                            <p class="font-semibold">${gpu.hdmi_ports || '-'}</p>
                </div>
            </div>
                </div>
            </div>
            
            <!-- 分隔线 -->
            <div class="border-t my-6"></div>
            
            <!-- 详细规格区域 -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- 左侧：物理规格 -->
                <div class="bg-gray-50 p-4 rounded-lg">
                    <h4 class="font-semibold text-lg mb-3 text-gray-800 border-b pb-2">物理规格</h4>
                    <div class="space-y-3">
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-600">尺寸:</span>
                            <span class="font-medium">${gpu.dimensions || '-'}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-600">电源接口:</span>
                            <span class="font-medium">${gpu.power_connectors || '-'}</span>
                        </div>
                    </div>
                </div>
                
                <!-- 右侧：其他信息 -->
                <div class="bg-gray-50 p-4 rounded-lg">
                    <h4 class="font-semibold text-lg mb-3 text-gray-800 border-b pb-2">其他信息</h4>
                    <div class="space-y-3">
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-600">添加时间:</span>
                            <span class="font-medium">${gpu.created_at ? new Date(gpu.created_at).toLocaleString() : '-'}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-600">最后更新:</span>
                            <span class="font-medium">${gpu.updated_at ? new Date(gpu.updated_at).toLocaleString() : '-'}</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 备注区域 -->
            ${gpu.notes ? `
            <div class="mt-6 bg-gray-50 p-4 rounded-lg">
                <h4 class="font-semibold text-lg mb-2 text-gray-800">备注</h4>
                <p class="text-gray-700">${gpu.notes}</p>
            </div>
            ` : ''}
        `;
        
        detailsElement.innerHTML = details;
    }

    // 编辑显卡信息
    function editGpu(id) {
        if (!id) return;
        
        fetchWithAuth(`${API_ENDPOINTS.GPU(id)}`)
            .then(response => {
                if (!response || !response.ok) {
                    throw new Error('获取显卡信息失败');
                }
                return response.json();
            })
            .then(gpu => {
                resetForm();
                currentGpuId = gpu.id;
                isEditing = true;  // 设置为编辑模式
                
                // 填充表单
                if (model) model.value = gpu.model || '';
                if (brand) brand.value = gpu.brand || '';
                if (chipset) chipset.value = gpu.chipset || '';
                if (memorySize) memorySize.value = gpu.memory_size || '';
                if (memoryType) memoryType.value = gpu.memory_type || '';
                if (memoryBus) memoryBus.value = gpu.memory_bus || '';
                if (coreClock) coreClock.value = gpu.core_clock || '';
                if (boostClock) boostClock.value = gpu.boost_clock || '';
                if (tdp) tdp.value = gpu.tdp || '';
                if (dimensions) dimensions.value = gpu.dimensions || '';
                if (powerConnectors) powerConnectors.value = gpu.power_connectors || '';
                if (recommendedPsu) recommendedPsu.value = gpu.recommended_psu || '';
                if (displayPorts) displayPorts.value = gpu.display_ports || '';
                if (hdmiPorts) hdmiPorts.value = gpu.hdmi_ports || '';
                if (price) price.value = gpu.price || '';
                if (notes) notes.value = gpu.notes || '';
                
                if (gpu.image_url && imagePreview) {
                    imagePreview.src = gpu.image_url;
                    if (imagePreviewContainer) {
                        imagePreviewContainer.classList.remove('hidden');
                    }

                    // 添加点击预览功能
                    imagePreview.onclick = () => {
                        openImageFullscreen(gpu.image_url);
                    };
                    imagePreview.style.cursor = 'pointer';
                    imagePreview.title = '点击查看大图';
                }
                
                const modalTitle = document.getElementById('modalTitle');
                if (modalTitle) {
                    modalTitle.textContent = '编辑显卡信息';
                }
                
                const submitBtn = document.getElementById('submitBtn') || (gpuForm ? gpuForm.querySelector('button[type="submit"]') : null);
                if (submitBtn) {
                    submitBtn.innerHTML = '<i class="fas fa-save mr-1"></i> 更新显卡信息';
                }
                
                // 如果有详情模态框打开，先关闭它
                const detailModal = document.getElementById('gpuDetailModal');
                if (detailModal && !detailModal.classList.contains('hidden')) {
                    detailModal.classList.add('hidden');
                }
                
                // 如果有模态框，关闭它
                if (gpuModal && !gpuModal.classList.contains('hidden')) {
                    gpuModal.classList.add('hidden');
                }
                
                // 滚动到表单区域
                if (gpuForm) {
                    gpuForm.scrollIntoView({ behavior: 'smooth' });
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showErrorMessage(error.message);
            });
    }

    // 确认删除显卡
    function confirmDeleteGpu(id) {
        if (!id) return;
        
        if (confirm('确定要删除这个显卡吗？此操作不可撤销。')) {
            deleteGpu(id);
        }
    }

    // 删除显卡
    function deleteGpu(id) {
        console.log(`准备删除ID为 ${id} 的GPU`);
        
        fetchWithAuth(API_ENDPOINTS.GPU(id), {
            method: 'DELETE',
        })
        .then(response => {
            // 如果状态码是 204 No Content，说明删除成功，且没有响应体
            if (response.status === 204) {
                return null; // 返回 null 或一个可识别的成功标志
            }
            // 如果有响应体，则正常解析JSON
            if (response.ok) {
                return response.json();
            }
            // 如果服务器返回了错误状态码，则尝试解析错误信息
            return response.json().then(errorData => {
                throw new Error(errorData.message || `服务器错误，状态码: ${response.status}`);
            });
        })
        .then(data => {
            console.log('GPU删除成功');
            showSuccessMessage('GPU删除成功！');
            
            // 从前端移除该行
            const rowToRemove = document.getElementById(`gpu-row-${id}`);
            if (rowToRemove) {
                rowToRemove.remove();
            }
            // 重新加载数据，以确保分页和总数正确
            loadGpus();
        })
        .catch(error => {
            console.error('删除GPU失败:', error);
            showErrorMessage(error.message || '删除失败，请稍后重试');
        });
    }

    // 处理编辑按钮点击
    function handleEdit() {
        if (currentGpuId) {
            editGpu(currentGpuId);
            
            // 关闭模态框
            if (gpuModal) {
                gpuModal.classList.add('hidden');
            }
        }
    }

    // 显示成功消息
    function showSuccessMessage(message) {
        const alertElement = document.createElement('div');
        alertElement.className = 'fixed top-4 right-4 bg-green-50 border-l-4 border-green-500 p-4 opacity-0 transition-opacity duration-300 shadow-md fade-in';
        alertElement.innerHTML = `
            <div class="flex items-center">
                <div class="flex-shrink-0 text-green-500">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="ml-3">
                    <p class="text-sm text-green-700">${message}</p>
                </div>
            </div>
        `;
        document.body.appendChild(alertElement);
        
        setTimeout(() => alertElement.classList.add('opacity-100'), 10);
        setTimeout(() => {
            alertElement.classList.remove('opacity-100');
            setTimeout(() => document.body.removeChild(alertElement), 300);
        }, 3000);
    }

    // 显示错误消息
    function showErrorMessage(message) {
        const alertElement = document.createElement('div');
        alertElement.className = 'fixed top-4 right-4 bg-red-50 border-l-4 border-red-500 p-4 opacity-0 transition-opacity duration-300 shadow-md fade-in';
        alertElement.innerHTML = `
            <div class="flex items-center">
                <div class="flex-shrink-0 text-red-500">
                    <i class="fas fa-exclamation-circle"></i>
                </div>
                <div class="ml-3">
                    <p class="text-sm text-red-700">${message}</p>
                </div>
            </div>
        `;
        document.body.appendChild(alertElement);
        
        setTimeout(() => alertElement.classList.add('opacity-100'), 10);
        setTimeout(() => {
            alertElement.classList.remove('opacity-100');
            setTimeout(() => document.body.removeChild(alertElement), 300);
        }, 3000);
    }

    // 防抖函数
    function debounce(func, delay) {
        let timeout;
        return function() {
            const context = this;
            const args = arguments;
            clearTimeout(timeout);
            timeout = setTimeout(() => func.apply(context, args), delay);
        };
    }
    
    // 暴露需要全局访问的函数
    window.viewGpuDetails = viewGpuDetails;
    window.editGpu = editGpu;
    window.confirmDeleteGpu = confirmDeleteGpu;
    window.openImageFullscreen = openImageFullscreen;
});

// 打开全屏图片 - 修复PC端表格图片点击预览功能
function openImageFullscreen(src) {
    console.log('正在打开图片预览:', src);
    try {
        // 如果存在之前的预览实例，先销毁它
        if (activeViewer) {
            try {
                activeViewer.destroy();
                activeViewer = null;
            } catch (e) {
                console.error('销毁上一个预览实例失败:', e);
            }
        }

        // 创建一个临时的图片容器
        const container = document.createElement('div');
        container.className = 'viewer-container';
        container.style.display = 'none';
        document.body.appendChild(container);

        // 创建图片元素
        const img = document.createElement('img');
        img.src = src;
        container.appendChild(img);

        // 确保Viewer是可用的
        if (typeof Viewer === 'undefined') {
            console.error('Viewer.js未加载，无法预览图片');
            alert('图片预览功能不可用，请刷新页面后重试');
            return;
        }

        // 初始化 Viewer
        const viewer = new Viewer(img, {
            backdrop: true,          // 启用背景遮罩
            button: true,           // 显示关闭按钮
            navbar: false,          // 隐藏底部导航栏（只有一张图片时不需要）
            title: false,           // 不显示标题
            toolbar: {              // 自定义工具栏
                zoomIn: true,       // 放大按钮
                zoomOut: true,      // 缩小按钮
                oneToOne: true,     // 1:1 尺寸按钮
                reset: true,        // 重置按钮
                prev: false,        // 上一张（隐藏，因为只有一张图片）
                play: false,        // 播放按钮（隐藏）
                next: false,        // 下一张（隐藏）
                rotateLeft: true,   // 向左旋转
                rotateRight: true,  // 向右旋转
                flipHorizontal: true, // 水平翻转
                flipVertical: true,  // 垂直翻转
            },
            viewed() {
                // 图片加载完成后自动打开查看器
                if (isMobile) {
                    viewer.zoomTo(0.8);  // 移动设备使用较小的初始缩放比例
                } else {
                    viewer.zoomTo(1);    // 桌面设备使用正常的缩放比例
                }
            },
            hidden() {
                // 查看器关闭后移除临时元素
                viewer.destroy();
                document.body.removeChild(container);
                activeViewer = null; // 清除活跃预览实例引用
            },
            maxZoomRatio: 5,        // 最大缩放比例
            minZoomRatio: 0.1,      // 最小缩放比例
            transition: true,       // 启用过渡效果
            keyboard: true,         // 启用键盘支持
        });

        // 保存当前预览实例
        activeViewer = viewer;

        // 显示查看器
        viewer.show();
    } catch (error) {
        console.error('图片预览出错:', error);
        alert('图片预览功能发生错误，请刷新页面后重试');
    }
}

// 显示Toast消息
function showToast(message, color = '#4F46E5') {
    const toast = document.createElement('div');
    toast.className = 'fixed bottom-4 right-4 px-4 py-2 rounded-md text-white shadow-lg';
    toast.style.backgroundColor = color;
    toast.textContent = message;
    document.body.appendChild(toast);
    setTimeout(() => toast.remove(), 3000);
} 










// 初始化权限控制
function initPermissions() {
    console.log('初始化权限控制...');
    
    // 如果没有权限控制模块，发出警告
    if (typeof setupPermissionBasedUI !== 'function') {
        console.warn('警告：权限控制模块未加载！请确保已引入permission-helper.js');
        return;
    }
    
    // 执行权限控制初始化
    setupPermissionBasedUI().then(() => {
        // 检查编辑按钮
        const editButtons = document.querySelectorAll('.btn-edit, [data-action="edit"], button[title="编辑"]');
        const deleteButtons = document.querySelectorAll('.btn-delete, [data-action="delete"], button[title="删除"]');
        
        // 根据用户角色处理按钮
        isAdmin().then(isAdminUser => {
            if (!isAdminUser) {
                // 禁用表单
                const form = document.querySelector('form');
                if (form) {
                    const inputs = form.querySelectorAll('input, select, textarea');
                    inputs.forEach(input => {
                        input.setAttribute('disabled', 'disabled');
                    });
                    
                    const submitBtn = form.querySelector('button[type="submit"]');
                    if (submitBtn) {
                        submitBtn.setAttribute('disabled', 'disabled');
                        submitBtn.title = '只有管理员可以添加或修改数据';
                    }
                }
                
                // 隐藏或禁用编辑、删除按钮
                editButtons.forEach(btn => {
                    btn.style.display = 'none';
                });
                
                deleteButtons.forEach(btn => {
                    btn.style.display = 'none';
                });
                
                // 特别处理详情模态框中的按钮
                const handleDetailModalButtons = () => {
                    const modalEditBtn = document.getElementById('editBtn');
                    const modalDeleteBtn = document.getElementById('deleteBtn');
                    
                    if (modalEditBtn) {
                        // 对于模态框中的编辑按钮，保留显示但添加权限检查
                        modalEditBtn.style.display = 'inline-flex';
                        modalEditBtn.removeEventListener('click', handleEdit);
                        modalEditBtn.addEventListener('click', function(e) {
                            e.preventDefault();
                            e.stopPropagation();
                            showErrorMessage('权限不足，只有管理员可以编辑数据');
                            return false;
                        }, true);
                    }
                    
                    if (modalDeleteBtn) {
                        // 对于模态框中的删除按钮，保留显示但添加权限检查
                        modalDeleteBtn.style.display = 'inline-flex';
                        // 移除原有的点击事件
                        const newDeleteBtn = modalDeleteBtn.cloneNode(true);
                        if (modalDeleteBtn.parentNode) {
                            modalDeleteBtn.parentNode.replaceChild(newDeleteBtn, modalDeleteBtn);
                        }
                        
                        newDeleteBtn.addEventListener('click', function(e) {
                            e.preventDefault();
                            e.stopPropagation();
                            showErrorMessage('权限不足，只有管理员可以删除数据');
                            return false;
                        }, true);
                    }
                };
                
                // 初始化时执行一次
                handleDetailModalButtons();
                
                // 每次模态框打开时也执行
                const gpuDetailModal = document.getElementById('gpuModal');
                if (gpuDetailModal) {
                    // 创建一个MutationObserver来监视模态框的显示状态
                    const observer = new MutationObserver(function(mutations) {
                        mutations.forEach(function(mutation) {
                            if (mutation.attributeName === 'class' && 
                                !gpuDetailModal.classList.contains('hidden')) {
                                // 模态框显示了，处理按钮
                                handleDetailModalButtons();
                            }
                        });
                    });
                    
                    // 开始观察模态框
                    observer.observe(gpuDetailModal, { attributes: true });
                }
                
                // 为所有删除操作添加权限验证拦截器
                interceptDeleteOperations();
            } else {
                // 添加管理员标识
                const header = document.querySelector('h1, h2');
                if (header) {
                    // 检查是否已经添加过徽章
                    if (!header.querySelector('.admin-badge')) {
                    const adminBadge = document.createElement('span');
                        adminBadge.className = 'bg-green-500 text-white text-xs px-2 py-1 rounded ml-2 admin-badge';
                    adminBadge.innerText = '管理员';
                    header.appendChild(adminBadge);
                    }
                }
            }
        });
    });
}

// 拦截所有删除操作的请求
function interceptDeleteOperations() {
    // 保存原始的fetch函数
    const originalFetch = window.fetch;
    
    // 重写fetch函数以拦截删除请求
    window.fetch = async function(url, options) {
        // 检查是否为删除操作
        if (options && options.method === 'DELETE') {
            console.log('拦截到删除操作请求:', url);
            
            // 检查用户权限
            const isAdminUser = await isAdmin();
            if (!isAdminUser) {
                console.warn('权限不足，普通用户无法执行删除操作');
                // 返回权限错误响应
                return Promise.resolve(new Response(JSON.stringify({
                    error: '权限不足',
                    message: '只有管理员可以删除数据'
                }), {
                    status: 403,
                    headers: {
                        'Content-Type': 'application/json'
                    }
                }));
            }
        }
        
        // 对于非删除请求或管理员的删除请求，正常执行
        return originalFetch.apply(this, arguments);
    };
    
    // 添加全局点击事件拦截
    document.addEventListener('click', async function(event) {
        // 查找可能触发删除操作的元素
        const deleteButton = event.target.closest('.btn-delete, [data-action="delete"], button[title="删除"]');
        if (deleteButton) {
            const isAdminUser = await isAdmin();
            if (!isAdminUser) {
                // 阻止事件传播
                event.preventDefault();
                event.stopPropagation();
                console.warn('权限不足，普通用户无法执行删除操作');
                
                // 显示提示消息
                if (typeof showToast === 'function') {
                    showToast('只有管理员可以删除数据', 'red');
                } else if (typeof showErrorMessage === 'function') {
                    showErrorMessage('权限不足，只有管理员可以删除数据');
                } else {
                    console.error('权限不足，只有管理员可以删除数据');
                }
                
                return false;
            }
        }
    }, true);
}

<!-- 移动端卡片布局辅助函数 -->
document.addEventListener('DOMContentLoaded', function() {
    // 检测是否为移动设备
    function isMobile() {
        return window.innerWidth < 640;
    }

    // 监听窗口大小变化，在切换设备宽度时重新渲染列表
    window.addEventListener('resize', debounce(function() {
        // 如果有GPU数据并且已加载JS文件，尝试重新渲染
        if (window.gpus && window.renderGpuTable) {
            renderGpuTable(window.gpus);
        }
    }, 250));

    // 防抖函数
    function debounce(func, wait) {
        let timeout;
        return function() {
            const context = this;
            const args = arguments;
            clearTimeout(timeout);
            timeout = setTimeout(() => func.apply(context, args), wait);
        };
    }

    // 为移动设备创建GPU卡片
    window.createGpuCard = function(gpu, index) {
        const cardContainer = document.createElement('div');
        cardContainer.className = 'gpu-card-outer-container';
        cardContainer.style.setProperty('--card-index', index);

        const card = document.createElement('div');
        card.className = 'gpu-card fade-in';

        // 卡片头部 - 型号和品牌
        const cardHeader = document.createElement('div');
        cardHeader.className = 'gpu-card-header';

        const modelText = document.createElement('div');
        modelText.className = 'gpu-model-text';
        modelText.style.fontSize = '0.8rem';
        modelText.textContent = gpu.model || '未知型号';

        const brandBadge = document.createElement('span');
        brandBadge.className = 'gpu-brand-badge';
        brandBadge.style.fontSize = '0.6rem';
        brandBadge.style.padding = '1px 3px';
        brandBadge.textContent = gpu.brand || '未知品牌';

        cardHeader.appendChild(modelText);
        cardHeader.appendChild(brandBadge);

        // 卡片主体 - 图片和规格
        const cardBody = document.createElement('div');
        cardBody.className = 'gpu-card-body';

        // 图片容器
        const imageContainer = document.createElement('div');
        imageContainer.className = 'gpu-card-image';
        imageContainer.style.width = '36px';
        imageContainer.style.height = '36px';

        // 如果有图片，显示图片；否则显示占位符
        if (gpu.image_url) {
            const img = document.createElement('img');
            img.src = gpu.image_url;
            img.alt = gpu.model || 'GPU图片';
            img.onerror = function() {
                this.style.display = 'none';
                imageContainer.textContent = 'GPU';
            };
            imageContainer.appendChild(img);
        } else {
            imageContainer.textContent = 'GPU';
            imageContainer.style.fontSize = '0.65rem';
            imageContainer.style.color = '#6b7280';
            imageContainer.style.display = 'flex';
            imageContainer.style.alignItems = 'center';
            imageContainer.style.justifyContent = 'center';
        }

        // 信息容器
        const infoContainer = document.createElement('div');
        infoContainer.className = 'gpu-card-info';

        // 芯片组信息
        if (gpu.chipset) {
            const chipsetText = document.createElement('div');
            chipsetText.className = 'gpu-chipset-text';
            chipsetText.style.fontSize = '0.75rem';
            chipsetText.textContent = gpu.chipset;
            infoContainer.appendChild(chipsetText);
        }

        // 显存和频率信息
        const memoryText = document.createElement('div');
        memoryText.className = 'gpu-info-text';
        memoryText.style.fontSize = '0.65rem';
        const memoryInfo = [];
        if (gpu.memory_size) memoryInfo.push(`${gpu.memory_size}GB`);
        if (gpu.memory_type) memoryInfo.push(gpu.memory_type);
        if (gpu.memory_bus) memoryInfo.push(`${gpu.memory_bus}位`);
        memoryText.textContent = memoryInfo.join(' ') || '显存信息未知';
        infoContainer.appendChild(memoryText);

        // 频率信息（可选，根据空间决定是否显示）
        if (gpu.core_clock || gpu.boost_clock) {
            const clockText = document.createElement('div');
            clockText.className = 'gpu-info-text';
            clockText.style.fontSize = '0.65rem';
            const clockInfo = [];
            if (gpu.core_clock) clockInfo.push(`核心: ${gpu.core_clock}MHz`);
            if (gpu.boost_clock) clockInfo.push(`Boost: ${gpu.boost_clock}MHz`);
            clockText.textContent = clockInfo.join(' / ');
            infoContainer.appendChild(clockText);
        }

        // 标签组
        const tagsContainer = document.createElement('div');
        tagsContainer.className = 'gpu-spec-tags';
        tagsContainer.style.marginTop = '1px';
        tagsContainer.style.gap = '2px';

        // 显存标签
        if (gpu.memory_size && gpu.memory_type) {
            const memoryTag = document.createElement('span');
            memoryTag.className = 'gpu-spec-tag gpu-memory-tag';
            memoryTag.style.fontSize = '0.55rem';
            memoryTag.style.padding = '0px 2px';
            memoryTag.textContent = `${gpu.memory_size}GB`;
            tagsContainer.appendChild(memoryTag);
        }

        // 功耗标签
        if (gpu.tdp) {
            const tdpTag = document.createElement('span');
            tdpTag.className = 'gpu-spec-tag gpu-power-tag';
            tdpTag.style.fontSize = '0.55rem';
            tdpTag.style.padding = '0px 2px';
            tdpTag.textContent = `${gpu.tdp}W`;
            tagsContainer.appendChild(tdpTag);
        }

        // 接口标签 - 只显示数量，不显示文本
        if (gpu.display_ports && gpu.hdmi_ports) {
            const portsTag = document.createElement('span');
            portsTag.className = 'gpu-spec-tag';
            portsTag.style.fontSize = '0.55rem';
            portsTag.style.padding = '0px 2px';
            portsTag.textContent = `${gpu.display_ports}/${gpu.hdmi_ports}`;
            tagsContainer.appendChild(portsTag);
        } else if (gpu.display_ports) {
            const portsTag = document.createElement('span');
            portsTag.className = 'gpu-spec-tag';
            portsTag.style.fontSize = '0.55rem';
            portsTag.style.padding = '0px 2px';
            portsTag.textContent = `DP:${gpu.display_ports}`;
            tagsContainer.appendChild(portsTag);
        } else if (gpu.hdmi_ports) {
            const portsTag = document.createElement('span');
            portsTag.className = 'gpu-spec-tag';
            portsTag.style.fontSize = '0.55rem';
            portsTag.style.padding = '0px 2px';
            portsTag.textContent = `HDMI:${gpu.hdmi_ports}`;
            tagsContainer.appendChild(portsTag);
        }

        // 位宽标签
        if (gpu.memory_bus) {
            const busTag = document.createElement('span');
            busTag.className = 'gpu-spec-tag';
            busTag.style.fontSize = '0.55rem';
            busTag.style.padding = '0px 2px';
            busTag.textContent = `${gpu.memory_bus}bit`;
            tagsContainer.appendChild(busTag);
        }

        // 只有当有标签时才添加标签容器
        if (tagsContainer.children.length > 0) {
            infoContainer.appendChild(tagsContainer);
        }

        cardBody.appendChild(imageContainer);
        cardBody.appendChild(infoContainer);

        // 卡片底部 - 操作按钮
        const cardFooter = document.createElement('div');
        cardFooter.className = 'gpu-card-footer';

        // 查看按钮
        const viewBtn = document.createElement('button');
        viewBtn.className = 'gpu-action-btn gpu-view-btn';
        viewBtn.innerHTML = '<i class="fas fa-eye mr-1"></i>查看';
        viewBtn.style.fontSize = '0.65rem';
        viewBtn.style.padding = '2px 6px';
        viewBtn.onclick = function() { viewGpuDetails(gpu.id); };

        // 编辑按钮
        const editBtn = document.createElement('button');
        editBtn.className = 'gpu-action-btn gpu-edit-btn';
        editBtn.innerHTML = '<i class="fas fa-edit mr-1"></i>编辑';
        editBtn.style.fontSize = '0.65rem';
        editBtn.style.padding = '2px 6px';
        editBtn.onclick = function() { editGpu(gpu.id); };

        // 删除按钮
        const deleteBtn = document.createElement('button');
        deleteBtn.className = 'gpu-action-btn gpu-delete-btn';
        deleteBtn.innerHTML = '<i class="fas fa-trash mr-1"></i>删除';
        deleteBtn.style.fontSize = '0.65rem';
        deleteBtn.style.padding = '2px 6px';
        deleteBtn.onclick = function() { confirmDelete(gpu.id); };

        cardFooter.appendChild(viewBtn);
        cardFooter.appendChild(editBtn);
        cardFooter.appendChild(deleteBtn);

        // 组装卡片
        card.appendChild(cardHeader);
        card.appendChild(cardBody);
        card.appendChild(cardFooter);
        cardContainer.appendChild(card);

        return cardContainer;
    };

    // 监听窗口大小变化，重新格式化芯片组选项
    window.addEventListener('resize', function() {
        formatChipsetOptions();
    });
});
