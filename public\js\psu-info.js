// 等待DOM加载完成
document.addEventListener('DOMContentLoaded', () => {
    // DOM元素引用
    const psuForm = document.getElementById('psuForm');
    const brandFilter = document.getElementById('brandFilter');
    const searchInput = document.getElementById('searchInput');
    const wattageFilter = document.getElementById('wattageFilter');
    const psuTableBody = document.getElementById('psuTableBody');
    const prevBtn = document.getElementById('prevBtn');
    const nextBtn = document.getElementById('nextBtn');
    const pageInfo = document.getElementById('pageInfo');
    const currentPageDisplay = document.getElementById('currentPageDisplay');
    const totalPagesDisplay = document.getElementById('totalPagesDisplay');
    const totalCount = document.getElementById('totalCount');
    const totalRecords = document.getElementById('totalRecords');
    const detailsModal = document.getElementById('detailsModal');
    const deleteConfirmModal = document.getElementById('deleteConfirmModal');
    const resetFormBtn = document.getElementById('resetFormBtn');
    const imageInput = document.getElementById('image');
    const imagePreview = document.getElementById('imagePreview');
    const previewImage = document.getElementById('previewImage');
    const smartInput = document.getElementById('smartInput');
    const autoFillBtn = document.getElementById('autoFillBtn');

    // 默认图片
    const DEFAULT_PSU_IMAGE = '/images/default-psu.svg';

    // 全局变量
    let psus = [];
    let currentPage = 1;
    let pageSize = 10;
    let currentPsuId = null;
    let totalItems = 0;
    const maxPageButtons = 5; // 最多显示的页码按钮数

    // 检测主题模式
    function isDarkMode() {
        return document.documentElement.classList.contains('dark');
    }

    // 初始化
    async function init() {
        // 首先设置基于权限的UI
        if (typeof setupPermissionBasedUI === 'function') {
            await setupPermissionBasedUI(); // 等待权限模块初始化完成
        }

        const isAdminUser = await isAdmin();

        // 根据权限禁用表单
        if (!isAdminUser) {
            if (psuForm) {
                const inputs = psuForm.querySelectorAll('input, select, textarea');
                inputs.forEach(input => {
                    input.disabled = true;
                });
                const buttons = psuForm.querySelectorAll('button');
                buttons.forEach(button => {
                    button.disabled = true;
                    button.title = '只有管理员可以操作';
                });
            }
            // 禁用智能填充按钮
            if (autoFillBtn) {
                autoFillBtn.disabled = true;
                autoFillBtn.title = '只有管理员可以操作';
            }
        }

        fetchPsus();
        initEventListeners();
    }

    // 初始化事件监听器
    function initEventListeners() {
        // 表单提交
        if (psuForm) {
            psuForm.addEventListener('submit', handleFormSubmit);
        }

        // 智能识别按钮
        if (autoFillBtn) {
            autoFillBtn.addEventListener('click', parsePsuInfo);
        }

        // 详情模态框关闭按钮
        const closeDetailsModal = document.getElementById('closeDetailsModal');
        if (closeDetailsModal) {
            closeDetailsModal.addEventListener('click', () => {
                if (detailsModal) {
                    detailsModal.classList.add('hidden');
                }
            });
        }

        // 搜索和过滤
        if (searchInput) {
            searchInput.addEventListener('input', debounce(() => {
                currentPage = 1;
                fetchPsus();
            }, 300));
        }

        if (brandFilter) {
            brandFilter.addEventListener('change', filterPsus);
        }

        if (wattageFilter) {
            wattageFilter.addEventListener('change', filterPsus);
        }

        // 添加窗口大小变化监听，以支持响应式布局
        window.addEventListener('resize', debounce(() => {
            console.log('Window resized, re-rendering PSU table');
            // 重新渲染表格布局，但不重新获取数据
            displayPsus();
        }, 200));

        // 监听主题变化
        document.addEventListener('themeChanged', () => {
            // 当主题变化时重新渲染表格以适应新主题
            displayPsus();
        });

        // 重置过滤器按钮
        const resetFiltersBtn = document.getElementById('resetFilters');
        if (resetFiltersBtn) {
            resetFiltersBtn.addEventListener('click', () => {
                if (searchInput) searchInput.value = '';
                if (brandFilter) brandFilter.value = '';
                if (wattageFilter) wattageFilter.value = '';

                // 重置到第一页并重新加载数据
                currentPage = 1;
                fetchPsus();
            });
        }

        // 分页按钮
        if (prevBtn) {
            prevBtn.addEventListener('click', () => {
                if (currentPage > 1) {
                    currentPage--;
                    fetchPsus();
                }
            });
        }

        if (nextBtn) {
            nextBtn.addEventListener('click', () => {
                currentPage++;
                fetchPsus();
            });
        }

        // 首页和尾页按钮
        const firstPageBtn = document.getElementById('firstPage');
        if (firstPageBtn) {
            firstPageBtn.addEventListener('click', () => {
                if (currentPage > 1) {
                    currentPage = 1;
                    fetchPsus();
                }
            });
        }

        const lastPageBtn = document.getElementById('lastPage');
        if (lastPageBtn) {
            lastPageBtn.addEventListener('click', () => {
                const totalPages = Math.ceil(totalItems / pageSize) || 1;
                if (currentPage < totalPages) {
                    currentPage = totalPages;
                    fetchPsus();
                }
            });
        }

        // 页码跳转
        const goToPageBtn = document.getElementById('goToPage');
        if (goToPageBtn) {
            goToPageBtn.addEventListener('click', () => {
                const pageJumpInput = document.getElementById('pageJump');
                if (pageJumpInput) {
                    let pageNum = parseInt(pageJumpInput.value);
                    const totalPages = Math.ceil(totalItems / pageSize) || 1;

                    if (isNaN(pageNum) || pageNum < 1) {
                        pageNum = 1;
                    } else if (pageNum > totalPages) {
                        pageNum = totalPages;
                    }

                    if (pageNum !== currentPage) {
                        currentPage = pageNum;
                        fetchPsus();
                    }

                    pageJumpInput.value = '';
                }
            });
        }

        // 监听页码输入框的回车事件
        const pageJumpInput = document.getElementById('pageJump');
        if (pageJumpInput) {
            pageJumpInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    const goToPageBtn = document.getElementById('goToPage');
                    if (goToPageBtn) {
                        goToPageBtn.click();
                    }
                }
            });
        }

        // 表格行按钮事件委托
        if (psuTableBody) {
            psuTableBody.addEventListener('click', (e) => {
                // 处理图片点击放大
                const img = e.target.closest('img');
                if (img && !img.classList.contains('no-zoom')) {
                    // 使用 Viewer.js 打开图片，不再使用旧的 zoomedImage
                    openImageFullscreen(img.src);
                    return;
                }

                const target = e.target.closest('button');
                if (!target) return;

                const id = target.dataset.id;
                if (!id) return;

                if (target.classList.contains('view-btn')) {
                    viewPsuDetails(id);
                } else if (target.classList.contains('edit-btn')) {
                    populateForm(id);
                } else if (target.classList.contains('delete-btn')) {
                    currentPsuId = id;
                    if (deleteConfirmModal) {
                        deleteConfirmModal.classList.remove('hidden');
                    }
                }
            });
        }

        // 删除确认
        const confirmDeleteBtn = document.getElementById('confirmDeleteBtn');
        if (confirmDeleteBtn) {
            confirmDeleteBtn.addEventListener('click', handleDelete);
        }

        const cancelDeleteBtn = document.getElementById('cancelDeleteBtn');
        if (cancelDeleteBtn) {
            cancelDeleteBtn.addEventListener('click', () => {
                if (deleteConfirmModal) {
                    deleteConfirmModal.classList.add('hidden');
                }
            });
        }

        // 图片上传预览
        if (imageInput) {
            imageInput.addEventListener('change', (e) => {
                const file = e.target.files[0];
                if (!file) return;

                // 验证文件大小和类型
                if (file.size > 5 * 1024 * 1024) { // 5MB
                    alert('图片大小不能超过5MB');
                    imageInput.value = '';
                    return;
                }

                if (!['image/jpeg', 'image/png', 'image/gif'].includes(file.type)) {
                    alert('只能上传JPG、PNG或GIF格式的图片');
                    imageInput.value = '';
                    return;
                }

                // 显示转换提示
                const uploadHint = imageInput.nextElementSibling;
                if (uploadHint && !uploadHint.querySelector('.upload-notification')) {
                    const notification = document.createElement('div');
                    notification.className = 'upload-notification mt-2 text-xs text-indigo-600 dark:text-indigo-400 flex items-center';
                    notification.innerHTML = '<i class="fas fa-sync fa-spin mr-1"></i>图片上传后将自动转换为WebP格式以优化加载速度 (文件大小: ' +
                        (file.size / 1024).toFixed(1) + 'KB)';
                    uploadHint.appendChild(notification);
                }

                // 预览图片
                const reader = new FileReader();
                reader.onload = (e) => {
                    if (previewImage) {
                        previewImage.src = e.target.result;

                        // 添加点击预览功能
                        previewImage.onclick = () => {
                            openImageFullscreen(e.target.result);
                        };
                        previewImage.style.cursor = 'pointer';
                        previewImage.title = '点击查看大图';
                    }
                    if (imagePreview) {
                        imagePreview.classList.remove('hidden');
                    }
                };
                reader.readAsDataURL(file);
            });
        }

        // 重置表单
        if (resetFormBtn) {
            resetFormBtn.addEventListener('click', resetForm);
        }
    }

    // 获取电源数据
    async function fetchPsus() {
        try {
            // 构建查询参数
            const params = new URLSearchParams();
            params.append('page', currentPage);
            params.append('limit', pageSize);

            // 添加搜索和过滤条件
            if (searchInput && searchInput.value.trim()) {
                params.append('search', searchInput.value.trim());
            }

            if (brandFilter && brandFilter.value) {
                params.append('brand', brandFilter.value);
            }

            if (wattageFilter && wattageFilter.value) {
                params.append('wattage', wattageFilter.value);
            }

            const [response, isAdminUser] = await Promise.all([
                fetchWithAuth(`/api/psus?${params.toString()}`),
                isAdmin()
            ]);

            if (!response || !response.ok) {
                throw new Error(`服务器返回错误: ${response.status}`);
            }

            const data = await response.json();

            // 处理不同的API响应格式
            if (Array.isArray(data)) {
                psus = data;
                totalItems = data.length;
            } else if (data.psus && Array.isArray(data.psus)) {
                psus = data.psus;
                totalItems = data.total || 0;
            } else if (data.data && Array.isArray(data.data)) {
                psus = data.data;
                totalItems = data.total || 0;
            } else {
                console.error('未知的API响应格式:', data);
                psus = [];
                totalItems = 0;
            }

            // 更新品牌过滤器选项
            updateBrandFilter();

            // 显示结果
            displayPsus(isAdminUser);
        } catch (error) {
            console.error('获取电源数据失败:', error);
            showToast('获取电源数据失败: ' + error.message, 'error');

            // 在错误情况下清空表格并更新分页
            if (psuTableBody) {
                psuTableBody.innerHTML = `
                    <tr>
                        <td colspan="7" class="px-4 py-4 text-center text-gray-500">
                            <div class="flex flex-col items-center">
                                <i class="fas fa-exclamation-circle text-red-500 text-2xl mb-2"></i>
                                <p>加载数据失败</p>
                                <p class="text-sm mt-1">错误: ${error.message}</p>
                            </div>
                        </td>
                    </tr>
                `;
            }

            updatePaginationUI(0, 0);
        }
    }

    // 更新品牌过滤器选项
    function updateBrandFilter() {
        if (!brandFilter) return;

        // 清空现有选项（保留第一个"所有品牌"选项）
        while (brandFilter.options.length > 1) {
            brandFilter.remove(1);
        }

        // 提取不同的品牌并排序
        if (Array.isArray(psus) && psus.length > 0) {
            const brands = [...new Set(psus.filter(psu => psu && psu.brand).map(psu => psu.brand))].sort();

            // 添加选项
            brands.forEach(brand => {
                if (brand) {
                    const option = document.createElement('option');
                    option.value = brand;
                    option.textContent = brand;
                    brandFilter.appendChild(option);
                }
            });
        }
    }

    // 获取品牌徽章样式
    function getBrandBadgeStyle() {
        if (isDarkMode()) {
            return {
                backgroundColor: 'rgba(59, 130, 246, 0.2)',
                color: '#60a5fa',
                border: '1px solid rgba(59, 130, 246, 0.4)'
            };
        } else {
            return {
                backgroundColor: 'rgba(59, 130, 246, 0.1)',
                color: '#3B82F6',
                border: '1px solid rgba(59, 130, 246, 0.2)'
            };
        }
    }

    // 获取按钮悬停样式
    function getButtonHoverStyle(color) {
        const baseColor = color || '#3B82F6';
        const darkModeOpacity = isDarkMode() ? '0.2' : '0.1';
        return `background-color: ${baseColor}${darkModeOpacity};`;
    }

    // 显示当前页的电源数据
    function displayPsus(isAdminUser) {
        if (!psuTableBody) return;

        // 清空表格
        psuTableBody.innerHTML = '';

        // 处理没有数据的情况
        if (!Array.isArray(psus) || psus.length === 0) {
            const emptyRow = document.createElement('tr');
            emptyRow.innerHTML = `<td colspan="7" class="px-4 py-4 text-center text-gray-500">没有找到电源数据</td>`;
            psuTableBody.appendChild(emptyRow);

            // 更新分页显示
            updatePaginationUI(0, 0);
            return;
        }

        // 检测是否为移动设备
        const isMobile = window.innerWidth < 640;

        // 根据设备类型选择渲染方法
        if (isMobile) {
            renderMobileCards(psus, isAdminUser);
            return;
        }

        // PC端表格布局 - 卡片式设计
        const tableElement = psuTableBody.closest('table');
        if (tableElement) {
            tableElement.classList.add('card-style');
        }

        // 显示当前页的数据
        psus.forEach((psu, index) => {
            // 创建行元素并添加淡入动画
            const row = document.createElement('tr');
            row.className = 'fade-in hover:bg-gray-50 transition-colors';
            row.style.animation = 'fadeIn 0.3s ease-in-out';
            row.style.animationFillMode = 'both';
            row.style.animationDelay = `${index * 0.05}s`;

            // 使用默认图片或实际图片
            const imageUrl = psu.image_url || DEFAULT_PSU_IMAGE;
            const isDefaultImage = !psu.image_url;

            // 格式化功率显示
            const wattageDisplay = psu.wattage ? `${psu.wattage}W` : '-';

            // 格式化价格显示
            const priceDisplay = psu.price ? `¥${parseFloat(psu.price).toFixed(2)}` : '-';

            // 图片和型号合并为一个单元格
            const modelCell = document.createElement('td');
            modelCell.className = 'px-2 py-3';

            const modelContainer = document.createElement('div');
            modelContainer.className = 'flex items-center';

            // 图片容器
            const imgContainer = document.createElement('div');
            imgContainer.className = 'w-10 h-10 flex-shrink-0 mr-2 relative group';

            const imgElement = document.createElement('img');
            imgElement.src = imageUrl;
            imgElement.alt = psu.model || '电源';
            imgElement.className = `w-10 h-10 object-contain rounded-md border border-gray-200 ${isDefaultImage ? 'opacity-60' : ''} transition-transform group-hover:scale-110`;
            imgElement.onclick = () => openImageFullscreen(imageUrl);

            // 添加图片悬停效果
            const viewOverlay = document.createElement('div');
            viewOverlay.className = 'absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 rounded-md flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity';
            viewOverlay.innerHTML = '<i class="fas fa-search-plus text-white text-opacity-80"></i>';
            viewOverlay.onclick = () => openImageFullscreen(imageUrl);

            imgContainer.appendChild(imgElement);
            imgContainer.appendChild(viewOverlay);
            modelContainer.appendChild(imgContainer);

            // 型号信息容器
            const infoContainer = document.createElement('div');
            infoContainer.className = 'overflow-hidden';

            const modelText = document.createElement('div');
            modelText.className = 'text-sm font-medium text-gray-900 truncate';
            modelText.textContent = psu.model || '-';
            infoContainer.appendChild(modelText);

            // 移动端显示的品牌信息
            const brandMobileText = document.createElement('div');
            brandMobileText.className = 'text-xs text-gray-500 mt-1 sm:hidden';
            brandMobileText.textContent = psu.brand || '-';
            infoContainer.appendChild(brandMobileText);

            modelContainer.appendChild(infoContainer);
            modelCell.appendChild(modelContainer);
            row.appendChild(modelCell);

            // 品牌单元格
            const brandCell = document.createElement('td');
            brandCell.className = 'px-2 py-3 hidden sm:table-cell';

            const brandBadge = document.createElement('span');
            brandBadge.className = 'psu-badge';
            brandBadge.textContent = psu.brand || '-';

            // 根据主题设置样式
            const badgeStyle = getBrandBadgeStyle();
            brandBadge.style.backgroundColor = badgeStyle.backgroundColor;
            brandBadge.style.color = badgeStyle.color;
            brandBadge.style.border = badgeStyle.border;

            brandCell.appendChild(brandBadge);
            row.appendChild(brandCell);

            // 规格单元格
            const specCell = document.createElement('td');
            specCell.className = 'px-2 py-3 hidden md:table-cell';

            const specTag = document.createElement('span');
            specTag.className = 'spec-tag modular';
            specTag.textContent = psu.psu_length || '-';
            specCell.appendChild(specTag);
            row.appendChild(specCell);

            // 功率单元格
            const wattageCell = document.createElement('td');
            wattageCell.className = 'px-2 py-3';

            const wattageContainer = document.createElement('div');
            wattageContainer.className = 'flex flex-col';

            const wattageText = document.createElement('div');
            wattageText.className = 'text-sm text-gray-900';

            const wattageTag = document.createElement('span');
            wattageTag.className = 'spec-tag wattage';
            wattageTag.innerHTML = `<i class="fas fa-bolt mr-1"></i>${wattageDisplay}`;
            wattageText.appendChild(wattageTag);
            wattageContainer.appendChild(wattageText);

            // 移动端显示的模块化类型
            const moduleMobileText = document.createElement('div');
            moduleMobileText.className = 'text-xs text-gray-500 sm:hidden mt-1';
            moduleMobileText.textContent = getModularType(psu.modular) || '-';
            wattageContainer.appendChild(moduleMobileText);

            wattageCell.appendChild(wattageContainer);
            row.appendChild(wattageCell);

            // 认证单元格
            const efficiencyCell = document.createElement('td');
            efficiencyCell.className = 'px-2 py-3 hidden sm:table-cell';

            if (psu.efficiency) {
                const efficiencyTag = document.createElement('span');
                efficiencyTag.className = 'spec-tag efficiency';
                efficiencyTag.textContent = psu.efficiency;
                efficiencyCell.appendChild(efficiencyTag);
            } else {
                efficiencyCell.textContent = '-';
            }

            row.appendChild(efficiencyCell);

            // 价格单元格
            const priceCell = document.createElement('td');
            priceCell.className = 'px-2 py-3 hidden md:table-cell';

            const priceText = document.createElement('div');
            priceText.className = 'text-sm text-gray-900 font-medium';
            priceText.textContent = priceDisplay;
            priceCell.appendChild(priceText);
            row.appendChild(priceCell);

            // 操作单元格
            const actionCell = document.createElement('td');
            actionCell.className = 'px-2 py-3 text-center';

            const actionContainer = document.createElement('div');
            actionContainer.className = 'flex justify-center space-x-1';

            // 查看按钮
            const viewButton = document.createElement('button');
            viewButton.className = 'p-1 text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded-full w-7 h-7 flex items-center justify-center transition-all';
            viewButton.innerHTML = '<i class="fas fa-eye"></i>';
            viewButton.title = '查看详情';
            viewButton.dataset.id = psu.id;
            viewButton.addEventListener('click', () => viewPsuDetails(psu.id));
            viewButton.addEventListener('mouseenter', function () {
                if (isDarkMode()) {
                    this.style.backgroundColor = '#2a2a2a';
                    this.style.transform = 'translateY(-1px)';
                } else {
                    this.style.backgroundColor = '#60a5fa10';
                    this.style.transform = 'translateY(-1px)';
                }
            });
            viewButton.addEventListener('mouseleave', function () {
                if (isDarkMode()) {
                    this.style.backgroundColor = '#1a1a1a';
                    this.style.transform = 'translateY(0)';
                } else {
                    this.style.backgroundColor = 'white';
                    this.style.transform = 'translateY(0)';
                }
            });
            actionContainer.appendChild(viewButton);

            if (isAdminUser) {
                // 编辑按钮
                const editButton = document.createElement('button');
                editButton.className = 'p-1 text-green-600 hover:text-green-800 hover:bg-green-50 rounded-full w-7 h-7 flex items-center justify-center transition-all edit-btn';
                editButton.innerHTML = '<i class="fas fa-edit"></i>';
                editButton.title = '编辑';
                editButton.dataset.id = psu.id;
                editButton.addEventListener('click', () => populateForm(psu.id));
                editButton.addEventListener('mouseenter', function () {
                    if (isDarkMode()) {
                        this.style.backgroundColor = '#2a2a2a';
                        this.style.transform = 'translateY(-1px)';
                    } else {
                        this.style.backgroundColor = '#34d39910';
                        this.style.transform = 'translateY(-1px)';
                    }
                });
                editButton.addEventListener('mouseleave', function () {
                    if (isDarkMode()) {
                        this.style.backgroundColor = '#1a1a1a';
                        this.style.transform = 'translateY(0)';
                    } else {
                        this.style.backgroundColor = 'white';
                        this.style.transform = 'translateY(0)';
                    }
                });
                actionContainer.appendChild(editButton);

                // 删除按钮
                const deleteButton = document.createElement('button');
                deleteButton.className = 'p-1 text-red-600 hover:text-red-800 hover:bg-red-50 rounded-full w-7 h-7 flex items-center justify-center transition-all delete-btn';
                deleteButton.innerHTML = '<i class="fas fa-trash"></i>';
                deleteButton.title = '删除';
                deleteButton.dataset.id = psu.id;
                deleteButton.addEventListener('click', () => {
                    currentPsuId = psu.id;
                    if (deleteConfirmModal) {
                        deleteConfirmModal.classList.remove('hidden');
                    }
                });
                deleteButton.addEventListener('mouseenter', function () {
                    if (isDarkMode()) {
                        this.style.backgroundColor = '#2a2a2a';
                        this.style.transform = 'translateY(-1px)';
                    } else {
                        this.style.backgroundColor = '#f8717110';
                        this.style.transform = 'translateY(-1px)';
                    }
                });
                deleteButton.addEventListener('mouseleave', function () {
                    if (isDarkMode()) {
                        this.style.backgroundColor = '#1a1a1a';
                        this.style.transform = 'translateY(0)';
                    } else {
                        this.style.backgroundColor = 'white';
                        this.style.transform = 'translateY(0)';
                    }
                });
                actionContainer.appendChild(deleteButton);
            }

            actionCell.appendChild(actionContainer);
            row.appendChild(actionCell);

            psuTableBody.appendChild(row);
        });

        // 更新分页显示
        updatePaginationUI(psus.length, totalItems);
    }

    // 为表格行按钮添加事件监听
    function attachTableEventListeners() {
        // 处理查看按钮点击
        document.querySelectorAll('.view-btn').forEach(btn => {
            btn.addEventListener('click', function () {
                const id = this.getAttribute('data-id');
                viewPsuDetails(id);
            });
        });

        // 处理编辑按钮点击
        document.querySelectorAll('.edit-btn').forEach(btn => {
            btn.addEventListener('click', function () {
                const id = this.getAttribute('data-id');
                populateForm(id);
            });
        });

        // 处理删除按钮点击
        document.querySelectorAll('.delete-btn').forEach(btn => {
            btn.addEventListener('click', function () {
                const id = this.getAttribute('data-id');
                currentPsuId = id;
                if (deleteConfirmModal) {
                    deleteConfirmModal.classList.remove('hidden');
                }
            });
        });
    }

    // 更新分页UI
    function updatePaginationUI(currentCount, total) {
        // 更新记录总数
        if (totalRecords) totalRecords.textContent = total;
        totalItems = total;

        // 计算总页数
        const totalPages = Math.ceil(total / pageSize) || 1;

        // 更新当前页和总页数显示
        if (currentPageDisplay) currentPageDisplay.textContent = currentPage;
        if (totalPagesDisplay) totalPagesDisplay.textContent = totalPages;

        // 更新分页按钮状态
        if (prevBtn) {
            prevBtn.disabled = currentPage <= 1;
            prevBtn.classList.toggle('opacity-30', currentPage <= 1);
        }

        if (nextBtn) {
            nextBtn.disabled = currentPage >= totalPages;
            nextBtn.classList.toggle('opacity-30', currentPage >= totalPages);
        }

        // 更新首页和尾页按钮状态
        const firstPageBtn = document.getElementById('firstPage');
        if (firstPageBtn) {
            firstPageBtn.disabled = currentPage <= 1;
            firstPageBtn.classList.toggle('opacity-30', currentPage <= 1);
        }

        const lastPageBtn = document.getElementById('lastPage');
        if (lastPageBtn) {
            lastPageBtn.disabled = currentPage >= totalPages;
            lastPageBtn.classList.toggle('opacity-30', currentPage >= totalPages);
        }

        // 设置页码跳转输入框的最大值
        const pageJumpInput = document.getElementById('pageJump');
        if (pageJumpInput) {
            pageJumpInput.max = totalPages;
        }

        // 生成页码按钮
        const pageNumbers = document.getElementById('pageNumbers');
        if (pageNumbers) {
            pageNumbers.innerHTML = '';

            // 确定要显示的页码范围
            let startPage = Math.max(1, currentPage - Math.floor(maxPageButtons / 2));
            let endPage = startPage + maxPageButtons - 1;

            if (endPage > totalPages) {
                endPage = totalPages;
                startPage = Math.max(1, endPage - maxPageButtons + 1);
            }

            // 生成页码按钮
            for (let i = startPage; i <= endPage; i++) {
                const pageButton = document.createElement('button');
                pageButton.type = 'button';
                pageButton.className = `px-3 py-1 border rounded-md text-sm ${i === currentPage
                        ? 'bg-gray-600 text-white border-gray-600'
                        : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
                    }`;
                pageButton.textContent = i;
                pageButton.addEventListener('click', () => {
                    if (i !== currentPage) {
                        currentPage = i;
                        fetchPsus();
                    }
                });

                pageNumbers.appendChild(pageButton);
            }
        }
    }

    // 获取效率等级的样式类
    function getEfficiencyClass(efficiency) {
        if (!efficiency) return '';

        if (efficiency.includes('Titanium')) {
            return 'text-purple-700';
        } else if (efficiency.includes('Platinum')) {
            return 'text-indigo-700';
        } else if (efficiency.includes('Gold')) {
            return 'text-yellow-700';
        } else if (efficiency.includes('Silver')) {
            return 'text-gray-700';
        } else if (efficiency.includes('Bronze')) {
            return 'text-amber-700';
        }
        return '';
    }

    // 获取模块化类型的中文描述
    function getModularType(modular) {
        if (!modular) return '-';

        switch (modular) {
            case 'Full': return '全模组';
            case 'Semi': return '半模组';
            case 'No': return '非模组';
            default: return modular;
        }
    }

    // 过滤电源数据
    function filterPsus() {
        currentPage = 1; // 重置到第一页
        fetchPsus();
    }

    // 防抖函数
    function debounce(func, delay) {
        let timeout;
        return function () {
            const context = this;
            const args = arguments;
            clearTimeout(timeout);
            timeout = setTimeout(() => func.apply(context, args), delay);
        };
    }

    // 搜索处理函数已经在初始化事件监听器中使用debounce进行了包装
    function handleSearch() {
        currentPage = 1;
        fetchPsus();
    }

    // 表单提交处理
    async function handleFormSubmit(event) {
        event.preventDefault();

        const isAdminUser = await isAdmin();
        if (!isAdminUser) {
            showToast('只有管理员可以提交数据', 'error');
            return;
        }

        const formData = new FormData(psuForm);

        // 检查必填字段
        const brand = formData.get('brand');
        const model = formData.get('model');
        const wattage = formData.get('wattage');

        if (!brand || !model || !wattage) {
            showToast('请填写必填字段：品牌、型号和功率', 'error');
            return;
        }

        // 添加图片处理提示
        const uploadedImageFile = formData.get('image');
        if (uploadedImageFile && uploadedImageFile.size > 0) {
            console.log('[DEBUG] 正在上传图片:', uploadedImageFile.name, '类型:', uploadedImageFile.type, '大小:', (uploadedImageFile.size / 1024).toFixed(1) + 'KB', '(将自动转换为WebP格式)');

            // 更新上传提示
            const uploadHint = document.querySelector('.upload-notification');
            if (uploadHint) {
                uploadHint.innerHTML = '<i class="fas fa-cloud-upload-alt mr-1"></i>图片正在上传并转换为WebP格式...';
            }
        }

        // 检查是否有图片上传
        const imageFile = formData.get('image');
        if (!imageFile || imageFile.size === 0) {
            // 如果没有上传新图片且是更新操作，则移除图片字段以保留原图片
            if (currentPsuId) {
                formData.delete('image');
            } else {
                // 如果是新增操作，使用默认图片
                formData.append('use_default_image', 'true');
            }
        }

        try {
            const url = currentPsuId ? `/api/psus/${currentPsuId}` : '/api/psus';
            const method = currentPsuId ? 'PUT' : 'POST';

            // 显示进度条并禁用提交按钮
            showUploadProgress();
            disableSubmitButton(true);

            // 使用XMLHttpRequest来支持上传进度
            const responseData = await uploadWithProgress(url, method, formData);

            // 重置表单和状态
            resetForm();

            // 显示成功消息
            showToast(`电源信息${currentPsuId ? '更新' : '添加'}成功`, 'success');

            // 保存当前ID以便刷新数据
            const updatedId = currentPsuId || responseData.id;
            currentPsuId = null;

            // 重新获取数据
            await fetchPsus();
        } catch (error) {
            console.error('保存电源信息时出错:', error);
            showToast('保存电源信息时出错: ' + error.message, 'error');
        } finally {
            // 隐藏进度条并恢复提交按钮
            hideUploadProgress();
            disableSubmitButton(false);
        }
    }

    // 重置表单
    function resetForm() {
        if (psuForm) {
            psuForm.reset();
            currentPsuId = null;

            // 重置图片预览
            if (imagePreview) {
                imagePreview.classList.add('hidden');
            }

            // 重置表单标题
            const submitButton = psuForm.querySelector('button[type="submit"]');
            if (submitButton) {
                submitButton.innerHTML = '<i class="fas fa-save mr-1"></i> 保存电源信息';
            }
        }
    }

    // 查看电源详情
    async function viewPsuDetails(id) {
        try {
            const [response, isAdminUser] = await Promise.all([
                fetchWithAuth(`/api/psus/${id}`, { cache: 'no-cache' }),
                isAdmin()
            ]);

            if (!response || !response.ok) {
                throw new Error(`服务器返回错误: ${response.status}`);
            }

            const psu = await response.json();

            // 使用默认图片或实际图片
            const imageUrl = psu.image_url || DEFAULT_PSU_IMAGE;

            // 填充详情模态框
            if (detailsModal) {
                // 保存产品ID到模态框数据属性，以便主题切换时可以重新加载
                detailsModal.dataset.psuId = id;

                // 获取当前是否为深色模式
                const isDark = isDarkMode();

                // 根据主题模式设置样式变量
                const textBaseColor = isDark ? '#e0e0e0' : '#1a202c';
                const textSecondaryColor = isDark ? '#b0b0b0' : '#4b5563';
                const bgPrimary = isDark ? '#1f2937' : 'white';
                const bgSecondary = isDark ? '#111827' : '#f8f9fa';
                const borderColor = isDark ? '#374151' : '#e5e7eb';

                // 功率区块样式
                const wattageBlockBg = isDark ? 'bg-gray-700' : 'bg-blue-50';
                const wattageTextColor = isDark ? 'text-blue-300' : 'text-blue-700';

                // 模块化区块样式
                const moduleBlockBg = isDark ? 'bg-gray-700' : 'bg-green-50';
                const moduleTextColor = isDark ? 'text-green-300' : 'text-green-700';

                // 卡片样式
                const cardBg = isDark ? '#1f2937' : 'white';
                const cardBorder = isDark ? '#374151' : '#e5e7eb';
                const cardHeaderColor = isDark ? 'text-blue-300' : 'text-blue-700';

                // 1. Construct the details HTML using the fetched psu data
                const detailsHTML = `
                        <!-- 主要信息区：图片和重要信息并排显示 -->
                        <div class="flex flex-col md:flex-row gap-6">
                            <!-- 左侧电源图片 - 垂直居中 -->
                            <div class="w-full md:w-1/3 flex items-center justify-center">
                                <div class="bg-${isDark ? 'gray-700' : 'gray-50'} p-2 rounded-lg shadow-md h-full flex items-center justify-center">
                                    ${imageUrl ?
                        `<img src="${imageUrl}" alt="${psu.model}" class="w-full rounded-lg cursor-pointer hover:opacity-90" onclick="openImageFullscreen('${imageUrl}')" style="max-height: 220px; object-fit: contain;">` :
                        `<div class="text-center text-gray-400">
                                            <i class="fas fa-plug text-5xl mb-2"></i>
                                            <p>暂无图片</p>
                                        </div>`
                    }
                                </div>
                            </div>
                            
                            <!-- 右侧重要信息 -->
                            <div class="md:w-2/3">
                                <!-- 标题和价格区域 -->
                                <div class="border-b ${isDark ? 'border-gray-600' : 'border-gray-200'} pb-3 mb-4">
                                    <h2 class="text-xl font-bold ${isDark ? 'text-gray-100' : 'text-gray-800'} mb-1">${psu.model || '未知型号'}</h2>
                                    <div class="flex items-center justify-between">
                                        <p class="${isDark ? 'text-gray-300' : 'text-gray-600'}">${psu.brand || '-'} ${psu.wattage ? `- ${psu.wattage}W` : ''}</p>
                                        <p class="text-xl font-bold ${isDark ? 'text-red-400' : 'text-red-600'}">${psu.price ? `¥${psu.price}` : '价格未知'}</p>
                                    </div>
                                </div>
                                
                                <!-- 关键信息区域 -->
                                <div class="grid grid-cols-2 gap-4 mb-4">
                                    <div class="${wattageBlockBg} p-3 rounded-lg">
                                        <h4 class="font-semibold text-md mb-2 ${wattageTextColor}">功率</h4>
                                        <p class="text-2xl font-bold ${wattageTextColor}">${psu.wattage ? `${psu.wattage}W` : '-'}</p>
                                        <p class="text-sm ${isDark ? 'text-gray-400' : 'text-gray-600'}">认证: ${psu.efficiency || '-'}</p>
                                    </div>
                                    <div class="${moduleBlockBg} p-3 rounded-lg">
                                        <h4 class="font-semibold text-md mb-2 ${moduleTextColor}">模块化</h4>
                                        <p class="text-2xl font-bold ${moduleTextColor}">${getModularType(psu.modular) || '-'}</p>
                                        <p class="text-sm ${isDark ? 'text-gray-400' : 'text-gray-600'}">规格: ${psu.psu_length || '-'}</p>
                                    </div>
                        </div>
                        
                                <!-- 次要信息 -->
                                <div class="grid grid-cols-2 gap-4 mb-4">
                            <div>
                                        <h5 class="text-sm font-medium ${isDark ? 'text-gray-300' : 'text-gray-700'}">风扇尺寸</h5>
                                        <p class="font-semibold ${isDark ? 'text-gray-100' : ''}">${psu.fan_size ? `${psu.fan_size}mm` : '-'}</p>
                            </div>
                            <div>
                                        <h5 class="text-sm font-medium ${isDark ? 'text-gray-300' : 'text-gray-700'}">物理长度</h5>
                                        <p class="font-semibold ${isDark ? 'text-gray-100' : ''}">${psu.physical_length ? `${psu.physical_length}mm` : '-'}</p>
                            </div>
                            </div>
                            </div>
                            </div>

                        <!-- 分隔线 -->
                        <div class="border-t ${isDark ? 'border-gray-600' : 'border-gray-200'} my-6"></div>
                        
                        <!-- 详细规格区域 -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                            <div class="border ${isDark ? 'border-gray-600' : 'border-gray-200'} rounded-lg p-4 ${isDark ? 'bg-gray-700' : 'bg-white'} shadow-sm">
                                <h4 class="font-semibold text-lg mb-2 ${isDark ? 'text-blue-300' : 'text-blue-700'} border-b ${isDark ? 'border-gray-600' : 'border-gray-200'} pb-2">基本信息</h4>
                                <div class="space-y-2 ${isDark ? 'text-gray-300' : ''}">
                                    <p><span class="font-medium">型号:</span> ${psu.model || '-'}</p>
                                    <p><span class="font-medium">品牌:</span> ${psu.brand || '-'}</p>
                                    <p><span class="font-medium">功率:</span> ${psu.wattage ? `${psu.wattage}W` : '-'}</p>
                                    <p><span class="font-medium">能效认证:</span> ${psu.efficiency || '-'}</p>
                                    <p><span class="font-medium">模块化类型:</span> ${getModularType(psu.modular) || '-'}</p>
                            </div>
                            </div>
                            <div class="border ${isDark ? 'border-gray-600' : 'border-gray-200'} rounded-lg p-4 ${isDark ? 'bg-gray-700' : 'bg-white'} shadow-sm">
                                <h4 class="font-semibold text-lg mb-2 ${isDark ? 'text-blue-300' : 'text-blue-700'} border-b ${isDark ? 'border-gray-600' : 'border-gray-200'} pb-2">接口信息</h4>
                                <div class="space-y-2 ${isDark ? 'text-gray-300' : ''}">
                                    <p><span class="font-medium">CPU接口:</span> ${psu.cpu_connector || '-'}</p>
                                    <p><span class="font-medium">PCIe接口:</span> ${psu.pcie_connector || '-'}</p>
                                    <p><span class="font-medium">SATA接口:</span> ${psu.sata_count || '-'}</p>
                            </div>
                            </div>
                        </div>
                        <div class="border ${isDark ? 'border-gray-600' : 'border-gray-200'} rounded-lg p-4 ${isDark ? 'bg-gray-700' : 'bg-white'} shadow-sm mb-4">
                            <h4 class="font-semibold text-lg mb-2 ${isDark ? 'text-blue-300' : 'text-blue-700'} border-b ${isDark ? 'border-gray-600' : 'border-gray-200'} pb-2">物理特性</h4>
                            <div class="space-y-2 ${isDark ? 'text-gray-300' : ''}">
                                <p><span class="font-medium">物理规格:</span> ${psu.psu_length || '-'}</p>
                                <p><span class="font-medium">物理长度:</span> ${psu.physical_length ? `${psu.physical_length}mm` : '-'}</p>
                                <p><span class="font-medium">风扇尺寸:</span> ${psu.fan_size ? `${psu.fan_size}mm` : '-'}</p>
                                <p><span class="font-medium">保修期:</span> ${psu.warranty ? `${psu.warranty}年` : '-'}</p>
                                ${psu.notes ? `<p><span class="font-medium">备注:</span> ${psu.notes}</p>` : ''}
                        </div>
                        </div>
                    `;

                // 2. Update the modal content
                const modalContent = document.getElementById('detailsModalContent');
                if (modalContent) {
                    modalContent.innerHTML = detailsHTML;
                } else {
                    console.error("Could not find '#detailsModalContent' to inject content.");
                }

                // 设置按钮事件并显示模态框
                const editButton = document.getElementById('detailsEditBtn');
                const deleteButton = document.getElementById('detailsDeleteBtn');

                if (isAdminUser) {
                    if (editButton) {
                        editButton.style.display = 'inline-flex';
                        editButton.onclick = () => {
                            detailsModal.classList.add('hidden');
                            populateForm(id);
                        };
                    }

                    if (deleteButton) {
                        deleteButton.style.display = 'inline-flex';
                        deleteButton.onclick = () => {
                            detailsModal.classList.add('hidden');
                            currentPsuId = id;
                            if (deleteConfirmModal) {
                                deleteConfirmModal.classList.remove('hidden');
                            }
                        };
                    }
                } else {
                    if (editButton) editButton.style.display = 'none';
                    if (deleteButton) deleteButton.style.display = 'none';
                }

                detailsModal.classList.remove('hidden');
            }
        } catch (error) {
            console.error('获取电源详情时出错:', error);
            showToast('获取电源详情时出错: ' + error.message, 'error');
        }
    }

    // 填充表单用于编辑
    async function populateForm(id) {
        const isAdminUser = await isAdmin();
        if (!isAdminUser) {
            showToast('只有管理员可以编辑数据', 'error');
            return;
        }
        try {
            const response = await fetchWithAuth(`/api/psus/${id}`);
            if (!response || !response.ok) {
                throw new Error(`服务器返回错误: ${response.status}`);
            }

            const psu = await response.json();
            currentPsuId = id;

            // 填充表单字段
            if (psuForm) {
                // 基本信息
                psuForm.brand.value = psu.brand || '';
                psuForm.model.value = psu.model || '';
                psuForm.wattage.value = psu.wattage || '';

                // 规格和认证
                if (psuForm.psu_length) psuForm.psu_length.value = psu.psu_length || '';
                if (psuForm.efficiency) psuForm.efficiency.value = psu.efficiency || '';
                if (psuForm.modular) psuForm.modular.value = psu.modular || '';

                // 接口和功能
                if (psuForm.pcie_connector) psuForm.pcie_connector.value = psu.pcie_connector || '';
                if (psuForm.sata_count) psuForm.sata_count.value = psu.sata_count || '';
                if (psuForm.cpu_connector) psuForm.cpu_connector.value = psu.cpu_connector || '';

                // 物理特性和其他
                if (psuForm.fan_size) psuForm.fan_size.value = psu.fan_size || '';
                if (psuForm.physical_length) psuForm.physical_length.value = psu.physical_length || '';
                if (psuForm.warranty) psuForm.warranty.value = psu.warranty || '';
                if (psuForm.price) psuForm.price.value = psu.price || '';
                if (psuForm.notes) psuForm.notes.value = psu.notes || '';

                // 图片预览
                if (psu.image_url && previewImage && imagePreview) {
                    previewImage.src = psu.image_url;
                    imagePreview.classList.remove('hidden');

                    // 添加点击预览功能
                    previewImage.onclick = () => {
                        openImageFullscreen(psu.image_url);
                    };
                    previewImage.style.cursor = 'pointer';
                    previewImage.title = '点击查看大图';
                } else if (imagePreview) {
                    imagePreview.classList.add('hidden');
                }

                // 更新提交按钮文本
                const submitButton = psuForm.querySelector('button[type="submit"]');
                if (submitButton) {
                    submitButton.innerHTML = '<i class="fas fa-save mr-1"></i> 更新电源信息';
                }

                // 滚动到表单
                psuForm.scrollIntoView({ behavior: 'smooth' });
            }
        } catch (error) {
            console.error('获取电源信息时出错:', error);
            showToast('获取电源信息时出错: ' + error.message, 'error');
        }
    }

    // 删除电源
    async function handleDelete() {
        if (!currentPsuId) return;

        const isAdminUser = await isAdmin();
        if (!isAdminUser) {
            showToast('只有管理员可以删除数据', 'error');
            if (deleteConfirmModal) {
                deleteConfirmModal.classList.add('hidden');
            }
            return;
        }

        try {
            const response = await fetchWithAuth(`/api/psus/${currentPsuId}`, {
                method: 'DELETE'
            });

            if (!response || !response.ok) {
                throw new Error(`服务器返回错误: ${response.status}`);
            }

            // 关闭模态框
            if (deleteConfirmModal) {
                deleteConfirmModal.classList.add('hidden');
            }

            // 重置状态
            currentPsuId = null;

            // 显示成功消息
            showToast('电源信息已成功删除', 'success');

            // 重新获取数据
            fetchPsus();
        } catch (error) {
            console.error('删除电源信息时出错:', error);
            showToast('删除电源信息时出错: ' + error.message, 'error');
        }
    }

    // 显示消息提示
    function showToast(message, type = 'success') {
        const toast = document.getElementById('toast');
        if (!toast) return;

        toast.textContent = message;
        toast.className = `fixed bottom-4 right-4 px-4 py-2 rounded-lg text-white ${type === 'success' ? 'bg-green-500' : 'bg-red-500'}`;
        toast.classList.remove('hidden');

        setTimeout(() => {
            toast.classList.add('hidden');
        }, 3000);
    }

    // 暴露全局函数
    window.openImageFullscreen = openImageFullscreen;

    // 智能解析电源信息
    function parsePsuInfo() {
        let input = smartInput?.value?.trim();
        if (!input) {
            showToast('请输入电源参数', 'error');
            return;
        }

        autoFillBtn.disabled = true;
        autoFillBtn.innerHTML = '<i class="fas fa-sync fa-spin mr-2"></i>正在解析...';

        try {
            console.log(`[Parse Start] Initial PSU Input: "${input}"`);

            const result = {
                brand: '', model: '', wattage: '', psu_length: '',
                efficiency: '', modular: '', pcie_connector: '', sata_count: '',
                cpu_connector: '', physical_length: '', fan_size: '', warranty: '',
                price: '', notes: ''
            };

            // 预处理输入文本
            let workInput = input.replace(/：/g, ':')
                .replace(/，/g, '、')
                .replace(/,/g, '、');

            console.log(`[Parse Preprocessed] Input: "${workInput}"`);

            // 分隔输入文本为字段对
            const pairs = [];
            // 首先尝试通过分隔符拆分
            const segments = workInput.split('、').filter(s => s.trim());

            for (const segment of segments) {
                const colonIndex = segment.indexOf(':');
                if (colonIndex > 0) {
                    const key = segment.substring(0, colonIndex).trim();
                    const value = segment.substring(colonIndex + 1).trim();
                    if (key && value) {
                        pairs.push({ key, value });
                        console.log(`[Parse] Split pair: ${key} = ${value}`);
                    }
                } else {
                    // 没有冒号的段落，可能是前一个字段的延续或独立信息
                    if (pairs.length > 0) {
                        // 附加到前一个值
                        pairs[pairs.length - 1].value += '、' + segment;
                        console.log(`[Parse] Appended to previous: ${pairs[pairs.length - 1].key} = ${pairs[pairs.length - 1].value}`);
                    } else {
                        // 没有前一个字段，作为未标记的信息
                        pairs.push({ key: 'unknown', value: segment });
                        console.log(`[Parse] Added unknown segment: ${segment}`);
                    }
                }
            }

            // 定义字段映射和处理函数
            const fieldMapping = {
                // 功率字段匹配
                '功率': {
                    field: 'wattage',
                    regex: /(功率|瓦数|功耗)[:：]?\s*(\d+)(?:\s*[wW瓦])?/i,
                    process: (val) => {
                        const match = val.match(/(\d+)/);
                        return match ? match[1] : val;
                    }
                },
                // 品牌字段匹配
                '品牌': {
                    field: 'brand',
                    regex: /(品牌|厂商|制造商)[:：]?\s*(.+)/i,
                    process: (val) => {
                        const brandAlias = {
                            'Seasonic': '海韵', 'Antec': '安钛克', 'Great Wall': '长城', 'Cooler Master': '酷冷至尊',
                            'ASUS': '华硕', 'MSI': '微星', 'Gigabyte': '技嘉', 'Corsair': '海盗船', '美商海盗船': '海盗船',
                            'SilverStone': '银欣', 'Enermax': '安耐美', 'Lian Li': '联力', 'Thermaltake': '曜越',
                            'AORUS': '技嘉', 'ROG': '华硕', '航嘉': '航嘉', '振华': '振华', '全汉': '全汉',
                            'SEASONIC': '海韵'
                        };
                        for (const alias in brandAlias) {
                            if (val.toLowerCase().includes(alias.toLowerCase()))
                                return brandAlias[alias];
                        }
                        const brandList = [
                            '海韵', '安钛克', '振华', '长城', '酷冷至尊', '华硕', '微星', '技嘉',
                            'EVGA', 'Corsair', '全汉', '银欣', '安耐美', '松下', '联力', '曜越', 'FSP', '航嘉'
                        ];
                        const foundBrand = brandList.find(b => val.toLowerCase().includes(b.toLowerCase()));
                        return foundBrand || val;
                    }
                },
                // 型号字段匹配
                '型号': {
                    field: 'model',
                    regex: /(型号|名称|规格型号|型号名称)[:：]?\s*(.+)/i
                },
                // 规格字段匹配
                '规格': {
                    field: 'psu_length',
                    regex: /(规格|尺寸规格|电源规格)[:：]?\s*(.+)/i,
                    process: (val) => {
                        const upperVal = val.toUpperCase();
                        if (upperVal.includes('SFX-L')) return 'SFX-L';
                        if (upperVal.includes('SFX')) return 'SFX';
                        if (upperVal.includes('ATX')) return 'ATX';
                        if (upperVal.includes('FLEXATX') || upperVal.includes('FLEX ATX')) return 'FlexATX';
                        if (upperVal.includes('TFX')) return 'TFX';
                        return val;
                    }
                },
                // 能效认证字段匹配
                '能效认证': {
                    field: 'efficiency',
                    regex: /(能效认证|80\+|80PLUS|认证|能效等级)[:：]?\s*(.+)/i,
                    process: (val) => {
                        const efficiencyMap = {
                            '白牌': '80+ White', '铜牌': '80+ Bronze', '银牌': '80+ Silver',
                            '金牌': '80+ Gold', '白金牌': '80+ Platinum', '钛金牌': '80+ Titanium',
                            'white': '80+ White', 'bronze': '80+ Bronze', 'silver': '80+ Silver',
                            'gold': '80+ Gold', 'platinum': '80+ Platinum', 'titanium': '80+ Titanium'
                        };
                        for (const key in efficiencyMap) {
                            if (val.toLowerCase().includes(key.toLowerCase())) return efficiencyMap[key];
                        }
                        if (val.toLowerCase().includes('80 plus 金牌')) return '80+ Gold';
                        if (val.toLowerCase().includes('80+')) return '80+ White';
                        return val;
                    }
                },
                // 模块化类型字段匹配
                '模块化类型': {
                    field: 'modular',
                    regex: /(模块化|模块化类型|线材)[:：]?\s*(.+)/i,
                    process: (val) => {
                        const modularMap = { '全模组': 'Full', '半模组': 'Semi', '非模组': 'No' };
                        for (const key in modularMap) {
                            if (val.includes(key)) return modularMap[key];
                        }
                        if (val.toLowerCase().includes('full')) return 'Full';
                        if (val.toLowerCase().includes('semi')) return 'Semi';
                        if (val.toLowerCase().includes('no')) return 'No';
                        return val;
                    }
                },
                // PCIe接口字段匹配
                'PCIe接口': {
                    field: 'pcie_connector',
                    regex: /(PCIe接口|PCIe|显卡供电|显卡接口|PCIe接口数)[:：]?\s*(.+)/i
                },
                // SATA接口字段匹配
                'SATA接口': {
                    field: 'sata_count',
                    regex: /(SATA接口|SATA|SATA口|SATA接口数)[:：]?\s*(.+)/i
                },
                // CPU接口字段匹配
                'CPU接口': {
                    field: 'cpu_connector',
                    regex: /(CPU接口|CPU\s*8针接口|CPU8针接口|CPU8针接口数|CPU|CPU供电|CPU针脚|CPU接口数)[:：]?\s*(.+)/i
                },
                // 专门为CPU8针接口数添加的匹配项
                'CPU8针接口数': {
                    field: 'cpu_connector',
                    regex: /(CPU8针接口数)[:：]?\s*(.+)/i
                },
                // 物理长度字段匹配
                '长度': {
                    field: 'physical_length',
                    regex: /(长度|物理长度|尺寸|尺寸长度)[:：]?\s*(\d+)\s*(?:mm|毫米)?/i,
                    process: (val) => {
                        const match = val.match(/(\d+)/);
                        return match ? match[1] : val;
                    }
                },
                // 风扇尺寸字段匹配
                '风扇尺寸': {
                    field: 'fan_size',
                    regex: /(风扇尺寸|风扇|散热风扇|风扇大小)[:：]?\s*(\d+)\s*(?:mm|cm|毫米)?/i,
                    process: (val) => {
                        const match = val.match(/(\d+)/);
                        if (!match) return val;

                        let size = parseInt(match[1]);
                        if (val.toLowerCase().includes('cm')) {
                            size *= 10;
                        }
                        return String(size);
                    }
                },
                // 保修期字段匹配
                '保修期': {
                    field: 'warranty',
                    regex: /(保修期|保修|质保)[:：]?\s*(\d+)\s*(?:年|months|years)?/i,
                    process: (val) => {
                        const match = val.match(/(\d+)/);
                        return match ? match[1] : val;
                    }
                },
                // 价格字段匹配
                '价格': {
                    field: 'price',
                    regex: /(价格|售价|费用|价钱)[:：]?\s*(?:¥|￥|RMB|CNY)?\s*(\d+(?:\.\d{1,2})?)/i,
                    process: (val) => {
                        const match = val.match(/(\d+(?:\.\d{1,2})?)/);
                        return match ? match[1] : val;
                    }
                },
                // 备注字段匹配
                '备注': {
                    field: 'notes',
                    regex: /(备注|注意|说明|附注)[:：]?\s*(.+)/i
                }
            };

            // 处理拆分后的字段对
            for (const { key, value } of pairs) {
                // 首先尝试通过键名匹配
                let matched = false;

                // 直接处理特殊情况 - CPU8针接口数
                if (key === 'CPU8针接口数') {
                    result.cpu_connector = value;
                    console.log(`[Parse] Direct match for CPU8针接口数: ${value}`);
                    matched = true;
                } else {
                    // 常规字段匹配
                    for (const [fieldKey, fieldInfo] of Object.entries(fieldMapping)) {
                        if (key.includes(fieldKey) || fieldKey.includes(key)) {
                            let processedValue = value;
                            if (fieldInfo.process) {
                                processedValue = fieldInfo.process(value);
                            }
                            result[fieldInfo.field] = processedValue;
                            console.log(`[Parse] Matched by key: ${fieldKey} = ${processedValue}`);
                            matched = true;
                            break;
                        }
                    }
                }

                // 如果键名不匹配，尝试通过正则表达式匹配值
                if (!matched && key === 'unknown') {
                    for (const [fieldKey, fieldInfo] of Object.entries(fieldMapping)) {
                        if (fieldInfo.regex && value.match(fieldInfo.regex)) {
                            const match = value.match(fieldInfo.regex);
                            if (match && match[2]) {
                                let processedValue = match[2].trim();
                                if (fieldInfo.process) {
                                    processedValue = fieldInfo.process(processedValue);
                                }
                                result[fieldInfo.field] = processedValue;
                                console.log(`[Parse] Matched by regex: ${fieldKey} = ${processedValue}`);
                                matched = true;
                                break;
                            }
                        }
                    }
                }

                // 如果仍然未匹配，且为未知字段，可能是独立数据或补充信息
                if (!matched && key === 'unknown') {
                    // 检查是否包含功率信息
                    const wattageMatch = value.match(/(\d+)\s*[wW瓦]/);
                    if (wattageMatch && !result.wattage) {
                        result.wattage = wattageMatch[1];
                        console.log(`[Parse] Extracted wattage from unknown: ${result.wattage}`);
                    }
                    // 追加到备注
                    else if (!result.notes) {
                        result.notes = value;
                        console.log(`[Parse] Added unknown segment to notes: ${value}`);
                    } else {
                        result.notes += '、' + value;
                        console.log(`[Parse] Appended unknown segment to notes: ${value}`);
                    }
                }
            }

            // 在原始文本中二次搜索重要字段
            const secondaryExtractions = [
                { field: 'wattage', regex: /(\d+)\s*[wW瓦]/ },
                { field: 'price', regex: /(?:¥|￥|RMB|CNY)?\s*(\d+(?:\.\d{1,2})?)/ },
                { field: 'physical_length', regex: /(\d+)\s*(?:mm|毫米)/ },
                { field: 'fan_size', regex: /(\d+)\s*(?:mm|cm|毫米)风扇/ },
                { field: 'warranty', regex: /(\d+)\s*(?:年|years|months)(?:质保|保修)/ }
            ];

            // 对于未填充的字段，尝试二次提取
            for (const { field, regex } of secondaryExtractions) {
                if (!result[field]) {
                    const match = workInput.match(regex);
                    if (match && match[1]) {
                        result[field] = match[1];
                        console.log(`[Parse] Secondary extraction for ${field}: ${result[field]}`);
                    }
                }
            }

            if (!psuForm) {
                console.error("psuForm not found");
                showToast('表单未找到，无法填充', 'error');
                autoFillBtn.disabled = false;
                autoFillBtn.innerHTML = '<i class="fas fa-robot mr-2"></i>智能解析';
                return;
            }

            function setSelectValue(selectElement, value) {
                if (selectElement && value !== undefined && value !== null && String(value).trim() !== '') {
                    const options = Array.from(selectElement.options);
                    const optionToSelect = options.find(opt => String(opt.value).toLowerCase() === String(value).toLowerCase() || opt.text.toLowerCase().includes(String(value).toLowerCase()));
                    if (optionToSelect) {
                        selectElement.value = optionToSelect.value;
                    } else {
                        console.warn(`Value "${value}" not found for select ${selectElement.id}. Attempting partial match or adding if simple text.`);
                        const partialMatch = options.find(opt => opt.text.toLowerCase().includes(String(value).toLowerCase()));
                        if (partialMatch) selectElement.value = partialMatch.value;
                    }
                }
            }

            if (result.brand) psuForm.brand.value = result.brand;
            if (result.model) psuForm.model.value = result.model;
            if (result.wattage) psuForm.wattage.value = result.wattage;

            setSelectValue(psuForm.psu_length, result.psu_length);
            setSelectValue(psuForm.efficiency, result.efficiency);
            setSelectValue(psuForm.modular, result.modular);

            if (result.pcie_connector && psuForm.pcie_connector) psuForm.pcie_connector.value = result.pcie_connector;
            if (result.sata_count && psuForm.sata_count) psuForm.sata_count.value = result.sata_count;
            if (result.cpu_connector && psuForm.cpu_connector) psuForm.cpu_connector.value = result.cpu_connector;

            if (result.physical_length && psuForm.physical_length) psuForm.physical_length.value = result.physical_length;
            if (result.fan_size && psuForm.fan_size) psuForm.fan_size.value = result.fan_size;
            if (result.warranty && psuForm.warranty) psuForm.warranty.value = result.warranty;
            if (result.price && psuForm.price) psuForm.price.value = result.price;
            if (result.notes && psuForm.notes) psuForm.notes.value = result.notes;

            const filledFields = Object.values(result).filter(value => String(value).trim() !== '').length;
            showToast(`成功识别 ${filledFields} 项参数！`, 'success');
            console.log('[Parse End] PSU Parsing complete. Result:', result);
        } catch (error) {
            console.error('解析电源参数时发生错误:', error);
            showToast('解析电源参数时发生错误: ' + error.message, 'error');
        } finally {
            autoFillBtn.disabled = false;
            autoFillBtn.innerHTML = '<i class="fas fa-robot mr-2"></i>智能解析';
        }
    } // End of parsePsuInfo

    // Helper function (keep openImageFullscreen global or ensure it's correctly scoped if moved)
    function openImageFullscreen(src) {
        // 检查是否已存在查看器容器，先清除
        const existingContainers = document.querySelectorAll('.viewer-container');
        existingContainers.forEach(container => {
            container.remove();
        });

        // 创建一个临时的图片容器
        const container = document.createElement('div');
        container.className = 'viewer-container';
        container.style.display = 'none';
        document.body.appendChild(container);

        // 创建图片元素
        const img = document.createElement('img');
        img.src = src;
        container.appendChild(img);

        // 初始化 Viewer
        const viewer = new Viewer(img, {
            backdrop: true,          // 启用背景遮罩
            button: true,           // 显示关闭按钮
            navbar: false,          // 隐藏底部导航栏（只有一张图片时不需要）
            title: false,           // 不显示标题
            toolbar: {              // 自定义工具栏
                zoomIn: true,       // 放大按钮
                zoomOut: true,      // 缩小按钮
                oneToOne: true,     // 1:1 尺寸按钮
                reset: true,        // 重置按钮
                prev: false,        // 上一张（隐藏，因为只有一张图片）
                play: false,        // 播放按钮（隐藏）
                next: false,        // 下一张（隐藏）
                rotateLeft: true,   // 向左旋转
                rotateRight: true,  // 向右旋转
                flipHorizontal: true, // 水平翻转
                flipVertical: true,  // 垂直翻转
            },
            viewed() {
                // 图片加载完成后自动打开查看器
                if (isMobile) {
                    viewer.zoomTo(0.8);  // 移动设备使用较小的初始缩放比例
                } else {
                    viewer.zoomTo(1);    // 桌面设备使用正常的缩放比例
                }
            },
            hidden() {
                // 查看器关闭后移除临时元素
                viewer.destroy();
                document.body.removeChild(container);
            },
            maxZoomRatio: 5,        // 最大缩放比例
            minZoomRatio: 0.1,      // 最小缩放比例
            transition: true,       // 启用过渡效果
            keyboard: true,         // 启用键盘支持
        });

        // 显示查看器
        viewer.show();
    }

    // 移动端卡片式布局渲染
    function renderMobileCards(data, isAdminUser) {
        psuTableBody.innerHTML = '';

        const tableElement = psuTableBody.closest('table');
        if (tableElement) {
            tableElement.style.display = 'block';
            tableElement.style.width = '100%';
            tableElement.style.maxWidth = '100%';
            tableElement.style.overflow = 'hidden';
            tableElement.style.borderCollapse = 'separate';
            tableElement.style.borderSpacing = '0 12px';
            const theadElement = tableElement.querySelector('thead');
            if (theadElement) {
                theadElement.style.display = 'none';
            }
        }

        psuTableBody.style.display = 'block';
        psuTableBody.style.width = '100%';
        psuTableBody.style.maxWidth = '100%';
        psuTableBody.style.paddingLeft = '4px';
        psuTableBody.style.paddingRight = '4px';

        data.forEach((psu, index) => {
            // 创建卡片外部容器
            const cardOuterContainer = document.createElement('div');
            cardOuterContainer.className = 'psu-card-outer-container';
            cardOuterContainer.style.width = '100%';
            cardOuterContainer.style.maxWidth = '100%';
            cardOuterContainer.style.boxSizing = 'border-box';
            cardOuterContainer.style.marginBottom = '16px';
            cardOuterContainer.style.position = 'relative';
            cardOuterContainer.style.animation = `fadeIn 0.3s ease-in-out ${index * 0.05}s both`;

            // 创建主卡片容器
            const card = document.createElement('div');
            card.className = 'psu-card-new';
            card.style.width = '100%';
            card.style.borderRadius = '12px';
            card.style.overflow = 'hidden';
            card.style.backgroundColor = isDarkMode() ? '#1a1a1a' : 'white';
            card.style.boxShadow = isDarkMode() ?
                '0 4px 12px rgba(0,0,0,0.2)' :
                '0 4px 12px rgba(0,0,0,0.08)';
            card.style.display = 'flex';
            card.style.flexDirection = 'column';
            card.style.minWidth = '0';
            card.style.transition = 'transform 0.2s ease, box-shadow 0.2s ease';

            // 添加触摸反馈效果
            card.addEventListener('touchstart', function () {
                this.style.transform = 'scale(0.99)';
                this.style.boxShadow = isDarkMode() ?
                    '0 2px 8px rgba(0,0,0,0.15)' :
                    '0 2px 8px rgba(0,0,0,0.05)';
            });
            card.addEventListener('touchend', function () {
                this.style.transform = 'scale(1)';
                this.style.boxShadow = isDarkMode() ?
                    '0 4px 12px rgba(0,0,0,0.2)' :
                    '0 4px 12px rgba(0,0,0,0.08)';
            });

            // 设置品牌颜色
            let brandColor = '#4A5568';
            let lightBrandColor = isDarkMode() ? 'rgba(55, 65, 81, 0.5)' : 'rgba(226, 232, 240, 0.5)';
            let borderBrandColor = isDarkMode() ? 'rgba(75, 85, 99, 0.5)' : 'rgba(203, 213, 225, 0.5)';
            let headerBgColor = isDarkMode() ? '#1a1a1a' : '#f8f9fa';

            // 卡片头部
            const cardHeader = document.createElement('div');
            cardHeader.style.padding = '10px 14px';
            cardHeader.style.backgroundColor = headerBgColor;
            cardHeader.style.borderBottom = `1px solid ${borderBrandColor}`;
            cardHeader.style.display = 'flex';
            cardHeader.style.justifyContent = 'space-between';
            cardHeader.style.alignItems = 'center';

            // 型号名称
            const modelName = document.createElement('div');
            modelName.textContent = psu.model || '未知型号';
            modelName.style.fontSize = '1rem';
            modelName.style.fontWeight = 'bold';
            modelName.style.color = isDarkMode() ? '#e0e0e0' : '#1a202c';
            modelName.style.wordBreak = 'break-word';
            modelName.style.flexGrow = '1';
            modelName.style.marginRight = '8px';

            // 品牌标签
            const brandBadge = document.createElement('span');
            brandBadge.textContent = psu.brand || '未知';
            brandBadge.style.padding = '3px 8px';
            brandBadge.style.fontSize = '0.7rem';
            brandBadge.style.fontWeight = 'bold';
            brandBadge.style.borderRadius = '4px';

            // 根据主题设置样式
            const badgeStyle = getBrandBadgeStyle();
            brandBadge.style.color = badgeStyle.color;
            brandBadge.style.backgroundColor = badgeStyle.backgroundColor;
            brandBadge.style.border = badgeStyle.border;
            brandBadge.style.flexShrink = '0';
            brandBadge.style.lineHeight = '1';

            cardHeader.appendChild(modelName);
            cardHeader.appendChild(brandBadge);

            // 卡片内容区
            const cardBody = document.createElement('div');
            cardBody.style.padding = '12px';
            cardBody.style.backgroundColor = isDarkMode() ? '#1a1a1a' : 'white';
            cardBody.style.display = 'flex';
            cardBody.style.gap = '12px';

            // 图片容器
            const imgContainer = document.createElement('div');
            imgContainer.className = 'flex-shrink-0';
            imgContainer.style.width = '60px';
            imgContainer.style.height = '60px';
            imgContainer.style.borderRadius = '6px';
            imgContainer.style.border = `1px solid ${borderBrandColor}`;
            imgContainer.style.display = 'flex';
            imgContainer.style.alignItems = 'center';
            imgContainer.style.justifyContent = 'center';
            imgContainer.style.overflow = 'hidden';
            imgContainer.style.backgroundColor = isDarkMode() ? '#1a1a1a' : '#f9f9f9';
            imgContainer.style.flexShrink = '0';

            // 图片元素
            const imgElement = document.createElement('img');
            imgElement.alt = psu.model || '电源图片';
            imgElement.style.width = '100%';
            imgElement.style.height = '100%';
            imgElement.style.objectFit = 'contain';
            imgElement.style.cursor = 'pointer';

            // 设置图片源
            const imageUrl = psu.image_url || DEFAULT_PSU_IMAGE;
            if (imageUrl) {
                imgElement.src = imageUrl;
                imgElement.onclick = () => openImageFullscreen(imageUrl);
                imgContainer.appendChild(imgElement);
            } else {
                // 无图片时显示占位文字
                imgContainer.textContent = '电源';
                imgContainer.style.fontSize = '0.75rem';
                imgContainer.style.color = isDarkMode() ? '#a0a0a0' : '#6b7280';
                imgContainer.style.textAlign = 'center';
            }

            // 图片加载失败处理
            imgElement.onerror = function () {
                this.onerror = null;
                this.style.display = 'none';
                imgContainer.textContent = '电源';
                imgContainer.style.fontSize = '0.75rem';
                imgContainer.style.color = isDarkMode() ? '#a0a0a0' : '#6b7280';
                imgContainer.style.textAlign = 'center';
            };

            // 信息容器
            const infoContainer = document.createElement('div');
            infoContainer.style.flexGrow = '1';
            infoContainer.style.display = 'flex';
            infoContainer.style.flexDirection = 'column';
            infoContainer.style.justifyContent = 'center';
            infoContainer.style.gap = '4px';

            // 功率信息
            if (psu.wattage) {
                const wattageText = document.createElement('div');
                wattageText.innerHTML = `<span style="font-weight: bold; color: ${isDarkMode() ? '#34d399' : '#166534'}; font-size: 0.95rem;">${psu.wattage}W</span>`;
                infoContainer.appendChild(wattageText);
            }

            // 规格信息
            if (psu.psu_length || psu.modular) {
                let specTextContent = '';
                if (psu.psu_length) {
                    specTextContent += `规格: ${psu.psu_length}`;
                }
                if (psu.modular) {
                    specTextContent += specTextContent ? ` | ${getModularType(psu.modular)}` : `类型: ${getModularType(psu.modular)}`;
                }

                const specText = document.createElement('div');
                specText.style.fontSize = '0.8rem';
                specText.style.color = isDarkMode() ? '#b0b0b0' : '#374151';
                specText.textContent = specTextContent;
                infoContainer.appendChild(specText);
            }

            // 标签组
            const tagGroup = document.createElement('div');
            tagGroup.style.display = 'flex';
            tagGroup.style.flexWrap = 'wrap';
            tagGroup.style.gap = '4px';
            tagGroup.style.marginTop = '6px';

            // 标签创建函数
            function createTag(text, bgColor, textColor) {
                const isDark = isDarkMode();
                let finalBgColor = bgColor;
                let finalTextColor = textColor;

                // 深色模式下的颜色调整
                if (isDark) {
                    // 提取基础颜色，增加透明度
                    if (typeof bgColor === 'string' && bgColor.includes('rgba')) {
                        const baseColor = bgColor.split(',')[0].replace('rgba(', '');
                        finalBgColor = `rgba(${baseColor}, 79, 229, 0.2)`;
                    }

                    // 文字颜色加亮
                    if (textColor === '#166534') finalTextColor = '#34d399'; // 绿色
                    else if (textColor === '#B91C1C') finalTextColor = '#f87171'; // 红色
                    else if (textColor === '#1D4ED8') finalTextColor = '#60a5fa'; // 蓝色
                    else if (textColor === '#B45309') finalTextColor = '#fbbf24'; // 黄色
                }

                const tag = document.createElement('span');
                tag.style.fontSize = '0.65rem';
                tag.style.padding = '2px 5px';
                tag.style.backgroundColor = finalBgColor;
                tag.style.color = finalTextColor;
                tag.style.borderRadius = '3px';
                tag.style.whiteSpace = 'nowrap';
                tag.textContent = text;
                return tag;
            }

            // 能效标签
            if (psu.efficiency) {
                let bgColor = 'rgba(79, 70, 229, 0.1)';
                let textColor = '#4F46E5';

                if (psu.efficiency.includes('80+ Gold') || psu.efficiency.includes('金牌')) {
                    bgColor = 'rgba(234, 179, 8, 0.1)';
                    textColor = '#B45309';
                } else if (psu.efficiency.includes('80+ Platinum') || psu.efficiency.includes('白金')) {
                    bgColor = 'rgba(147, 197, 253, 0.1)';
                    textColor = '#1D4ED8';
                } else if (psu.efficiency.includes('80+ Titanium') || psu.efficiency.includes('钛')) {
                    bgColor = 'rgba(167, 139, 250, 0.1)';
                    textColor = '#6D28D9';
                }

                tagGroup.appendChild(createTag(psu.efficiency, bgColor, textColor));
            }

            // 接口标签
            if (psu.cpu_connector) {
                tagGroup.appendChild(createTag(`CPU: ${psu.cpu_connector}`, 'rgba(22, 101, 52, 0.1)', '#166534'));
            }

            // SATA标签
            if (psu.sata_count) {
                tagGroup.appendChild(createTag(`SATA: ${psu.sata_count}`, 'rgba(220, 38, 38, 0.1)', '#B91C1C'));
            }

            // 价格标签
            if (psu.price) {
                tagGroup.appendChild(createTag(`¥${psu.price}`, 'rgba(220, 38, 38, 0.1)', '#B91C1C'));
            }

            // 添加标签组到信息容器
            if (tagGroup.children.length > 0) {
                infoContainer.appendChild(tagGroup);
            }

            // 将容器添加到卡片体
            cardBody.appendChild(imgContainer);
            cardBody.appendChild(infoContainer);

            // 卡片底部按钮区
            const cardFooter = document.createElement('div');
            cardFooter.style.padding = '10px 12px';
            cardFooter.style.backgroundColor = isDarkMode() ? '#1a1a1a' : '#f9f9fb';
            cardFooter.style.borderTop = `1px solid ${isDarkMode() ? '#374151' : '#e5e7eb'}`;
            cardFooter.style.display = 'flex';
            cardFooter.style.justifyContent = 'space-evenly';
            cardFooter.style.alignItems = 'center';
            cardFooter.style.gap = '8px';

            // 创建操作按钮
            const viewButton = createActionButton('<i class="fas fa-eye"></i><span class="ml-1">查看</span>', '查看', '#3B82F6', () => viewPsuDetails(psu.id));
            cardFooter.appendChild(viewButton);

            if (isAdminUser) {
                const editButton = createActionButton('<i class="fas fa-edit"></i><span class="ml-1">编辑</span>', '编辑', '#10B981', () => populateForm(psu.id));
                const deleteButton = createActionButton('<i class="fas fa-trash"></i><span class="ml-1">删除</span>', '删除', '#EF4444', () => {
                    currentPsuId = psu.id;
                    if (deleteConfirmModal) {
                        deleteConfirmModal.classList.remove('hidden');
                    }
                });

                cardFooter.appendChild(editButton);
                cardFooter.appendChild(deleteButton);
            }

            // 组装卡片
            card.appendChild(cardHeader);
            card.appendChild(cardBody);
            card.appendChild(cardFooter);
            cardOuterContainer.appendChild(card);
            psuTableBody.appendChild(cardOuterContainer);
        });
    }

    // 创建操作按钮辅助函数
    function createActionButton(innerHTML, title, textColor, onClick) {
        const button = document.createElement('button');
        button.style.padding = '8px 12px';
        button.style.flex = '1';
        button.style.borderRadius = '6px';
        button.style.display = 'inline-flex';
        button.style.alignItems = 'center';
        button.style.justifyContent = 'center';

        // 根据主题模式设置按钮样式
        if (isDarkMode()) {
            button.style.backgroundColor = '#1a1a1a';  // 更深的背景
            button.style.border = '1px solid #374151';
            // 根据按钮类型设置不同颜色
            if (textColor === '#3B82F6') { // 查看按钮
                button.style.color = '#60a5fa';
            } else if (textColor === '#10B981') { // 编辑按钮
                button.style.color = '#34d399';
            } else if (textColor === '#EF4444') { // 删除按钮
                button.style.color = '#f87171';
            } else {
                button.style.color = '#e5e7eb';
            }
        } else {
            button.style.backgroundColor = 'white';
            button.style.border = '1px solid #e5e7eb';
            button.style.color = textColor;
        }

        button.style.fontSize = '0.85rem';
        button.style.fontWeight = '500';
        button.style.boxShadow = isDarkMode() ?
            '0 1px 3px rgba(0,0,0,0.3)' :
            '0 1px 2px rgba(0,0,0,0.05)';
        button.style.transition = 'all 0.15s ease-out';
        button.innerHTML = innerHTML;
        button.title = title;
        button.addEventListener('click', onClick);

        // 添加悬停效果
        button.addEventListener('mouseenter', function () {
            if (isDarkMode()) {
                this.style.backgroundColor = '#2a2a2a';
                this.style.transform = 'translateY(-1px)';
            } else {
                this.style.backgroundColor = `${textColor}10`;
                this.style.transform = 'translateY(-1px)';
            }
        });

        button.addEventListener('mouseleave', function () {
            if (isDarkMode()) {
                this.style.backgroundColor = '#1a1a1a';
                this.style.transform = 'translateY(0)';
            } else {
                this.style.backgroundColor = 'white';
                this.style.transform = 'translateY(0)';
            }
        });

        // 添加触摸效果
        button.addEventListener('touchstart', function () {
            if (isDarkMode()) {
                this.style.backgroundColor = '#2a2a2a';
            } else {
                this.style.backgroundColor = `${textColor}20`;
            }
        });

        button.addEventListener('touchend', function () {
            if (isDarkMode()) {
                this.style.backgroundColor = '#1a1a1a';
            } else {
                this.style.backgroundColor = 'white';
            }
        });

        return button;
    }

    // 监听主题切换按钮事件
    const themeToggleBtn = document.getElementById('themeToggleBtn');
    if (themeToggleBtn) {
        themeToggleBtn.addEventListener('click', () => {
            // 创建一个主题变化的自定义事件
            const themeChangeEvent = new Event('themeChanged');
            document.dispatchEvent(themeChangeEvent);
        });
    }

    // 添加对模态框内容的主题样式监听
    document.addEventListener('themeChanged', () => {
        // 当主题变化时，重新应用模态框样式
        updateModalTheme();
    });

    // 更新模态框主题样式
    function updateModalTheme() {
        const isDark = isDarkMode();

        // 更新详情模态框样式
        const detailsModalBg = document.querySelector('#detailsModal .bg-white');
        if (detailsModalBg) {
            if (isDark) {
                detailsModalBg.classList.remove('bg-white');
                detailsModalBg.classList.remove('bg-black');
                detailsModalBg.classList.add('bg-gray-800');
                detailsModalBg.classList.add('bg-opacity-95');
                detailsModalBg.classList.add('text-gray-100');
            } else {
                detailsModalBg.classList.add('bg-white');
                detailsModalBg.classList.remove('bg-gray-800');
                detailsModalBg.classList.remove('bg-opacity-95');
                detailsModalBg.classList.remove('text-gray-100');
            }
        }

        // 更新删除确认模态框样式
        const deleteModalBg = document.querySelector('#deleteConfirmModal .bg-white');
        if (deleteModalBg) {
            if (isDark) {
                deleteModalBg.classList.remove('bg-white');
                deleteModalBg.classList.remove('bg-black');
                deleteModalBg.classList.add('bg-gray-800');
                deleteModalBg.classList.add('bg-opacity-95');
                deleteModalBg.classList.add('text-gray-100');
            } else {
                deleteModalBg.classList.add('bg-white');
                deleteModalBg.classList.remove('bg-gray-800');
                deleteModalBg.classList.remove('bg-opacity-95');
                deleteModalBg.classList.remove('text-gray-100');
            }
        }

        // 如果模态框是打开的，更新内部卡片内容的颜色
        if (detailsModal && !detailsModal.classList.contains('hidden') && detailsModal.querySelector('.p-6')) {
            // 获取当前显示的产品ID并重新加载详情
            const currentDetailPsuId = detailsModal.dataset.psuId;
            if (currentDetailPsuId) {
                viewPsuDetails(currentDetailPsuId);
            }
        }
    }

    // 初始化调用
    init();
}); // End of DOMContentLoaded










// 初始化权限控制
function initPermissions() {
    console.log('初始化权限控制...');

    // 如果没有权限控制模块，发出警告
    if (typeof setupPermissionBasedUI !== 'function') {
        console.warn('警告：权限控制模块未加载！请确保已引入permission-helper.js');
        return;
    }

    // 执行权限控制初始化
    setupPermissionBasedUI().then(() => {
        // 检查编辑按钮
        const editButtons = document.querySelectorAll('.btn-edit, [data-action="edit"]');
        const deleteButtons = document.querySelectorAll('.btn-delete, [data-action="delete"]');

        // 根据用户角色处理按钮
        isAdmin().then(isAdminUser => {
            if (!isAdminUser) {
                // 禁用表单
                const form = document.querySelector('form');
                if (form) {
                    const inputs = form.querySelectorAll('input, select, textarea');
                    inputs.forEach(input => {
                        input.setAttribute('disabled', 'disabled');
                    });

                    const submitBtn = form.querySelector('button[type="submit"]');
                    if (submitBtn) {
                        submitBtn.setAttribute('disabled', 'disabled');
                        submitBtn.title = '只有管理员可以添加或修改数据';
                    }
                }

                // 隐藏或禁用编辑、删除按钮
                editButtons.forEach(btn => {
                    btn.style.display = 'none';
                });

                deleteButtons.forEach(btn => {
                    btn.style.display = 'none';
                });

                // 为所有删除操作添加权限验证拦截器
                interceptDeleteOperations();
            } else {
                // 添加管理员标识
                const header = document.querySelector('h1, h2');
                if (header) {
                    const adminBadge = document.createElement('span');
                    adminBadge.className = 'bg-green-500 text-white text-xs px-2 py-1 rounded ml-2';
                    adminBadge.innerText = '管理员';
                    header.appendChild(adminBadge);
                }
            }
        });
    });
}

// 拦截所有删除操作的请求
function interceptDeleteOperations() {
    // 保存原始的fetch函数
    const originalFetch = window.fetch;

    // 重写fetch函数以拦截删除请求
    window.fetch = async function (url, options) {
        // 检查是否为删除操作
        if (options && options.method === 'DELETE') {
            console.log('拦截到删除操作请求:', url);

            // 检查用户权限
            const isAdminUser = await isAdmin();
            if (!isAdminUser) {
                console.warn('权限不足，普通用户无法执行删除操作');
                // 返回权限错误响应
                return Promise.resolve(new Response(JSON.stringify({
                    error: '权限不足',
                    message: '只有管理员可以删除数据'
                }), {
                    status: 403,
                    headers: {
                        'Content-Type': 'application/json'
                    }
                }));
            }
        }

        // 对于非删除请求或管理员的删除请求，正常执行
        return originalFetch.apply(this, arguments);
    };

    // 添加全局点击事件拦截
    document.addEventListener('click', async function (event) {
        // 查找可能触发删除操作的元素
        const deleteButton = event.target.closest('.btn-delete, [data-action="delete"]');
        if (deleteButton) {
            const isAdminUser = await isAdmin();
            if (!isAdminUser) {
                // 阻止事件传播
                event.preventDefault();
                event.stopPropagation();
                console.warn('权限不足，普通用户无法执行删除操作');

                // 可选：显示提示消息（使用toast或alert）
                if (typeof showToast === 'function') {
                    showToast('只有管理员可以删除数据', 'error');
                } else {
                    console.error('权限不足，只有管理员可以删除数据');
                }

                return false;
            }
        }
    }, true);
}

// 进度条相关函数
function showUploadProgress() {
    const progressContainer = document.getElementById('uploadProgressContainer');
    const progressBar = document.getElementById('uploadProgressBar');
    const progressText = document.getElementById('uploadProgressText');
    const progressPercent = document.getElementById('uploadProgressPercent');

    if (progressContainer) {
        progressContainer.classList.remove('hidden');
        progressBar.style.width = '0%';
        if (progressText) {
            progressText.innerHTML = '<i id="uploadProgressIcon" class="fas fa-clock mr-1"></i>准备上传...';
        }
        if (progressPercent) {
            progressPercent.textContent = '0%';
        }
    }
}

function hideUploadProgress() {
    const progressContainer = document.getElementById('uploadProgressContainer');
    if (progressContainer) {
        setTimeout(() => {
            progressContainer.classList.add('hidden');
        }, 1000); // 延迟1秒隐藏，让用户看到完成状态
    }
}

function updateUploadProgress(percent, text, icon = 'fa-upload') {
    const progressBar = document.getElementById('uploadProgressBar');
    const progressText = document.getElementById('uploadProgressText');
    const progressPercent = document.getElementById('uploadProgressPercent');

    if (progressBar) {
        progressBar.style.width = percent + '%';
    }
    if (progressText && text) {
        const iconElement = `<i id="uploadProgressIcon" class="fas ${icon} mr-1"></i>`;
        progressText.innerHTML = iconElement + text;
    }
    if (progressPercent) {
        progressPercent.textContent = Math.round(percent) + '%';
    }
}

function disableSubmitButton(disabled) {
    const submitBtn = document.querySelector('button[type="submit"]');
    if (submitBtn) {
        submitBtn.disabled = disabled;
        if (disabled) {
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i> 正在处理...';
            submitBtn.classList.add('opacity-50', 'cursor-not-allowed');
        } else {
            const isEditingMode = currentPsuId !== null;
            if (isEditingMode) {
                submitBtn.innerHTML = '<i class="fas fa-save mr-1"></i> 更新电源信息';
            } else {
                submitBtn.innerHTML = '<i class="fas fa-save mr-1"></i> 保存电源信息';
            }
            submitBtn.classList.remove('opacity-50', 'cursor-not-allowed');
        }
    }
}

// 带进度的上传函数
function uploadWithProgress(url, method, formData) {
    return new Promise((resolve, reject) => {
        const xhr = new XMLHttpRequest();

        // 设置超时时间（30秒）
        xhr.timeout = 30000;

        // 上传进度监听
        xhr.upload.addEventListener('progress', (e) => {
            if (e.lengthComputable) {
                const percentComplete = (e.loaded / e.total) * 70; // 上传占70%
                const uploadedMB = (e.loaded / 1024 / 1024).toFixed(1);
                const totalMB = (e.total / 1024 / 1024).toFixed(1);
                updateUploadProgress(percentComplete, `正在上传图片... (${uploadedMB}MB / ${totalMB}MB)`, 'fa-upload');
            }
        });

        // 请求状态变化监听
        xhr.addEventListener('readystatechange', () => {
            if (xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {
                updateUploadProgress(75, '上传完成，正在处理数据...', 'fa-check-circle');
            } else if (xhr.readyState === XMLHttpRequest.LOADING) {
                updateUploadProgress(85, '正在转换为WebP格式...', 'fa-sync fa-spin');
            }
        });

        // 请求完成监听
        xhr.addEventListener('load', () => {
            updateUploadProgress(100, '处理完成！', 'fa-check-circle');

            if (xhr.status >= 200 && xhr.status < 300) {
                try {
                    const response = JSON.parse(xhr.responseText);
                    resolve(response);
                } catch (e) {
                    reject(new Error('响应解析失败'));
                }
            } else {
                try {
                    const errorResponse = JSON.parse(xhr.responseText);
                    reject(new Error(errorResponse.message || '操作失败'));
                } catch (e) {
                    reject(new Error(`HTTP ${xhr.status}: ${xhr.statusText}`));
                }
            }
        });

        // 错误监听
        xhr.addEventListener('error', () => {
            reject(new Error('网络错误'));
        });

        // 超时监听
        xhr.addEventListener('timeout', () => {
            reject(new Error('请求超时'));
        });

        // 打开请求
        xhr.open(method, url);

        // 设置请求头（必须在open之后）
        const token = localStorage.getItem('token');
        if (token) {
            xhr.setRequestHeader('Authorization', `Bearer ${token}`);
        }

        // 发送请求
        xhr.send(formData);
    });
}
// 主题切换脚本
document.addEventListener('DOMContentLoaded', function () {
    // 获取主题切换按钮和图标
    const themeToggleBtn = document.getElementById('themeToggleBtn');
    const themeIcon = document.getElementById('themeIcon');
    const html = document.documentElement;

    // 从本地存储中获取主题偏好
    const savedTheme = localStorage.getItem('theme');
    if (savedTheme === 'dark') {
        html.classList.add('dark');
        html.classList.remove('light');
        themeIcon.classList.remove('fa-sun');
        themeIcon.classList.add('fa-moon');
        themeIcon.classList.remove('text-yellow-500');
        themeIcon.classList.add('text-blue-300');
    } else {
        html.classList.add('light');
        html.classList.remove('dark');
        themeIcon.classList.add('fa-sun');
        themeIcon.classList.remove('fa-moon');
        themeIcon.classList.add('text-yellow-500');
        themeIcon.classList.remove('text-blue-300');
    }

    // 切换主题
    themeToggleBtn.addEventListener('click', function () {
        if (html.classList.contains('dark')) {
            // 切换到明亮模式
            html.classList.remove('dark');
            html.classList.add('light');
            localStorage.setItem('theme', 'light');

            // 切换图标
            themeIcon.classList.add('fa-sun');
            themeIcon.classList.remove('fa-moon');
            themeIcon.classList.add('text-yellow-500');
            themeIcon.classList.remove('text-blue-300');

            // 添加旋转动画
            themeIcon.style.transform = 'rotate(360deg)';
            setTimeout(() => {
                themeIcon.style.transform = '';
            }, 500);

            // 显示提示
            showThemeToast('已切换到明亮模式', '#10B981');
        } else {
            // 切换到暗色模式
            html.classList.add('dark');
            html.classList.remove('light');
            localStorage.setItem('theme', 'dark');

            // 切换图标
            themeIcon.classList.remove('fa-sun');
            themeIcon.classList.add('fa-moon');
            themeIcon.classList.remove('text-yellow-500');
            themeIcon.classList.add('text-blue-300');

            // 添加旋转动画
            themeIcon.style.transform = 'rotate(360deg)';
            setTimeout(() => {
                themeIcon.style.transform = '';
            }, 500);

            // 显示提示
            showThemeToast('已切换到暗色模式', '#4B5563');
        }
    });

    // 显示主题切换提示
    function showThemeToast(message, bgColor) {
        const toast = document.getElementById('toast');
        if (!toast) return;

        toast.textContent = message;
        toast.style.backgroundColor = bgColor;
        toast.classList.remove('hidden');

        setTimeout(() => {
            toast.classList.add('hidden');
        }, 2000);
    }
});

// 页面加载完成后执行权限初始化
document.addEventListener('DOMContentLoaded', initPermissions);
