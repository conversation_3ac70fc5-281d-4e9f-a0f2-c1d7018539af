// DOM Elements
document.addEventListener('DOMContentLoaded', () => {
    // 主题切换功能
    const themeToggle = document.getElementById('theme-toggle');
    const htmlElement = document.documentElement;

    function applyTheme(theme) {
        if (theme === 'dark') {
            htmlElement.classList.add('dark-theme');
            if (themeToggle) {
                themeToggle.innerHTML = '<i class="fas fa-moon"></i>';
                themeToggle.title = '切换到亮色主题';
            }
        } else {
            htmlElement.classList.remove('dark-theme');
            if (themeToggle) {
                themeToggle.innerHTML = '<i class="fas fa-sun"></i>';
                themeToggle.title = '切换到暗色主题';
            }
        }
    }

    // 检查本地存储中的主题设置
    let currentTheme = localStorage.getItem('theme') || 'light';

    // 应用已保存的主题
    applyTheme(currentTheme);

    // 主题切换按钮点击事件
    if (themeToggle) {
        themeToggle.addEventListener('click', function() {
            currentTheme = currentTheme === 'light' ? 'dark' : 'light';
            applyTheme(currentTheme);
            localStorage.setItem('theme', currentTheme);
        });
    }

    // DOM Elements
    const caseForm = document.getElementById('caseForm');
    const imageInput = document.getElementById('image');
    const imagePreview = document.getElementById('imagePreview');
    const previewImage = document.getElementById('previewImage');
    const caseTableBody = document.getElementById('caseTableBody');
    const searchInput = document.getElementById('searchInput');
    const brandFilter = document.getElementById('brandFilter');
    const formFactorFilter = document.getElementById('formFactorFilter');
    const prevBtn = document.getElementById('prevBtn');
    const nextBtn = document.getElementById('nextBtn');
    const firstPage = document.getElementById('firstPage');
    const lastPage = document.getElementById('lastPage');
    const pageNumbers = document.getElementById('pageNumbers');
    const currentPageDisplay = document.getElementById('currentPageDisplay');
    const totalPagesDisplay = document.getElementById('totalPagesDisplay');
    const pageJump = document.getElementById('pageJump');
    const goToPage = document.getElementById('goToPage');
    const totalRecords = document.getElementById('totalRecords');
    const detailsModal = document.getElementById('detailsModal');
    const closeDetailsModal = document.getElementById('closeDetailsModal');
    const detailsImage = document.getElementById('detailsImage');
    const detailsInfo = document.getElementById('detailsInfo');
    const detailsEditBtn = document.getElementById('detailsEditBtn');
    const detailsDeleteBtn = document.getElementById('detailsDeleteBtn');
    const deleteConfirmModal = document.getElementById('deleteConfirmModal');
    const cancelDeleteBtn = document.getElementById('cancelDeleteBtn');
    const confirmDeleteBtn = document.getElementById('confirmDeleteBtn');
    const toast = document.getElementById('toast');

    // Global variables
    let currentPage = 1;
    let totalPages = 1;
    let cases = [];
    let brands = new Set();
    let selectedCaseId = null;
    let resizeTimeout;

    // API Endpoints
    const API_URL = {
        GET_ALL: '/api/cases',
        GET_BY_ID: (id) => `/api/cases/${id}`,
        CREATE: '/api/cases',
        UPDATE: (id) => `/api/cases/${id}`,
        DELETE: (id) => `/api/cases/${id}`
    };

    // Initialize the page
    init();

    async function init() {
        // Permission Check First
        const isAdminUser = await isAdmin();
        if (!isAdminUser) {
            if (caseForm) {
                // Disable form inputs
                const inputs = caseForm.querySelectorAll('input, select, textarea');
                inputs.forEach(input => input.disabled = true);
                
                // Disable submit button
                const submitBtn = caseForm.querySelector('button[type="submit"]');
                if (submitBtn) {
                    submitBtn.disabled = true;
                    submitBtn.title = '只有管理员可以操作';
                }

                // Disable smart-fill button
                const autoFillBtn = document.getElementById('autoFillBtn');
                if (autoFillBtn) {
                    autoFillBtn.disabled = true;
                    autoFillBtn.title = '只有管理员可以操作';
                }
            }
        } else {
            // Optional: Add admin badge
            const header = document.querySelector('h1.text-xl');
            if (header && !header.querySelector('.admin-badge')) {
                const adminBadge = document.createElement('span');
                adminBadge.className = 'admin-badge bg-green-500 text-white text-xs font-semibold ml-2 px-2 py-1 rounded-full';
                adminBadge.textContent = '管理员';
                header.appendChild(adminBadge);
            }
        }
    
        // Load cases
        loadCases();

        // Event listeners
        if (caseForm) {
            caseForm.addEventListener('submit', handleFormSubmit);
        }
        
        if (imageInput) {
            imageInput.addEventListener('change', handleImageChange);
        }
        
        // 智能识别按钮
        const autoFillBtn = document.getElementById('autoFillBtn');
        if (autoFillBtn) {
            autoFillBtn.addEventListener('click', parseCaseInfo);
        }
        
        if (searchInput) {
            searchInput.addEventListener('input', debounce(handleSearch, 300));
        }
        
        if (brandFilter) {
            brandFilter.addEventListener('change', handleFilter);
        }
        
        if (formFactorFilter) {
            formFactorFilter.addEventListener('change', handleFilter);
        }
        
        if (prevBtn) {
            prevBtn.addEventListener('click', handlePrevPage);
        }
        
        if (nextBtn) {
            nextBtn.addEventListener('click', handleNextPage);
        }
        
        if (firstPage) {
            firstPage.addEventListener('click', handleFirstPage);
        }
        
        if (lastPage) {
            lastPage.addEventListener('click', handleLastPage);
        }
        
        if (goToPage) {
            goToPage.addEventListener('click', handleGoToPage);
        }
        
        if (pageJump) {
            pageJump.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    handleGoToPage();
                }
            });
        }
        
        if (closeDetailsModal) {
            closeDetailsModal.addEventListener('click', closeModal);
        }
        
        if (detailsEditBtn) {
            detailsEditBtn.addEventListener('click', handleEdit);
        }
        
        if (detailsDeleteBtn) {
            detailsDeleteBtn.addEventListener('click', showDeleteConfirm);
        }
        
        if (cancelDeleteBtn) {
            cancelDeleteBtn.addEventListener('click', hideDeleteConfirm);
        }
        
        if (confirmDeleteBtn) {
            confirmDeleteBtn.addEventListener('click', handleDelete);
        }
        
        // 监听窗口大小变化
        window.addEventListener('resize', () => {
            // 使用防抖函数避免频繁触发
            clearTimeout(resizeTimeout);
            resizeTimeout = setTimeout(() => {
                renderCases();
            }, 250);
        });
    }

    // Debounce function to limit API calls
    function debounce(func, delay) {
        let timeout;
        return function() {
            const context = this;
            const args = arguments;
            clearTimeout(timeout);
            timeout = setTimeout(() => func.apply(context, args), delay);
        };
    }

    // Load cases with pagination and filters
    async function loadCases() {
        try {
            const search = searchInput.value.trim();
            const brand = brandFilter.value;
            const formFactor = formFactorFilter.value;
            
            const params = new URLSearchParams({
                page: currentPage,
                limit: 10,
                search: search,
                brand: brand,
                formFactor: formFactor
            });

            // Concurrently fetch cases and user permission status
            const [response, isAdminUser] = await Promise.all([
                fetchWithAuth(`${API_URL.GET_ALL}?${params}`),
                isAdmin() // from permission-helper.js
            ]);
            
            if (!response || !response.ok) {
                throw new Error('Server responded with an error');
            }
            
            const data = await response.json();

            cases = data.data;
            totalPages = Math.ceil(data.total / data.limit);
            
            renderCases(isAdminUser); // Pass permission status to renderer
            updatePaginationControls();
            updatePaginationInfo(data.data.length, data.total);
            
            // Extract and update brand options
            if (brands.size === 0 || !search) {
                collectBrands(data.data);
            }
        } catch (error) {
            console.error('Error loading cases:', error);
            showToast('加载机箱数据失败', 'error');
        }
    }

    // Render cases in the table
    function renderCases(isAdminUser) {
        caseTableBody.innerHTML = '';

        if (cases.length === 0) {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td colspan="7" class="py-4 text-center text-gray-500">暂无数据</td>
            `;
            caseTableBody.appendChild(row);
            return;
        }

        // 检测是否为移动设备
        const isMobile = window.innerWidth < 640;
        
        // 根据设备类型选择渲染方法
        if (isMobile) {
            renderMobileCards(isAdminUser);
        } else {
            renderDesktopTable(isAdminUser);
        }
    }

    // 移动端卡片式布局渲染
    function renderMobileCards(isAdminUser) {
        const tableElement = caseTableBody.closest('table');
        if (tableElement) {
            tableElement.style.display = 'block';
            tableElement.style.width = '100%';
            tableElement.style.maxWidth = '100%';
            tableElement.style.borderCollapse = 'collapse';
            tableElement.style.borderSpacing = '0';
            const theadElement = tableElement.querySelector('thead');
            if (theadElement) {
                theadElement.style.display = 'none';
            }
        }

        caseTableBody.style.display = 'block';
        caseTableBody.style.width = '100%';
        caseTableBody.style.maxWidth = '100%';

        cases.forEach((caseItem, index) => {
            const cardOuterContainer = document.createElement('div');
            cardOuterContainer.className = 'case-card-outer-container';
            cardOuterContainer.style.animation = `fadeIn 0.3s ease-in-out ${index * 0.05}s both`;

            const card = document.createElement('div');
            card.className = 'case-card-new';

            card.addEventListener('touchstart', function() {
                this.style.transform = 'scale(0.99)';
                this.style.boxShadow = '0 2px 8px rgba(0,0,0,0.05)';
            });
            card.addEventListener('touchend', function() {
                this.style.transform = 'scale(1)';
                this.style.boxShadow = '0 5px 15px rgba(0,0,0,0.07)';
            });

            // 卡片头部
            const cardHeader = document.createElement('div');
            cardHeader.style.padding = '10px 14px';
            cardHeader.style.backgroundColor = 'rgba(79, 70, 229, 0.05)';
            cardHeader.style.borderBottom = '1px solid rgba(79, 70, 229, 0.15)';
            cardHeader.style.display = 'flex';
            cardHeader.style.justifyContent = 'space-between';
            cardHeader.style.alignItems = 'center';

            const modelName = document.createElement('div');
            modelName.textContent = caseItem.model || '未知型号';
            modelName.style.fontSize = '1rem';
            modelName.style.fontWeight = 'bold';
            modelName.style.color = '#1a202c';
            modelName.style.wordBreak = 'break-word';
            modelName.style.flexGrow = '1';
            modelName.style.marginRight = '8px';

            const brandBadge = document.createElement('span');
            brandBadge.textContent = caseItem.brand || '未知';
            brandBadge.className = 'case-badge';
            
            cardHeader.appendChild(modelName);
            cardHeader.appendChild(brandBadge);

            // 卡片内容
            const cardBody = document.createElement('div');
            cardBody.style.padding = '12px 14px';
            cardBody.style.backgroundColor = 'white';
            cardBody.style.display = 'flex';
            cardBody.style.gap = '12px'; // Gap between image and info

            // 图片容器
            const imgContainer = document.createElement('div');
            imgContainer.style.width = '60px';
            imgContainer.style.height = '60px';
            imgContainer.style.borderRadius = '6px';
            imgContainer.style.border = '1px solid rgba(79, 70, 229, 0.2)';
            imgContainer.style.display = 'flex';
            imgContainer.style.alignItems = 'center';
            imgContainer.style.justifyContent = 'center';
            imgContainer.style.overflow = 'hidden';
            imgContainer.style.position = 'relative'; // 添加定位
            
            // 添加深色模式下的样式增强
            imgContainer.className = 'case-image-container';
            
            const imgElement = document.createElement('img');
            imgElement.alt = caseItem.model || '机箱图片';
            imgElement.style.width = '100%';
            imgElement.style.height = '100%';
            imgElement.style.objectFit = 'contain';
            imgElement.style.cursor = 'pointer';
            imgElement.className = 'case-image'; // 添加类名便于CSS选择器
            
            const defaultImage = '/images/default-case.png';
            
            // 存储原始图片地址
            const imageSrc = caseItem.image_url || defaultImage;
            imgElement.dataset.originalSrc = imageSrc;
            imgElement.src = imageSrc;
            
            imgElement.onerror = () => {
                console.log('[DEBUG] Mobile card image failed to load, using default');
                imgElement.src = defaultImage;
                imgElement.dataset.originalSrc = defaultImage;
            };
            
            // 直接为图片添加点击事件，移除中间层
            imgElement.onclick = (e) => {
                e.stopPropagation();
                openImageFullscreen(imgElement.dataset.originalSrc);
            };
            
            imgContainer.appendChild(imgElement);

            // 信息容器
            const infoContainer = document.createElement('div');
            infoContainer.style.flexGrow = '1';
            infoContainer.style.display = 'flex';
            infoContainer.style.flexDirection = 'column';
            infoContainer.style.justifyContent = 'center';
            infoContainer.style.gap = '4px';

            // 价格信息
            const priceInfo = document.createElement('div');
            priceInfo.innerHTML = caseItem.price 
                ? `<span style="font-weight: bold; color: #E53E3E; font-size: 0.95rem;">¥${caseItem.price}</span>` 
                : '<span style="color: #718096; font-size: 0.95rem;">价格未知</span>';
            infoContainer.appendChild(priceInfo);
            
            // 主板规格信息
            if (caseItem.form_factor) {
                const formFactorText = document.createElement('div');
                formFactorText.style.fontSize = '0.85rem';
                formFactorText.style.color = '#4A5568';
                formFactorText.textContent = `支持：${caseItem.form_factor}`;
                infoContainer.appendChild(formFactorText);
            }
            
            // 颜色和材质信息
            const colorMaterialInfo = document.createElement('div');
            colorMaterialInfo.style.fontSize = '0.85rem';
            colorMaterialInfo.style.color = '#718096';
            
            let colorMaterialText = '';
            if (caseItem.color) colorMaterialText += caseItem.color;
            if (caseItem.color && caseItem.material) colorMaterialText += ' | ';
            if (caseItem.material) colorMaterialText += caseItem.material;
            
            colorMaterialInfo.textContent = colorMaterialText || '无颜色/材质信息';
            infoContainer.appendChild(colorMaterialInfo);

            // 标签组
            const tagGroup = document.createElement('div');
            tagGroup.className = 'tag-group';

            // 添加尺寸标签
            if (caseItem.dimensions) {
                const dimensionsTag = document.createElement('span');
                dimensionsTag.className = 'spec-tag';
                dimensionsTag.style.backgroundColor = 'rgba(66, 153, 225, 0.1)';
                dimensionsTag.style.color = '#3182CE';
                dimensionsTag.style.border = '1px solid rgba(66, 153, 225, 0.2)';
                dimensionsTag.textContent = caseItem.dimensions;
                tagGroup.appendChild(dimensionsTag);
            }

            // 添加前置接口标签
            if (caseItem.front_io) {
                const frontIoTag = document.createElement('span');
                frontIoTag.className = 'spec-tag';
                frontIoTag.style.backgroundColor = 'rgba(56, 178, 172, 0.1)';
                frontIoTag.style.color = '#319795';
                frontIoTag.style.border = '1px solid rgba(56, 178, 172, 0.2)';
                frontIoTag.textContent = '前置接口';
                tagGroup.appendChild(frontIoTag);
            }

            // 添加散热支持标签
            if (caseItem.radiator_support) {
                const radiatorTag = document.createElement('span');
                radiatorTag.className = 'spec-tag';
                radiatorTag.style.backgroundColor = 'rgba(237, 100, 166, 0.1)';
                radiatorTag.style.color = '#D53F8C';
                radiatorTag.style.border = '1px solid rgba(237, 100, 166, 0.2)';
                radiatorTag.textContent = '支持水冷';
                tagGroup.appendChild(radiatorTag);
            }

            // 添加风扇标签
            if (caseItem.included_fans) {
                const fansTag = document.createElement('span');
                fansTag.className = 'spec-tag';
                fansTag.style.backgroundColor = 'rgba(154, 230, 180, 0.1)';
                fansTag.style.color = '#38A169';
                fansTag.style.border = '1px solid rgba(154, 230, 180, 0.2)';
                fansTag.textContent = caseItem.included_fans;
                tagGroup.appendChild(fansTag);
            }

            if (tagGroup.children.length > 0) {
                infoContainer.appendChild(tagGroup);
            }

            cardBody.appendChild(imgContainer);
            cardBody.appendChild(infoContainer);

            // 卡片底部操作区
            const cardFooter = document.createElement('div');
            cardFooter.style.padding = '8px 14px';
            cardFooter.style.backgroundColor = 'white';
            cardFooter.style.borderTop = '1px solid #e5e7eb';
            cardFooter.style.display = 'flex';
            cardFooter.style.justifyContent = 'space-around';
            cardFooter.style.alignItems = 'center';
            cardFooter.style.gap = '8px';

            // 查看按钮
            const viewButton = document.createElement('button');
            viewButton.innerHTML = '<i class="fas fa-eye mr-1"></i>查看';
            viewButton.className = 'mobile-action-btn mobile-action-view';
            viewButton.addEventListener('click', (e) => {
                e.stopPropagation(); // 阻止事件冒泡
                showDetails(caseItem.id);
            });

            // 编辑按钮
            const editButton = document.createElement('button');
            editButton.innerHTML = '<i class="fas fa-edit mr-1"></i>编辑';
            editButton.className = 'mobile-action-btn mobile-action-edit';
            editButton.addEventListener('click', (e) => {
                e.stopPropagation(); // 阻止事件冒泡
                editCase(caseItem.id);
            });

            // 删除按钮
            const deleteButton = document.createElement('button');
            deleteButton.innerHTML = '<i class="fas fa-trash mr-1"></i>删除';
            deleteButton.className = 'mobile-action-btn mobile-action-delete';
            deleteButton.addEventListener('click', (e) => {
                e.stopPropagation(); // 阻止事件冒泡
                confirmDelete(caseItem.id);
            });

            cardFooter.appendChild(viewButton);
            if (isAdminUser) {
                cardFooter.appendChild(editButton);
                cardFooter.appendChild(deleteButton);
            }

            // 组装卡片
            card.appendChild(cardHeader);
            card.appendChild(cardBody);
            card.appendChild(cardFooter);
            cardOuterContainer.appendChild(card);
            caseTableBody.appendChild(cardOuterContainer);
        });
    }

    // PC端表格布局渲染
    function renderDesktopTable(isAdminUser) {
        cases.forEach((caseItem, index) => {
            const row = document.createElement('tr');
            // 添加淡入动画和悬停效果
            row.className = 'hover:bg-gray-50 transition-colors border-b border-gray-200';
            row.style.animation = 'fadeIn 0.3s ease-in-out';
            row.style.animationFillMode = 'both';
            row.style.animationDelay = `${index * 0.05}s`;
            
            // 图片列
            const imageCell = document.createElement('td');
            imageCell.className = 'px-2 py-2 sm:px-3 sm:py-3 hidden sm:table-cell text-center';
            
            const imgContainer = document.createElement('div');
            imgContainer.className = 'w-10 h-10 mx-auto relative group';
            
            const defaultImage = '/images/default-case.png';
            const imgElement = document.createElement('img');
            imgElement.src = caseItem.image_url || defaultImage;
            imgElement.alt = caseItem.model || '机箱图片';
            imgElement.className = `w-10 h-10 object-contain mx-auto rounded-md border border-gray-200 ${!caseItem.image_url ? 'opacity-60' : ''} transition-transform group-hover:scale-110`;
            imgElement.dataset.originalSrc = imgElement.src; // 保存原始src以便预览
            imgElement.onclick = (e) => {
                e.stopPropagation();
                openImageFullscreen(imgElement.dataset.originalSrc);
            };
            imgElement.onerror = () => {
                console.log('[DEBUG] Image failed to load, using default');
                imgElement.src = defaultImage;
                imgElement.dataset.originalSrc = defaultImage;
            };
            
            // 添加查看图标提示
            const viewOverlay = document.createElement('div');
            viewOverlay.className = 'absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 rounded-md flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity';
            viewOverlay.innerHTML = '<i class="fas fa-search-plus text-white text-opacity-80"></i>';
            viewOverlay.style.cursor = 'pointer';
            viewOverlay.onclick = (e) => {
                e.stopPropagation();
                openImageFullscreen(imgElement.dataset.originalSrc);
            };
            
            imgContainer.appendChild(imgElement);
            imgContainer.appendChild(viewOverlay);
            imageCell.appendChild(imgContainer);
            row.appendChild(imageCell);

            // 型号列
            const modelCell = document.createElement('td');
            modelCell.className = 'px-2 py-2 sm:px-3 sm:py-3';
            
            const modelText = document.createElement('div');
            modelText.className = 'text-sm font-medium text-gray-900 text-truncate flex items-center';
            modelText.title = caseItem.model || '-';

            // 添加机箱图标
            const brandIconContainer = document.createElement('span');
            brandIconContainer.className = 'inline-block mr-2';
            brandIconContainer.innerHTML = '<i class="fas fa-server text-indigo-600"></i>';
            
            modelText.appendChild(brandIconContainer);
            
            // 型号文本
            const modelName = document.createElement('span');
            modelName.textContent = caseItem.model || '-';
            modelText.appendChild(modelName);
            
            // 添加颜色信息（如果有）
            if (caseItem.color) {
                const colorInfo = document.createElement('div');
                colorInfo.className = 'text-xs text-gray-500 mt-0.5';
                colorInfo.textContent = caseItem.color;
                modelCell.appendChild(colorInfo);
            }
            
            modelCell.insertBefore(modelText, modelCell.firstChild);
            row.appendChild(modelCell);

            // 品牌列
            const brandCell = document.createElement('td');
            brandCell.className = 'px-2 py-2 sm:px-3 sm:py-3 hidden sm:table-cell';
            
            // 品牌标签
            const brandBadge = document.createElement('span');
            brandBadge.className = 'case-badge transition-transform hover:scale-105';
            brandBadge.textContent = caseItem.brand || '-';
            brandCell.appendChild(brandBadge);
            
            row.appendChild(brandCell);

            // 规格列
            const formFactorCell = document.createElement('td');
            formFactorCell.className = 'px-2 py-2 sm:px-3 sm:py-3 hidden md:table-cell';
            
            const formFactorIcon = document.createElement('span');
            formFactorIcon.className = 'mr-1 text-amber-600';
            formFactorIcon.innerHTML = '<i class="fas fa-microchip"></i>';
            
            const formFactorTag = document.createElement('span');
            formFactorTag.className = 'spec-tag socket inline-flex items-center transition-all hover:shadow-sm';
            formFactorTag.appendChild(formFactorIcon);
            
            const formFactorText = document.createElement('span');
            formFactorText.textContent = caseItem.form_factor || '-';
            formFactorTag.appendChild(formFactorText);
            
            formFactorCell.appendChild(formFactorTag);
            row.appendChild(formFactorCell);

            // 颜色列
            const colorCell = document.createElement('td');
            colorCell.className = 'px-2 py-2 sm:px-3 sm:py-3 hidden md:table-cell';
            
            if (caseItem.color) {
                const colorIcon = document.createElement('span');
                colorIcon.className = 'mr-1 text-green-600';
                colorIcon.innerHTML = '<i class="fas fa-palette"></i>';
                
                const colorTag = document.createElement('span');
                colorTag.className = 'spec-tag cores inline-flex items-center transition-all hover:shadow-sm';
                colorTag.appendChild(colorIcon);
                
                const colorText = document.createElement('span');
                colorText.textContent = caseItem.color;
                colorTag.appendChild(colorText);
                
                colorCell.appendChild(colorTag);
            } else {
                colorCell.textContent = '-';
            }
            
            row.appendChild(colorCell);

            // 价格列
            const priceCell = document.createElement('td');
            priceCell.className = 'px-2 py-2 sm:px-3 sm:py-3 hidden sm:table-cell';
            
            const priceIcon = document.createElement('span');
            priceIcon.className = 'mr-1 text-purple-600';
            priceIcon.innerHTML = '<i class="fas fa-tag"></i>';
            
            const priceTag = document.createElement('span');
            priceTag.className = 'spec-tag freq inline-flex items-center transition-all hover:shadow-sm';
            priceTag.appendChild(priceIcon);
            
            const priceText = document.createElement('span');
            priceText.textContent = caseItem.price ? `¥${caseItem.price}` : '-';
            priceTag.appendChild(priceText);
            
            priceCell.appendChild(priceTag);
            row.appendChild(priceCell);

            // 操作列
            const actionCell = document.createElement('td');
            actionCell.className = 'px-2 py-2 sm:px-3 sm:py-3 text-center';

            // 操作按钮容器
            const actionContainer = document.createElement('div');
            actionContainer.className = 'flex justify-center space-x-2';

            // 查看按钮
            const viewButton = document.createElement('button');
            viewButton.className = 'p-1 text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded-full w-7 h-7 flex items-center justify-center transition-all';
            viewButton.innerHTML = '<i class="fas fa-eye"></i>';
            viewButton.title = '查看详情';
            viewButton.dataset.id = caseItem.id;
            viewButton.addEventListener('click', () => showDetails(caseItem.id));
            actionContainer.appendChild(viewButton);

            if (isAdminUser) {
                // 编辑按钮
                const editButton = document.createElement('button');
                editButton.className = 'p-1 text-green-600 hover:text-green-800 hover:bg-green-50 rounded-full w-7 h-7 flex items-center justify-center transition-all';
                editButton.innerHTML = '<i class="fas fa-edit"></i>';
                editButton.title = '编辑';
                editButton.dataset.id = caseItem.id;
                editButton.addEventListener('click', () => editCase(caseItem.id));
                actionContainer.appendChild(editButton);

                // 删除按钮
                const deleteButton = document.createElement('button');
                deleteButton.className = 'p-1 text-red-600 hover:text-red-800 hover:bg-red-50 rounded-full w-7 h-7 flex items-center justify-center transition-all';
                deleteButton.innerHTML = '<i class="fas fa-trash"></i>';
                deleteButton.title = '删除';
                deleteButton.dataset.id = caseItem.id;
                deleteButton.addEventListener('click', () => {
                    confirmDelete(caseItem.id);
                });
                actionContainer.appendChild(deleteButton);
            }

            actionCell.appendChild(actionContainer);
            row.appendChild(actionCell);

            caseTableBody.appendChild(row);
        });
    }

    // Collect unique brands for filter dropdown
    function collectBrands(data) {
        data.forEach(caseItem => {
            if (caseItem.brand) {
                brands.add(caseItem.brand);
            }
        });
        
        updateBrandOptions();
    }

    // Update brand filter options
    function updateBrandOptions() {
        // Save current selection
        const currentSelection = brandFilter.value;
        
        // Clear and rebuild options
        brandFilter.innerHTML = '<option value="">所有品牌</option>';
        
        // Sort brands alphabetically
        const sortedBrands = Array.from(brands).sort();
        
        sortedBrands.forEach(brand => {
            const option = document.createElement('option');
            option.value = brand;
            option.textContent = brand;
            brandFilter.appendChild(option);
        });
        
        // Restore selection if it exists
        if (currentSelection && sortedBrands.includes(currentSelection)) {
            brandFilter.value = currentSelection;
        }
    }

    // Update pagination controls
    function updatePaginationControls() {
        // Update button states
        if (prevBtn) {
            prevBtn.disabled = currentPage <= 1;
            prevBtn.classList.toggle('opacity-50', currentPage <= 1);
        }
        
        if (nextBtn) {
            nextBtn.disabled = currentPage >= totalPages || totalPages === 0;
            nextBtn.classList.toggle('opacity-50', currentPage >= totalPages || totalPages === 0);
        }
        
        if (firstPage) {
            firstPage.disabled = currentPage <= 1;
            firstPage.classList.toggle('opacity-50', currentPage <= 1);
        }
        
        if (lastPage) {
            lastPage.disabled = currentPage >= totalPages || totalPages === 0;
            lastPage.classList.toggle('opacity-50', currentPage >= totalPages || totalPages === 0);
        }
        
        // Set max value for page jump input
        if (pageJump) {
            pageJump.max = totalPages;
        }
        
        // Generate page number buttons
        if (pageNumbers) {
            pageNumbers.innerHTML = '';
            
            // Maximum number of page buttons to show
            const maxPageButtons = 5;
            
            // Calculate the range of page numbers to display
            let startPage = Math.max(1, currentPage - Math.floor(maxPageButtons / 2));
            let endPage = startPage + maxPageButtons - 1;
            
            // Adjust if end page exceeds total pages
            if (endPage > totalPages) {
                endPage = totalPages;
                startPage = Math.max(1, endPage - maxPageButtons + 1);
            }
            
            // Create page number buttons
            for (let i = startPage; i <= endPage; i++) {
                const pageButton = document.createElement('button');
                pageButton.type = 'button';
                pageButton.className = `px-3 py-1 border rounded-md text-sm ${
                    i === currentPage
                        ? 'bg-indigo-600 text-white border-indigo-600'
                        : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
                }`;
                pageButton.textContent = i;
                pageButton.addEventListener('click', () => {
                    currentPage = i;
                    loadCases();
                });
                
                pageNumbers.appendChild(pageButton);
            }
        }
    }

    // Handle go to first page
    function handleFirstPage() {
        if (currentPage !== 1) {
            currentPage = 1;
            loadCases();
        }
    }

    // Handle go to last page
    function handleLastPage() {
        if (currentPage !== totalPages) {
            currentPage = totalPages;
            loadCases();
        }
    }

    // Handle go to specific page
    function handleGoToPage() {
        if (pageJump && pageJump.value) {
            const pageNum = parseInt(pageJump.value);
            
            if (!isNaN(pageNum) && pageNum >= 1 && pageNum <= totalPages) {
                currentPage = pageNum;
                loadCases();
            }
            
            // Clear input after use
            pageJump.value = '';
        }
    }

    // Update pagination information text
    function updatePaginationInfo(displayed, total) {
        currentPageDisplay.textContent = currentPage;
        totalPagesDisplay.textContent = totalPages;
        totalRecords.textContent = total;
    }

    // Handle image upload and preview
    function handleImageChange(e) {
        const file = e.target.files[0];
        
        if (!file) {
            imagePreview.classList.add('hidden');
            return;
        }
        
        // Validate file type
        if (!['image/jpeg', 'image/png', 'image/gif'].includes(file.type)) {
            showToast('请选择JPEG、PNG或GIF格式的图片', 'error');
            e.target.value = '';
            return;
        }
        
        // Validate file size (max 5MB)
        if (file.size > 5 * 1024 * 1024) {
            showToast('图片大小不能超过5MB', 'error');
            e.target.value = '';
            return;
        }
        
        // 显示WebP转换提示
        const uploadHint = document.querySelector('.text-xs.text-slate-500.dark\\:text-slate-400');
        if (uploadHint && !uploadHint.querySelector('.upload-notification')) {
            const notification = document.createElement('div');
            notification.className = 'upload-notification mt-2 text-xs text-indigo-600 dark:text-indigo-400 flex items-center';
            notification.innerHTML = '<i class="fas fa-sync fa-spin mr-1"></i>图片将自动转换为WebP格式以优化加载速度 (文件大小: ' + 
                (file.size / 1024).toFixed(1) + 'KB)';
            uploadHint.appendChild(notification);
        }
        
        // Show preview
        const reader = new FileReader();
        reader.onload = (e) => {
            previewImage.src = e.target.result;
            imagePreview.classList.remove('hidden');

            // 添加点击预览功能
            previewImage.onclick = () => {
                openImageFullscreen(e.target.result);
            };
            previewImage.style.cursor = 'pointer';
            previewImage.title = '点击查看大图';
        };
        reader.readAsDataURL(file);
    }

    // Handle form submission
    async function handleFormSubmit(e) {
        e.preventDefault();

        const isAdminUser = await isAdmin();
        if (!isAdminUser) {
            showToast('权限不足，只有管理员可以操作', 'error');
            return;
        }
        
        // Validate required fields
        const model = document.getElementById('model').value.trim();
        const brand = document.getElementById('brand').value.trim();
        const formFactor = document.getElementById('formFactor').value.trim();
        
        if (!model || !brand || !formFactor) {
            showToast('请填写必填字段', 'error');
            return;
        }
        
        // Create form data
        const formData = new FormData(caseForm);
        
        // 添加图片处理提示
        const uploadedImageFile = formData.get('image');
        if (uploadedImageFile && uploadedImageFile.size > 0) {
            console.log('[DEBUG] 正在上传图片:', uploadedImageFile.name, '类型:', uploadedImageFile.type, '大小:', (uploadedImageFile.size / 1024).toFixed(1) + 'KB', '(将自动转换为WebP格式)');
            
            // 更新上传提示
            const uploadHint = document.querySelector('.upload-notification');
            if (uploadHint) {
                uploadHint.innerHTML = '<i class="fas fa-cloud-upload-alt mr-1"></i>图片正在上传并转换为WebP格式...';
            }
        }
        
        // 确保使用正确的字段名并处理特殊字段
        const fieldsToRename = {
            // 'oldCamelCaseId': 'new_snake_case_name'
            'formFactor': 'form_factor',
            'driveCapacity': 'drive_capacity',
            'expansionSlots': 'expansion_slots',
            'fanSupport': 'fan_supports',
            'radiatorSupport': 'radiator_support',
            'maxGpuLength': 'max_gpu_length',
            'maxCpuCoolerHeight': 'max_cpu_cooler_height',
            'frontIo': 'front_io',
            'includedFans': 'included_fans',
            'sidePanel': 'side_panel'
        };

        for (const [oldName, newName] of Object.entries(fieldsToRename)) {
            if (formData.has(oldName)) {
                const value = document.getElementById(oldName).value;
                formData.delete(oldName);
                formData.append(newName, value);
            }
        }
        
        // 单独处理psuShroud，因为它是一个select，但后端需要布尔字符串
        if (formData.has('psuShroud')) {
            const psuValue = document.getElementById('psuShroud').value; // '1' or '0'
            formData.delete('psuShroud');
            formData.append('psu_shroud', psuValue === '1' ? 'true' : 'false');
        }
        
        try {
            const url = selectedCaseId 
                ? API_URL.UPDATE(selectedCaseId) 
                : API_URL.CREATE;
            
            const method = selectedCaseId ? 'PUT' : 'POST';
            
            const response = await fetchWithAuth(url, {
                method: method,
                body: formData
            });
            
            if (!response || !response.ok) {
                throw new Error('Server responded with an error');
            }
            
            const result = await response.json();
            
            showToast(selectedCaseId ? '机箱更新成功' : '机箱添加成功', 'success');
            caseForm.reset();
            imagePreview.classList.add('hidden');
            selectedCaseId = null;
            currentPage = 1;
            loadCases();
        } catch (error) {
            console.error('Error saving case:', error);
            showToast('保存失败，请稍后重试', 'error');
        }
    }

    // Show case details
    async function showDetails(id) {
        try {
            const [response, isAdminUser] = await Promise.all([
                fetchWithAuth(API_URL.GET_BY_ID(id)),
                isAdmin()
            ]);
            
            if (!response || !response.ok) {
                throw new Error('Server responded with an error');
            }
            
            const caseItem = await response.json();
            selectedCaseId = caseItem.id;
            
            // --- UI Redesign for Details Modal (v7 - Glass Morphism & Modern UI) ---
            
            // 检查当前是否为深色主题
            const isDarkMode = document.documentElement.classList.contains('dark-theme');
            
            const detailsHTML = `
                <!-- Top Section: Image and Core Info -->
                <div class="flex flex-col md:flex-row gap-6 p-6">
                    <!-- Image -->
                    <div class="w-full md:w-1/3 flex-shrink-0 rounded-lg flex items-center justify-center ${isDarkMode ? 'bg-indigo-900/20' : 'bg-gray-50'} p-4 shadow-lg border ${isDarkMode ? 'border-indigo-700/50' : 'border-gray-200'}">
                        ${caseItem.image_url ?
                            `<img src="${caseItem.image_url}" alt="${caseItem.model}" class="max-h-60 w-auto object-contain rounded-md cursor-pointer transition-transform hover:scale-105" onclick="openImageFullscreen('${caseItem.image_url}')">` :
                            `<div class="text-center ${isDarkMode ? 'text-gray-500' : 'text-gray-400'} p-4">
                                <i class="fas fa-image text-6xl mb-2"></i>
                                <p class="text-sm">暂无图片</p>
                            </div>`
                        }
                    </div>
                    
                    <!-- Core Details -->
                    <div class="w-full md:w-2/3 flex flex-col">
                        <div class="${isDarkMode ? 'bg-indigo-900/20' : 'bg-gray-50'} p-4 rounded-lg border ${isDarkMode ? 'border-indigo-700/50' : 'border-gray-200'}">
                            <div class="inline-block ${isDarkMode ? 'bg-indigo-800/50 text-indigo-300' : 'bg-indigo-100 text-indigo-700'} px-3 py-1 rounded-md text-sm font-semibold mb-2">
                                ${caseItem.brand || '未知品牌'}
                            </div>
                            <h2 class="text-3xl font-bold ${isDarkMode ? 'text-indigo-100' : 'text-indigo-900'} mt-1">${caseItem.model || '未知型号'}</h2>
                            <div class="flex items-baseline mt-4">
                                <p class="text-3xl font-bold ${isDarkMode ? 'text-red-400' : 'text-red-600'}">${caseItem.price ? `¥${caseItem.price}` : '-'}</p>
                                ${caseItem.price ? `<span class="${isDarkMode ? 'text-gray-400' : 'text-gray-500'} text-sm ml-2">参考价格</span>` : ''}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Key Features Section -->
                <div class="px-6 pb-6">
                    <div class="grid grid-cols-2 md:grid-cols-3 gap-4">
                        <!-- Feature Badge: Motherboard Support -->
                        <div class="flex items-center p-3 rounded-xl ${isDarkMode ? 'bg-indigo-800/30' : 'bg-gray-50'} shadow-sm hover:shadow-md transition-all duration-300 transform hover:-translate-y-1 border ${isDarkMode ? 'border-indigo-700/50' : 'border-gray-200'}">
                            <div class="${isDarkMode ? 'bg-indigo-700/50' : 'bg-indigo-100'} p-2 rounded-lg mr-3">
                                <i class="fas fa-microchip fa-fw text-xl ${isDarkMode ? 'text-indigo-300' : 'text-indigo-700'}"></i>
                            </div>
                            <div>
                                <p class="text-xs font-medium ${isDarkMode ? 'text-indigo-200' : 'text-indigo-600'}">主板支持</p>
                                <p class="font-bold text-sm ${isDarkMode ? 'text-white' : 'text-indigo-900'}">${caseItem.form_factor || '-'}</p>
                            </div>
                        </div>
                        <!-- Feature Badge: GPU Length -->
                        <div class="flex items-center p-3 rounded-xl ${isDarkMode ? 'bg-amber-800/30' : 'bg-amber-50'} shadow-sm hover:shadow-md transition-all duration-300 transform hover:-translate-y-1 border ${isDarkMode ? 'border-amber-700/50' : 'border-amber-200'}">
                            <div class="${isDarkMode ? 'bg-amber-700/50' : 'bg-amber-200'} p-2 rounded-lg mr-3">
                                <i class="fas fa-ruler-vertical fa-fw text-xl ${isDarkMode ? 'text-amber-300' : 'text-amber-700'}"></i>
                            </div>
                            <div>
                                <p class="text-xs font-medium ${isDarkMode ? 'text-amber-200' : 'text-amber-600'}">显卡限长</p>
                                <p class="font-bold text-sm ${isDarkMode ? 'text-white' : 'text-amber-900'}">${caseItem.max_gpu_length ? `${caseItem.max_gpu_length}mm` : '-'}</p>
                            </div>
                        </div>
                        <!-- Feature Badge: CPU Cooler Height -->
                        <div class="flex items-center p-3 rounded-xl ${isDarkMode ? 'bg-blue-800/30' : 'bg-blue-50'} shadow-sm hover:shadow-md transition-all duration-300 transform hover:-translate-y-1 border ${isDarkMode ? 'border-blue-700/50' : 'border-blue-200'}">
                            <div class="${isDarkMode ? 'bg-blue-700/50' : 'bg-blue-200'} p-2 rounded-lg mr-3">
                                <i class="fas fa-fan fa-fw text-xl ${isDarkMode ? 'text-blue-300' : 'text-blue-700'}"></i>
                            </div>
                            <div>
                                <p class="text-xs font-medium ${isDarkMode ? 'text-blue-200' : 'text-blue-600'}">CPU散热器限高</p>
                                <p class="font-bold text-sm ${isDarkMode ? 'text-white' : 'text-blue-900'}">${caseItem.max_cpu_cooler_height ? `${caseItem.max_cpu_cooler_height}mm` : '-'}</p>
                            </div>
                        </div>
                        <!-- Feature Badge: Side Panel -->
                        <div class="flex items-center p-3 rounded-xl ${isDarkMode ? 'bg-teal-800/30' : 'bg-teal-50'} shadow-sm hover:shadow-md transition-all duration-300 transform hover:-translate-y-1 border ${isDarkMode ? 'border-teal-700/50' : 'border-teal-200'}">
                            <div class="${isDarkMode ? 'bg-teal-700/50' : 'bg-teal-200'} p-2 rounded-lg mr-3">
                                <i class="far fa-window-maximize fa-fw text-xl ${isDarkMode ? 'text-teal-300' : 'text-teal-700'}"></i>
                            </div>
                            <div>
                                <p class="text-xs font-medium ${isDarkMode ? 'text-teal-200' : 'text-teal-600'}">侧板</p>
                                <p class="font-bold text-sm ${isDarkMode ? 'text-white' : 'text-teal-900'}">${caseItem.side_panel || '-'}</p>
                            </div>
                        </div>
                        <!-- Feature Badge: PSU Shroud -->
                        <div class="flex items-center p-3 rounded-xl ${isDarkMode ? 'bg-purple-800/30' : 'bg-purple-50'} shadow-sm hover:shadow-md transition-all duration-300 transform hover:-translate-y-1 border ${isDarkMode ? 'border-purple-700/50' : 'border-purple-200'}">
                            <div class="${isDarkMode ? 'bg-purple-700/50' : 'bg-purple-200'} p-2 rounded-lg mr-3">
                                <i class="fas fa-plug fa-fw text-xl ${isDarkMode ? 'text-purple-300' : 'text-purple-700'}"></i>
                            </div>
                            <div>
                                <p class="text-xs font-medium ${isDarkMode ? 'text-purple-200' : 'text-purple-600'}">电源仓</p>
                                <p class="font-bold text-sm ${isDarkMode ? 'text-white' : 'text-purple-900'}">${caseItem.psu_shroud ? '有' : '无'}</p>
                            </div>
                        </div>
                         <!-- Feature Badge: Expansion Slots -->
                        <div class="flex items-center p-3 rounded-xl ${isDarkMode ? 'bg-green-800/30' : 'bg-green-50'} shadow-sm hover:shadow-md transition-all duration-300 transform hover:-translate-y-1 border ${isDarkMode ? 'border-green-700/50' : 'border-green-200'}">
                            <div class="${isDarkMode ? 'bg-green-700/50' : 'bg-green-200'} p-2 rounded-lg mr-3">
                                <i class="fas fa-arrows-alt-h fa-fw text-xl ${isDarkMode ? 'text-green-300' : 'text-green-700'}"></i>
                            </div>
                            <div>
                                <p class="text-xs font-medium ${isDarkMode ? 'text-green-200' : 'text-green-600'}">扩展槽</p>
                                <p class="font-bold text-sm ${isDarkMode ? 'text-white' : 'text-green-900'}">${caseItem.expansion_slots || '-'}</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Divider with icon -->
                <div class="relative px-6 py-4">
                    <div class="absolute inset-0 flex items-center">
                        <div class="w-full border-t-2 ${isDarkMode ? 'border-indigo-700/50' : 'border-indigo-200'}"></div>
                    </div>
                    <div class="relative flex justify-center">
                        <span class="px-6 py-1 ${isDarkMode ? 'bg-indigo-800 text-indigo-200 border-indigo-700' : 'bg-indigo-100 text-indigo-700 border-indigo-200'} text-sm font-medium rounded-full shadow-sm border">
                            <i class="fas fa-clipboard-list mr-2"></i>详细规格
                        </span>
                    </div>
                </div>

                <!-- Detailed Specs Section -->
                <div class="p-6 grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-6">
                    <!-- Cooling -->
                    <div class="${isDarkMode ? 'bg-blue-900/20' : 'bg-blue-50'} p-4 rounded-xl shadow-sm border ${isDarkMode ? 'border-blue-800/40' : 'border-blue-100'}">
                        <h4 class="text-base font-semibold mb-3 ${isDarkMode ? 'text-blue-200' : 'text-blue-800'} flex items-center">
                            <span class="flex items-center justify-center w-8 h-8 rounded-full ${isDarkMode ? 'bg-blue-800' : 'bg-blue-200'} mr-3 shadow-sm">
                                <i class="fas fa-wind fa-fw ${isDarkMode ? 'text-blue-300' : 'text-blue-700'}"></i>
                            </span>
                            散热
                        </h4>
                        <div class="space-y-3 text-sm ml-11">
                            <p class="flex items-center">
                                <span class="${isDarkMode ? 'text-blue-400' : 'text-blue-600'} font-medium min-w-24">风扇位:</span> 
                                <span class="font-medium ${isDarkMode ? 'text-white bg-blue-800/40' : 'text-gray-900 bg-blue-100/60'} ml-2 px-2 py-1 rounded-md">${caseItem.fan_supports || '-'}</span>
                            </p>
                            <p class="flex items-center">
                                <span class="${isDarkMode ? 'text-blue-400' : 'text-blue-600'} font-medium min-w-24">水冷排:</span> 
                                <span class="font-medium ${isDarkMode ? 'text-white bg-blue-800/40' : 'text-gray-900 bg-blue-100/60'} ml-2 px-2 py-1 rounded-md">${caseItem.radiator_support || '-'}</span>
                            </p>
                            <p class="flex items-center">
                                <span class="${isDarkMode ? 'text-blue-400' : 'text-blue-600'} font-medium min-w-24">附赠风扇:</span> 
                                <span class="font-medium ${isDarkMode ? 'text-white bg-blue-800/40' : 'text-gray-900 bg-blue-100/60'} ml-2 px-2 py-1 rounded-md">${caseItem.included_fans || '-'}</span>
                            </p>
                        </div>
                    </div>
                    
                     <!-- Storage -->
                    <div class="${isDarkMode ? 'bg-amber-900/20' : 'bg-amber-50'} p-4 rounded-xl shadow-sm border ${isDarkMode ? 'border-amber-800/40' : 'border-amber-100'}">
                        <h4 class="text-base font-semibold mb-3 ${isDarkMode ? 'text-amber-200' : 'text-amber-800'} flex items-center">
                            <span class="flex items-center justify-center w-8 h-8 rounded-full ${isDarkMode ? 'bg-amber-800' : 'bg-amber-200'} mr-3 shadow-sm">
                                <i class="fas fa-hdd fa-fw ${isDarkMode ? 'text-amber-300' : 'text-amber-700'}"></i>
                            </span>
                            存储
                        </h4>
                        <div class="space-y-3 text-sm ml-11">
                             <p class="flex items-center">
                                <span class="${isDarkMode ? 'text-amber-400' : 'text-amber-600'} font-medium min-w-24">硬盘位:</span> 
                                <span class="font-medium ${isDarkMode ? 'text-white bg-amber-800/40' : 'text-gray-900 bg-amber-100/60'} ml-2 px-2 py-1 rounded-md">${caseItem.drive_capacity || '-'}</span>
                            </p>
                        </div>
                    </div>

                    <!-- Physical Info -->
                    <div class="${isDarkMode ? 'bg-green-900/20' : 'bg-green-50'} p-4 rounded-xl shadow-sm border ${isDarkMode ? 'border-green-800/40' : 'border-green-100'}">
                        <h4 class="text-base font-semibold mb-3 ${isDarkMode ? 'text-green-200' : 'text-green-800'} flex items-center">
                            <span class="flex items-center justify-center w-8 h-8 rounded-full ${isDarkMode ? 'bg-green-800' : 'bg-green-200'} mr-3 shadow-sm">
                                <i class="fas fa-cube fa-fw ${isDarkMode ? 'text-green-300' : 'text-green-700'}"></i>
                            </span>
                            物理信息
                        </h4>
                        <div class="space-y-3 text-sm ml-11">
                            <p class="flex items-center">
                                <span class="${isDarkMode ? 'text-green-400' : 'text-green-600'} font-medium min-w-24">尺寸(mm):</span> 
                                <span class="font-medium ${isDarkMode ? 'text-white bg-green-800/40' : 'text-gray-900 bg-green-100/60'} ml-2 px-2 py-1 rounded-md">${caseItem.dimensions || '-'}</span>
                            </p>
                            <p class="flex items-center">
                                <span class="${isDarkMode ? 'text-green-400' : 'text-green-600'} font-medium min-w-24">材质:</span> 
                                <span class="font-medium ${isDarkMode ? 'text-white bg-green-800/40' : 'text-gray-900 bg-green-100/60'} ml-2 px-2 py-1 rounded-md">${caseItem.material || '-'}</span>
                            </p>
                            <p class="flex items-center">
                                <span class="${isDarkMode ? 'text-green-400' : 'text-green-600'} font-medium min-w-24">颜色:</span> 
                                <span class="font-medium ${isDarkMode ? 'text-white bg-green-800/40' : 'text-gray-900 bg-green-100/60'} ml-2 px-2 py-1 rounded-md">${caseItem.color || '-'}</span>
                            </p>
                            <p class="flex items-center">
                                <span class="${isDarkMode ? 'text-green-400' : 'text-green-600'} font-medium min-w-24">重量:</span> 
                                <span class="font-medium ${isDarkMode ? 'text-white bg-green-800/40' : 'text-gray-900 bg-green-100/60'} ml-2 px-2 py-1 rounded-md">${caseItem.weight ? `${caseItem.weight}kg` : '-'}</span>
                            </p>
                        </div>
                    </div>
                </div>

                <!-- Notes Section -->
                                  ${caseItem.notes ? `
                  <div class="px-6 pt-2 pb-6">
                    <div class="${isDarkMode ? 'bg-purple-900/20' : 'bg-purple-50'} p-4 rounded-xl shadow-sm border ${isDarkMode ? 'border-purple-800/40' : 'border-purple-100'}">
                        <h4 class="text-base font-semibold mb-3 ${isDarkMode ? 'text-purple-200' : 'text-purple-800'} flex items-center">
                            <span class="flex items-center justify-center w-8 h-8 rounded-full ${isDarkMode ? 'bg-purple-800' : 'bg-purple-200'} mr-3 shadow-sm">
                                <i class="fas fa-info-circle fa-fw ${isDarkMode ? 'text-purple-300' : 'text-purple-700'}"></i>
                            </span>
                            备注
                        </h4>
                        <div class="ml-11 ${isDarkMode ? 'bg-purple-800/40' : 'bg-purple-100'} p-3 rounded-lg border ${isDarkMode ? 'border-purple-700/30' : 'border-purple-200'}">
                            <p class="text-sm ${isDarkMode ? 'text-gray-100' : 'text-gray-800'}">${caseItem.notes}</p>
                        </div>
                    </div>
                </div>
                ` : ''}
            `;
            
            // Update modal content
            const modalContent = document.querySelector('#detailsModal .modal-content');
            if (modalContent) {
                modalContent.innerHTML = detailsHTML;
            }

            // Show/hide admin buttons based on permissions
            if (detailsEditBtn) {
                detailsEditBtn.style.display = isAdminUser ? 'inline-flex' : 'none';
            }
            if (detailsDeleteBtn) {
                detailsDeleteBtn.style.display = isAdminUser ? 'inline-flex' : 'none';
            }
            
            // Show modal
            detailsModal.classList.remove('hidden');
        } catch (error) {
            console.error('Error fetching case details:', error);
            showToast('获取机箱详情失败', 'error');
        }
    }

    // Edit case
    async function editCase(id) {
        const isAdminUser = await isAdmin();
        if (!isAdminUser) {
            showToast('权限不足，只有管理员可以编辑', 'error');
            return;
        }
        try {
            const response = await fetchWithAuth(API_URL.GET_BY_ID(id));
            
            if (!response || !response.ok) {
                throw new Error('Server responded with an error');
            }
            
            const caseItem = await response.json();
            selectedCaseId = caseItem.id;
            
            // Populate form fields
            document.getElementById('model').value = caseItem.model || '';
            document.getElementById('brand').value = caseItem.brand || '';
            document.getElementById('formFactor').value = caseItem.form_factor || '';
            document.getElementById('dimensions').value = caseItem.dimensions || '';
            document.getElementById('color').value = caseItem.color || '';
            document.getElementById('material').value = caseItem.material || '';
            document.getElementById('weight').value = caseItem.weight || '';
            document.getElementById('driveCapacity').value = caseItem.drive_capacity || '';
            document.getElementById('expansionSlots').value = caseItem.expansion_slots || '';
            document.getElementById('fanSupport').value = caseItem.fan_supports || '';
            document.getElementById('radiatorSupport').value = caseItem.radiator_support || '';
            document.getElementById('maxGpuLength').value = caseItem.max_gpu_length || '';
            document.getElementById('maxCpuCoolerHeight').value = caseItem.max_cpu_cooler_height || '';
            document.getElementById('frontIo').value = caseItem.front_io || '';
            document.getElementById('includedFans').value = caseItem.included_fans || '';
            document.getElementById('price').value = caseItem.price || '';
            document.getElementById('notes').value = caseItem.notes || '';
            document.getElementById('psuShroud').value = caseItem.psu_shroud ? '1' : (caseItem.psu_shroud === false ? '0' : '');
            document.getElementById('sidePanel').value = caseItem.side_panel || '';
            
            // Clear image input value as we can't set file input values for security reasons
            imageInput.value = '';
            
            // Show image preview if there's an image_url
            if (caseItem.image_url) {
                previewImage.src = caseItem.image_url;
                imagePreview.classList.remove('hidden');

                // 添加点击预览功能
                previewImage.onclick = () => {
                    openImageFullscreen(caseItem.image_url);
                };
                previewImage.style.cursor = 'pointer';
                previewImage.title = '点击查看大图';
            } else {
                imagePreview.classList.add('hidden');
            }
            
            // Scroll to form
            caseForm.scrollIntoView({ behavior: 'smooth' });
            
            // Close details modal if open
            closeModal();
        } catch (error) {
            console.error('Error fetching case for edit:', error);
            showToast('获取机箱信息失败', 'error');
        }
    }

    // Show delete confirmation
    async function confirmDelete(id) {
        const isAdminUser = await isAdmin();
        if (!isAdminUser) {
            showToast('权限不足，只有管理员可以删除', 'error');
            return;
        }
        selectedCaseId = id;
        deleteConfirmModal.classList.remove('hidden');
    }

    // Close details modal
    function closeModal() {
        detailsModal.classList.add('hidden');
    }

    // Show delete confirmation modal
    function showDeleteConfirm() {
        closeModal();
        deleteConfirmModal.classList.remove('hidden');
    }

    // Hide delete confirmation modal
    function hideDeleteConfirm() {
        deleteConfirmModal.classList.add('hidden');
    }

    // Handle delete
    async function handleDelete() {
        if (!selectedCaseId) return;

        const isAdminUser = await isAdmin();
        if (!isAdminUser) {
            showToast('权限不足，只有管理员可以删除', 'error');
            hideDeleteConfirm();
            return;
        }
        
        try {
            const response = await fetchWithAuth(API_URL.DELETE(selectedCaseId), {
                method: 'DELETE'
            });
            
            if (!response || !response.ok) {
                throw new Error('Server responded with an error');
            }
            
            hideDeleteConfirm();
            showToast('机箱删除成功', 'success');
            currentPage = 1;
            loadCases();
            selectedCaseId = null;
        } catch (error) {
            console.error('Error deleting case:', error);
            showToast('删除失败，请稍后重试', 'error');
        }
    }

    // Handle search
    function handleSearch() {
        currentPage = 1;
        loadCases();
    }

    // Handle filter change
    function handleFilter() {
        currentPage = 1;
        loadCases();
    }

    // Handle previous page
    function handlePrevPage() {
        if (currentPage > 1) {
            currentPage--;
            loadCases();
        }
    }

    // Handle next page
    function handleNextPage() {
        if (currentPage < totalPages) {
            currentPage++;
            loadCases();
        }
    }

    // Handle edit
    function handleEdit() {
        editCase(selectedCaseId);
    }

    // Show toast message
    function showToast(message, type = 'success') {
        // 移除现有toast
        const existingToast = document.getElementById('toast');
        if (existingToast) {
            existingToast.remove();
        }
        
        // 根据类型设置颜色
        let bgColor;
        let icon;
        if (type === 'success') {
            bgColor = '#4CAF50';
            icon = '<i class="fas fa-check-circle mr-2"></i>';
        } else if (type === 'error') {
            bgColor = '#F44336';
            icon = '<i class="fas fa-exclamation-circle mr-2"></i>';
        } else if (type === 'info') {
            bgColor = '#4F46E5';
            icon = '<i class="fas fa-info-circle mr-2"></i>';
        } else if (type === 'warning') {
            bgColor = '#FF9800';
            icon = '<i class="fas fa-exclamation-triangle mr-2"></i>';
        }
        
        // 创建toast元素
        const toast = document.createElement('div');
        toast.id = 'toast';
        toast.className = 'fixed top-16 left-1/2 transform -translate-x-1/2 px-4 py-2 rounded-md text-white z-50 shadow-lg transition-all duration-300 ease-in-out';
        toast.style.backgroundColor = bgColor;
        toast.style.opacity = '0';
        toast.innerHTML = `${icon}${message}`;
        
        // 添加到DOM
        document.body.appendChild(toast);
        
        // 显示动画
        setTimeout(() => {
            toast.style.opacity = '1';
        }, 10);
        
        // 3秒后隐藏
        setTimeout(() => {
            toast.style.opacity = '0';
            setTimeout(() => {
                if (toast.parentNode) toast.parentNode.removeChild(toast);
            }, 300);
        }, 3000);
    }

    // Open image in fullscreen mode
    function openImageFullscreen(src) {
        // 阻止事件传播，避免干扰其他功能
        if (event && event.stopPropagation) {
            event.stopPropagation();
        }
        
        // 创建一个临时的图片容器
        const container = document.createElement('div');
        container.className = 'viewer-container';
        container.style.display = 'none';
        document.body.appendChild(container);

        // 创建图片元素
        const img = document.createElement('img');
        img.src = src;
        
        // 添加图片加载失败的处理
        img.onerror = function() {
            console.log('[DEBUG] Fullscreen image failed to load, using default');
            this.src = '/images/default-case.png';
        };
        
        container.appendChild(img);

        // 初始化 Viewer
        const viewer = new Viewer(img, {
            backdrop: true,          // 启用背景遮罩
            button: true,           // 显示关闭按钮
            navbar: false,          // 隐藏底部导航栏（只有一张图片时不需要）
            title: false,           // 不显示标题
            toolbar: {              // 自定义工具栏
                zoomIn: true,       // 放大按钮
                zoomOut: true,      // 缩小按钮
                oneToOne: true,     // 1:1 尺寸按钮
                reset: true,        // 重置按钮
                prev: false,        // 上一张（隐藏，因为只有一张图片）
                play: false,        // 播放按钮（隐藏）
                next: false,        // 下一张（隐藏）
                rotateLeft: true,   // 向左旋转
                rotateRight: true,  // 向右旋转
                flipHorizontal: true, // 水平翻转
                flipVertical: true,  // 垂直翻转
            },
            viewed() {
                // 图片加载完成后自动打开查看器
                if (isMobile) {
                    viewer.zoomTo(0.8);  // 移动设备使用较小的初始缩放比例
                } else {
                    viewer.zoomTo(1);    // 桌面设备使用正常的缩放比例
                }
            },
            hidden() {
                // 查看器关闭后移除临时元素
                viewer.destroy();
                document.body.removeChild(container);
            },
            maxZoomRatio: 5,        // 最大缩放比例
            minZoomRatio: 0.1,      // 最小缩放比例
            transition: true,       // 启用过渡效果
            keyboard: true,         // 启用键盘支持
        });

        // 显示查看器
        viewer.show();
    }

    // 解析机箱信息的主函数
    async function parseCaseInfo() {
        let input = document.getElementById('smartInput').value.trim();
        if (!input) return showToast('请输入机箱参数', 'error');

        document.getElementById('autoFillBtn').disabled = true;
        document.getElementById('autoFillBtn').innerHTML = '<i class="fas fa-sync fa-spin mr-2"></i>正在解析...';

        try {
            console.log(`[Parse Start] Initial Input: "${input}"`);

            // 使用正则表达式解析
            console.log('[Parse] Using regex parsing');
            const regexResult = parseWithRegex(input);
            
            if (regexResult) {
                console.log('[Parse] Using regex parsing result:', regexResult);
                fillFormWithData(regexResult);
                document.getElementById('autoFillBtn').disabled = false;
                document.getElementById('autoFillBtn').innerHTML = '<i class="fas fa-magic mr-2"></i>智能识别';
                console.log('[Parse End] Parsing process finished.');
                return;
            }

            // 如果正则解析失败，回退到本地解析
            console.log('[Parse] Regex parsing failed, falling back to local parsing');

            // 本地解析逻辑
            const result = {
                brand: '',
                model: '',
                formFactor: '',
                dimensions: '',
                color: '',
                material: '',
                weight: '',
                driveCapacity: '',
                expansionSlots: '',
                fanSupport: '',
                radiatorSupport: '',
                maxGpuLength: '',
                maxCpuCoolerHeight: '',
                frontIo: '',
                includedFans: '',
                price: '',
                notes: '',
                // 添加缺失的字段
                psuShroud: '',
                sidePanel: ''
            };

            const originalInputForNotes = input;
            let remainingInput = input;
            console.log(`[Parse Detail] Initial remainingInput: "${remainingInput}"`);

            function extractAndRemove(regex, targetField, processFunc) {
                console.log(`[Parse Detail] Attempting to extract '${targetField}' with regex: ${regex}`);
                const match = remainingInput.match(regex);
                if (match && match[1]) {
                    let value = match[1].trim();
                    console.log(`[Parse Detail] Regex Matched for '${targetField}'. Raw matched value: "${match[0]}", Extracted part: "${value}"`);
                    if (processFunc) {
                        const processedValue = processFunc(value, match);
                        console.log(`[Parse Detail] Processed value for '${targetField}': "${processedValue}" (Original: "${value}")`);
                        value = processedValue;
                    }
                    result[targetField] = value;
                    const oldRemainingInput = remainingInput;
                    remainingInput = remainingInput.replace(match[0], '').trim();
                    console.log(`[Parse Detail] Successfully extracted '${targetField}': "${value}". Remaining input: "${remainingInput}" (was: "${oldRemainingInput}")`);
                    return true;
                } else {
                    console.log(`[Parse Detail] No match for '${targetField}' with regex: ${regex} on input: "${remainingInput}"`);
                }
                return false;
            }

            const labeledFields = {
                '机箱型号': { field: 'model' },
                '型号': { field: 'model' },
                '品牌': { field: 'brand' },
                '支持主板类型|主板类型': { field: 'formFactor' },
                '尺寸(?:\\s*\\（宽x高x深\\）)?': { field: 'dimensions' },
                '颜色': { field: 'color' },
                '材质': { field: 'material' },
                '重量(?:\\s*\\(kg\\))?': { 
                    field: 'weight', 
                    process: (val) => {
                        const numMatch = val.match(/(\d+(?:\.\d+)?)/);
                        if (numMatch) return numMatch[1];
                        return val;
                    } 
                },
                '硬盘容量': { field: 'driveCapacity' },
                '扩展槽数量': { 
                    field: 'expansionSlots', 
                    process: (val) => {
                        const numMatch = val.match(/(\d+)/);
                        if (numMatch) return numMatch[1];
                        return val;
                    } 
                },
                '风扇支持': { field: 'fanSupport' },
                '散热器支持': { field: 'radiatorSupport' },
                '最大显卡长度(?:\\s*\\(mm\\))?': { 
                    field: 'maxGpuLength', 
                    process: (val) => {
                        const numMatch = val.match(/(\d+)/);
                        if (numMatch) return numMatch[1];
                        return val;
                    } 
                },
                'CPU散热器最大高度(?:\\s*\\(mm\\))?': { 
                    field: 'maxCpuCoolerHeight', 
                    process: (val) => {
                        const numMatch = val.match(/(\d+)/);
                        if (numMatch) return numMatch[1];
                        return val;
                    } 
                },
                '前置接口': { field: 'frontIo' },
                '附带风扇': { field: 'includedFans' },
                '价格(?:\\s*\\(¥\\))?': { 
                    field: 'price', 
                    process: (val) => {
                        const numMatch = val.match(/(\d+(?:\.\d+)?)/);
                        if (numMatch) return numMatch[1];
                        return val;
                    } 
                },
                '备注': { field: 'notes' },
                // 添加缺失的字段
                '电源仓|是否有电源仓': { 
                    field: 'psuShroud',
                    process: (val) => {
                        // 统一处理电源仓布尔值
                        val = val.toLowerCase();
                        if (val === '是' || val === '有' || val === 'true' || val === '1') {
                            return 'true';
                        } else if (val === '否' || val === '无' || val === 'false' || val === '0') {
                            return 'false';
                        }
                        return val;
                    }
                },
                '侧板|侧板类型|侧板材质': { field: 'sidePanel' }
            };

            console.log('[Parse Phase] Starting Labeled Field Extraction.');
            for (const labelKey in labeledFields) {
                const { field, process } = labeledFields[labelKey];
                const escapedLabelKey = labelKey.replace(/[.*+?^${}()|[\]\\]/g, '\\\\$&');
                const regex = new RegExp(`${escapedLabelKey}[：:]\\s*([^、，,。]+)`, 'i');
                extractAndRemove(regex, field, process);
            }

            // 提取品牌
            if (!result.brand) {
                const brandList = [
                    '联力', '酷冷至尊', '恩杰', '追风者', '华硕', '微星', '安钛克', 
                    '迎广', '先马', '乔思伯', '海盗船', '爱国者', '鑫谷', '长城', 
                    '游戏悍将', '航嘉', '雷神', '金河田', '大水牛', '撒哈拉'
                ];
                const brandRegex = new RegExp(`(${brandList.join('|')})`, 'i');
                extractAndRemove(brandRegex, 'brand');
            }

            // 提取主板类型
            if (!result.formFactor) {
                const formFactorList = ['ATX', 'Micro-ATX', 'Mini-ITX', 'E-ATX', 'XL-ATX'];
                const formFactorRegex = new RegExp(`(${formFactorList.join('|')})`, 'i');
                extractAndRemove(formFactorRegex, 'formFactor');
            }

            // 尝试提取尺寸 (WxHxD 格式)
            if (!result.dimensions) {
                const dimensionsRegex = /(\d+(?:\.\d+)?)\s*[x×]\s*(\d+(?:\.\d+)?)\s*[x×]\s*(\d+(?:\.\d+)?)\s*(?:mm|厘米|cm)?/i;
                const match = remainingInput.match(dimensionsRegex);
                if (match) {
                    result.dimensions = `${match[1]}x${match[2]}x${match[3]}mm`;
                    remainingInput = remainingInput.replace(match[0], '').trim();
                    console.log(`[Parse Detail] Extracted dimensions: "${result.dimensions}"`);
                }
            }

            // 提取电源仓信息
            if (!result.psuShroud) {
                // 尝试通过关键词检测是否有电源仓
                const hasPsuShroudRegex = /(电源仓|底部电源仓|PSU仓|PSU盖|电源下置|电源独立仓)/i;
                if (hasPsuShroudRegex.test(remainingInput)) {
                    result.psuShroud = 'true';
                    console.log(`[Parse Detail] Detected presence of PSU shroud from context`);
                }
            }

            // 提取侧板类型
            if (!result.sidePanel) {
                const sidePanelTypes = ['钢化玻璃', '亚克力', '金属', '铝', '透明', '玻璃'];
                for (const type of sidePanelTypes) {
                    if (remainingInput.includes(type + '侧板') || remainingInput.includes('侧板' + type)) {
                        result.sidePanel = type;
                        console.log(`[Parse Detail] Extracted side panel type: "${result.sidePanel}"`);
                        break;
                    }
                }
            }

            // 最后使用fillFormWithData函数更新UI
            fillFormWithData(result);

            document.getElementById('autoFillBtn').disabled = false;
            document.getElementById('autoFillBtn').innerHTML = '<i class="fas fa-magic mr-2"></i>智能识别';
            console.log('[Parse End] Parsing process finished.');
        } catch (error) {
            console.error('[Parse Error] Error during parsing:', error);
            showToast('解析参数时发生错误: ' + error.message, 'error');
            document.getElementById('autoFillBtn').disabled = false;
            document.getElementById('autoFillBtn').innerHTML = '<i class="fas fa-magic mr-2"></i>智能识别';
            console.log('[Parse End] Parsing process finished.');
        }
    }

    // 使用正则表达式解析机箱参数
    function parseWithRegex(inputText) {
        try {
            console.log('[Regex Parse] Starting regex parsing with input:', inputText);

            // 预处理输入：替换特定替换字符
            let processedInput = inputText
                .replace(/：/g, ':') // 统一冒号
                .replace(/，/g, '、') // 统一分隔符
                .replace(/,/g, '、');

            // 初始化结果对象
            const result = {
                brand: '',
                model: '',
                formFactor: '',
                dimensions: '',
                color: '',
                material: '',
                weight: '',
                driveCapacity: '',
                expansionSlots: '',
                fanSupport: '',
                radiatorSupport: '',
                maxGpuLength: '',
                maxCpuCoolerHeight: '',
                frontIo: '',
                includedFans: '',
                price: '',
                notes: '',
                // 添加缺失的字段
                psuShroud: '',
                sidePanel: ''
            };

            // 分割键值对
            const pairs = processedInput.split(/、(?=[^:]+:)/);
            console.log('[Regex Parse] Split pairs:', pairs);

            // 字段映射表
            const fieldMappings = [
                { regex: /^品牌$/i, field: 'brand' },
                { regex: /^(机箱)?型号$/i, field: 'model' },
                { regex: /^支持主板类型$|^主板类型$/i, field: 'formFactor' },
                { regex: /^尺寸(\s*\(宽x高x深\))?$/i, field: 'dimensions' },
                { regex: /^颜色$/i, field: 'color' },
                { regex: /^材质$/i, field: 'material' },
                { regex: /^重量(\s*\(kg\))?$/i, field: 'weight' },
                { regex: /^硬盘容量$/i, field: 'driveCapacity' },
                { regex: /^扩展槽数量$/i, field: 'expansionSlots' },
                { regex: /^风扇支持$/i, field: 'fanSupport' },
                { regex: /^散热器支持$/i, field: 'radiatorSupport' },
                { regex: /^最大显卡长度(\s*\(mm\))?$/i, field: 'maxGpuLength' },
                { regex: /^CPU散热器最大高度(\s*\(mm\))?$/i, field: 'maxCpuCoolerHeight' },
                { regex: /^前置接口$/i, field: 'frontIo' },
                { regex: /^附带风扇$/i, field: 'includedFans' },
                { regex: /^价格(\s*\(¥\))?$/i, field: 'price' },
                { regex: /^备注$/i, field: 'notes' },
                // 添加缺失的字段映射
                { regex: /^电源仓$|^(是否)?有电源仓$/i, field: 'psuShroud' },
                { regex: /^侧板$|^侧板类型$|^侧板材质$/i, field: 'sidePanel' }
            ];

            // 解析每个字段
            pairs.forEach(pair => {
                // 对于没有冒号的部分（可能是型号或备注）
                if (!pair.includes(':')) {
                    // 如果很短，可能是型号
                    if (pair.trim().length < 30 && !result.model) {
                        result.model = pair.trim();
                        console.log(`[Regex Parse] Found standalone model: "${result.model}"`);
                    }
                    // 否则可能是一个备注
                    else if (!result.notes) {
                        result.notes = pair.trim();
                        console.log(`[Regex Parse] Found standalone notes: "${result.notes}"`);
                    }
                    return;
                }

                // 处理正常的键值对
                const match = pair.match(/([^:]+):\s*(.*)/);
                if (match) {
                    const key = match[1].trim();
                    let value = match[2].trim();

                    // 使用正则表达式匹配字段
                    let field = null;
                    for (const mapping of fieldMappings) {
                        if (mapping.regex.test(key)) {
                            field = mapping.field;
                            console.log(`[Regex Parse] Field "${key}" matched to "${field}" via regex`);
                            break;
                        }
                    }

                    if (field) {
                        // 特殊字段处理
                        if (field === 'weight' || field === 'expansionSlots' || field === 'maxGpuLength' || field === 'maxCpuCoolerHeight') {
                            // 提取数字部分
                            const numMatch = value.match(/(\d+(?:\.\d+)?)/);
                            if (numMatch) {
                                result[field] = numMatch[1];
                            } else {
                                result[field] = value;
                            }
                        }
                        else if (field === 'price') {
                            // 提取价格数字部分
                            const numMatch = value.match(/(\d+(?:\.\d+)?)/);
                            if (numMatch) {
                                result[field] = numMatch[1];
                            } else {
                                result[field] = value;
                            }
                        }
                        // 其他字段直接赋值
                        else {
                            result[field] = value;
                        }
                        console.log(`[Regex Parse] Mapped "${key}" to field "${field}" with value: "${result[field]}"`);
                    } else {
                        console.log(`[Regex Parse] Unknown field: "${key}" with value: "${value}"`);
                        // 如果是未知字段且备注为空，添加到备注中
                        if (!result.notes) {
                            result.notes = `${key}: ${value}`;
                        } else {
                            result.notes += `; ${key}: ${value}`;
                        }
                    }
                }
            });

            // 特殊情况：如果没有型号但有完整文本的第一行，可能是型号
            if (!result.model && inputText) {
                const firstLine = inputText.split(/[\r\n,，、]/)[0].trim();
                if (firstLine && firstLine.length < 30) {
                    result.model = firstLine;
                    console.log(`[Regex Parse] Using first line as model: "${result.model}"`);
                }
            }

            // 验证至少有一些基本信息被提取
            let filledFields = 0;
            for (const key in result) {
                if (result[key] && result[key].trim() !== '') {
                    filledFields++;
                }
            }

            if (filledFields < 2) {
                console.log('[Regex Parse] Not enough fields were parsed, result may be incomplete');
                return null;
            }

            console.log('[Regex Parse] Final result:', result);
            return result;
        } catch (error) {
            console.error('[Regex Parse] Error during regex parsing:', error);
            return null; // 解析失败返回null
        }
    }

    // 将解析结果填充到表单中
    function fillFormWithData(data) {
        console.log('[UI Update] Updating UI with recognized data:', data);
        
        // 遍历识别出的数据，更新表单字段
        Object.entries(data).forEach(([field, value]) => {
            if (value && value !== '暂无数据') {
                const element = document.getElementById(field);
                if (element) {
                    // 对于select元素，需要特殊处理
                    if (element.tagName === 'SELECT') {
                        // 尝试找到匹配的选项
                        const options = Array.from(element.options);
                        const option = options.find(opt => 
                            opt.value.toLowerCase() === value.toLowerCase() || 
                            opt.textContent.toLowerCase().includes(value.toLowerCase())
                        );
                        
                        if (option) {
                            element.value = option.value;
                        } else {
                            console.log(`[UI Update] No matching option found for ${field}: ${value}`);
                        }
                    } else {
                        element.value = value;
                    }
                    console.log(`[UI Update] Set field ${field} to ${value}`);
                } else {
                    console.log(`[UI Update] Element not found for field: ${field}`);
                }
            }
        });
        
        showToast('参数识别完成！', 'success');
    }

    // Make openImageFullscreen available globally
    window.openImageFullscreen = openImageFullscreen;
}); 










// 初始化权限控制
function initPermissions() {
    console.log('初始化权限控制...');
    
    // 如果没有权限控制模块，发出警告
    if (typeof setupPermissionBasedUI !== 'function') {
        console.warn('警告：权限控制模块未加载！请确保已引入permission-helper.js');
        return;
    }
    
    // 执行权限控制初始化
    setupPermissionBasedUI().then(() => {
        // 检查编辑按钮
        const editButtons = document.querySelectorAll('.btn-edit, [data-action="edit"]');
        const deleteButtons = document.querySelectorAll('.btn-delete, [data-action="delete"]');
        
        // 根据用户角色处理按钮
        isAdmin().then(isAdminUser => {
            if (!isAdminUser) {
                // 禁用表单
                const form = document.querySelector('form');
                if (form) {
                    const inputs = form.querySelectorAll('input, select, textarea');
                    inputs.forEach(input => {
                        input.setAttribute('disabled', 'disabled');
                    });
                    
                    const submitBtn = form.querySelector('button[type="submit"]');
                    if (submitBtn) {
                        submitBtn.setAttribute('disabled', 'disabled');
                        submitBtn.title = '只有管理员可以添加或修改数据';
                    }
                }
                
                // 隐藏或禁用编辑、删除按钮
                editButtons.forEach(btn => {
                    btn.style.display = 'none';
                });
                
                deleteButtons.forEach(btn => {
                    btn.style.display = 'none';
                });
                
                // 为所有删除操作添加权限验证拦截器
                interceptDeleteOperations();
            } else {
                // 添加管理员标识
                const header = document.querySelector('h1, h2');
                if (header) {
                    const adminBadge = document.createElement('span');
                    adminBadge.className = 'bg-green-500 text-white text-xs px-2 py-1 rounded ml-2';
                    adminBadge.innerText = '管理员';
                    header.appendChild(adminBadge);
                }
            }
        });
    });
}

// 拦截所有删除操作的请求
function interceptDeleteOperations() {
    // 保存原始的fetch函数
    const originalFetch = window.fetch;
    
    // 重写fetch函数以拦截删除请求
    window.fetch = async function(url, options) {
        // 检查是否为删除操作
        if (options && options.method === 'DELETE') {
            console.log('拦截到删除操作请求:', url);
            
            // 检查用户权限
            const isAdminUser = await isAdmin();
            if (!isAdminUser) {
                console.warn('权限不足，普通用户无法执行删除操作');
                // 返回权限错误响应
                return Promise.resolve(new Response(JSON.stringify({
                    error: '权限不足',
                    message: '只有管理员可以删除数据'
                }), {
                    status: 403,
                    headers: {
                        'Content-Type': 'application/json'
                    }
                }));
            }
        }
        
        // 对于非删除请求或管理员的删除请求，正常执行
        return originalFetch.apply(this, arguments);
    };
    
    // 添加全局点击事件拦截
    document.addEventListener('click', async function(event) {
        // 查找可能触发删除操作的元素
        const deleteButton = event.target.closest('.btn-delete, [data-action="delete"]');
        if (deleteButton) {
            const isAdminUser = await isAdmin();
            if (!isAdminUser) {
                // 阻止事件传播
                event.preventDefault();
                event.stopPropagation();
                console.warn('权限不足，普通用户无法执行删除操作');
                
                // 可选：显示提示消息（使用toast或alert）
                if (typeof showToast === 'function') {
                    showToast('只有管理员可以删除数据', 'error');
                } else {
                    console.error('权限不足，只有管理员可以删除数据');
                }
                
                return false;
            }
        }
    }, true);
}

// 页面加载完成后执行权限初始化
document.addEventListener('DOMContentLoaded', initPermissions);
