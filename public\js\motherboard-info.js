// 检测浏览器是否支持WebP


// 默认图片路径
const DEFAULT_MOTHERBOARD_IMAGE = '/images/default-motherboard.png';



// 页面加载时检测WebP支持
document.addEventListener('DOMContentLoaded', function() {
  checkWebPSupport();
});

document.addEventListener('DOMContentLoaded', function () {
    // DOM元素
    const motherboardForm = document.getElementById('motherboardForm');
    const motherboardModel = document.getElementById('motherboardModel');
    const brand = document.getElementById('brand');
    const motherboardType = document.getElementById('motherboardType');
    const chipset = document.getElementById('chipset');
    const formFactor = document.getElementById('formFactor');
    const memoryCapacity = document.getElementById('memoryCapacity');
    const memoryGeneration = document.getElementById('memoryGeneration');
    const memoryFrequency = document.getElementById('memoryFrequency');
    const biosVersion = document.getElementById('biosVersion');
    const releaseDate = document.getElementById('releaseDate');
    const cpuSocket = document.getElementById('cpuSocket');
    const cpuModel = document.getElementById('cpuModel');
    const maxTdp = document.getElementById('maxTdp');
    const overclockSupport = document.getElementById('overclockSupport');
    const pcieSlots = document.getElementById('pcieSlots');
    const m2Slots = document.getElementById('m2Slots');
    const sataSlots = document.getElementById('sataSlots');
    const usbSlots = document.getElementById('usbSlots');
    const wifiSupport = document.getElementById('wifiSupport');
    const powerPhases = document.getElementById('powerPhases');
    const vrmCooling = document.getElementById('vrmCooling');
    const powerConnector = document.getElementById('powerConnector');
    const motherboardImage = document.getElementById('motherboardImage');
    const imagePreview = document.getElementById('imagePreview');
    const imagePreviewContainer = document.getElementById('imagePreviewContainer');
    const removeImageBtn = document.getElementById('removeImageBtn');
    const warranty = document.getElementById('warranty');
    const notes = document.getElementById('notes');
    const motherboardTableBody = document.getElementById('motherboardTableBody');
    const motherboardSearch = document.getElementById('motherboardSearch');
    const brandFilter = document.getElementById('brandFilter');
    const totalCount = document.getElementById('totalCount');
    const totalRecords = document.getElementById('totalRecords');
    const prevPage = document.getElementById('prevPage');
    const nextPage = document.getElementById('nextPage');
    const pageInfo = document.getElementById('pageInfo');
    const firstPage = document.getElementById('firstPage');
    const lastPage = document.getElementById('lastPage');
    const pageNumbers = document.getElementById('pageNumbers');
    const currentPageDisplay = document.getElementById('currentPageDisplay');
    const totalPagesDisplay = document.getElementById('totalPagesDisplay');
    const pageJump = document.getElementById('pageJump');
    const goToPage = document.getElementById('goToPage');
    const motherboardModal = document.getElementById('motherboardModal');
    const closeModal = document.getElementById('closeModal');
    const closeModalBtn = document.getElementById('closeModalBtn');
    const motherboardDetails = document.getElementById('motherboardDetails');
    const editBtn = document.getElementById('editBtn');
    const deleteBtn = document.getElementById('deleteBtn');

    // 全局变量
    let currentPage = 1;
    const pageSize = 10;
    let motherboards = [];
    let imageFile = null;
    let currentMotherboardId = null;
    let isEditing = false;
    let isMobile = window.innerWidth < 640;
    
    // 占位图片URL
    const PLACEHOLDER_IMAGE = '/images/placeholder-motherboard.png';

    // 在全局变量下添加API端点常量
    const API_ENDPOINTS = {
        MOTHERBOARDS: '/api/motherboards',
        MOTHERBOARD: (id) => `/api/motherboards/${id}`
    };

    // 初始化
    init();

    // 初始化函数
    async function init() {
        // 首先进行权限检查
        const isAdminUser = await isAdmin();

        // 根据权限禁用表an
        if (!isAdminUser) {
            if (motherboardForm) {
                const inputs = motherboardForm.querySelectorAll('input, select, textarea');
                inputs.forEach(input => input.disabled = true);
                
                const submitBtn = motherboardForm.querySelector('button[type="submit"]');
                if (submitBtn) {
                    submitBtn.disabled = true;
                    submitBtn.title = '只有管理员可以操作';
                }

                const autoFillBtn = document.getElementById('autoFillBtn');
                if (autoFillBtn) {
                    autoFillBtn.disabled = true;
                    autoFillBtn.title = '只有管理员可以操作';
                }
            }
        }
        
        setupEventListeners();
        loadMotherboards();
        setupResponsiveLayout();
        createPlaceholderDataURI();
    }

    // 设置事件监听器
    function setupEventListeners() {
        // 图片上传
        motherboardImage.addEventListener('change', handleImageUpload);
        removeImageBtn.addEventListener('click', removeImage);

        // 表单提交
        motherboardForm.addEventListener('submit', handleFormSubmit);

        // 智能识别按钮
        const autoFillBtn = document.getElementById('autoFillBtn');
        if (autoFillBtn) {
            autoFillBtn.addEventListener('click', parseMotherboardInfo);
        }

        // 搜索和筛选
        motherboardSearch.addEventListener('input', debounce(() => {
            currentPage = 1;
            loadMotherboards();
        }, 300));

        brandFilter.addEventListener('change', () => {
            currentPage = 1;
            loadMotherboards();
        });
        
        // 芯片组筛选
        const chipsetFilter = document.getElementById('chipsetFilter');
        if (chipsetFilter) {
            chipsetFilter.addEventListener('change', () => {
                currentPage = 1;
                loadMotherboards();
            });
        }

        // 重置过滤器
        const resetFilterBtn = document.getElementById('resetFilterBtn');
        if (resetFilterBtn) {
            resetFilterBtn.addEventListener('click', () => {
                if (motherboardSearch) motherboardSearch.value = '';
                
                if (brandFilter) {
                    brandFilter.value = 'all';
                    console.log('重置品牌筛选为:', brandFilter.value);
                }
                
                if (chipsetFilter) {
                    chipsetFilter.value = 'all';
                    console.log('重置芯片组筛选为:', chipsetFilter.value);
                }
                
                currentPage = 1;
                loadMotherboards();
            });
        }

        // 分页
        prevPage.addEventListener('click', () => changePage(-1));
        nextPage.addEventListener('click', () => changePage(1));
        
        // 新增分页控件的事件监听
        if (firstPage) {
            firstPage.addEventListener('click', handleFirstPage);
        }
        
        if (lastPage) {
            lastPage.addEventListener('click', handleLastPage);
        }
        
        if (goToPage) {
            goToPage.addEventListener('click', handleGoToPage);
        }
        
        if (pageJump) {
            pageJump.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    handleGoToPage();
                }
            });
        }

        // 模态框
        closeModal.addEventListener('click', () => motherboardModal.classList.add('hidden'));
        closeModalBtn.addEventListener('click', () => motherboardModal.classList.add('hidden'));
        editBtn.addEventListener('click', handleEdit);
        deleteBtn.addEventListener('click', confirmDelete);

        // 添加窗口大小变化检测
        window.addEventListener('resize', debounce(handleWindowResize, 200));
    }

    // 设置响应式布局
    function setupResponsiveLayout() {
        const table = document.querySelector('.table-modern');
        if (table) {
            if (window.innerWidth < 640) {
                table.classList.add('mobile-table');
                table.classList.remove('mobile-table-none');
                console.log('Added mobile-table class for mobile view');
            } else {
                table.classList.remove('mobile-table');
                table.classList.add('mobile-table-none');
                console.log('Removed mobile-table class for desktop view');
            }
        } else {
            console.log('Table element not found');
        }
    }

    // 处理窗口大小变化
    function handleWindowResize() {
        setupResponsiveLayout();
        
        // 如果当前视图与上次渲染时的视图不同，重新加载表格
        const currentIsMobile = window.innerWidth < 640;
        if (currentIsMobile !== isMobile) {
            loadMotherboards();
        }
    }

    // 处理图片上传
    function handleImageUpload(e) {
        const file = e.target.files[0];
        if (!file) return;

        // 验证文件类型
        const validTypes = ['image/jpeg', 'image/png', 'image/gif'];
        if (!validTypes.includes(file.type)) {
            alert('请上传JPG、PNG或GIF格式的图片');
            return;
        }

        // 验证文件大小
        if (file.size > 5 * 1024 * 1024) { // 5MB
            alert('图片大小不能超过5MB');
            return;
        }

        imageFile = file;
        const reader = new FileReader();
        reader.onload = function (event) {
            imagePreview.src = event.target.result;
            imagePreviewContainer.classList.remove('hidden');

            // 添加点击预览功能
            imagePreview.onclick = () => {
                openImageFullscreen(event.target.result);
            };
            imagePreview.style.cursor = 'pointer';
            imagePreview.title = '点击查看大图';
            
            // 添加WebP转换提示
            if (supportWebP) {
                const fileSize = (file.size / 1024).toFixed(1);
                const estimatedWebPSize = (file.size / 1024 * 0.7).toFixed(1); // 预估WebP大小约为原始大小的70%
                console.log(`图片大小: ${fileSize}KB，预计转换为WebP后约: ${estimatedWebPSize}KB`);
                
                // 显示文件大小信息
                document.querySelector('.text-xs.text-gray-500').innerHTML = 
                    `图片大小: ${fileSize}KB，转换为WebP后约: ${estimatedWebPSize}KB <small>(WebP格式支持: <span class="text-green-500">是</span>)</small>`;
            } else {
                console.log('浏览器不支持WebP格式，但服务器仍会进行转换');
                
                // 显示不支持的提示
                document.querySelector('.text-xs.text-gray-500').innerHTML = 
                    `PNG, JPG, GIF 格式（将自动转换为WebP格式以优化加载速度）<small> - 你的浏览器可能不支持WebP预览</small>`;
            }
        };
        reader.readAsDataURL(file);
    }

    // 移除图片
    function removeImage() {
        motherboardImage.value = '';
        imagePreview.src = '';
        imagePreviewContainer.classList.add('hidden');
        imageFile = null;
    }

    // 处理表单提交
    async function handleFormSubmit(e) {
        e.preventDefault();

        const isAdminUser = await isAdmin();
        if (!isAdminUser) {
            return showErrorMessage('权限不足，无法提交。');
        }

        // 表单验证
        if (!motherboardModel.value.trim()) {
            showErrorMessage('请输入主板型号');
            motherboardModel.focus();
            return;
        }

        if (!brand.value) {
            showErrorMessage('请选择主板品牌');
            brand.focus();
            return;
        }

        // 创建FormData对象
        const formData = new FormData();
        formData.append('motherboardModel', motherboardModel.value.trim());
        formData.append('brand', brand.value);
        formData.append('motherboardType', motherboardType.value);
        formData.append('chipset', chipset.value);
        formData.append('formFactor', formFactor.value);
        formData.append('memoryCapacity', memoryCapacity.value);
        formData.append('memoryGeneration', memoryGeneration.value);
        formData.append('memoryFrequency', memoryFrequency.value);
        formData.append('biosVersion', biosVersion.value.trim());
        formData.append('releaseDate', releaseDate.value);
        formData.append('cpuSocket', cpuSocket.value);
        formData.append('cpuModel', cpuModel.value.trim());
        formData.append('maxTdp', maxTdp.value);
        formData.append('overclockSupport', overclockSupport.value);
        formData.append('pcieSlots', pcieSlots.value);
        formData.append('m2Slots', m2Slots.value);
        formData.append('sataSlots', sataSlots.value);
        formData.append('usbSlots', usbSlots.value);
        formData.append('wifiSupport', wifiSupport.value);
        formData.append('powerPhases', powerPhases.value.trim());
        formData.append('vrmCooling', vrmCooling.value.trim());
        formData.append('powerConnector', powerConnector.value.trim());
        formData.append('warranty', warranty.value);
        formData.append('notes', notes.value.trim());

        if (imageFile) {
            formData.append('image', imageFile);
            // 添加调试信息
            console.log('[DEBUG] 正在上传图片:', imageFile.name, '类型:', imageFile.type, '大小:', (imageFile.size / 1024).toFixed(1) + 'KB', '(将自动转换为WebP格式以优化加载速度)');
            
            // 显示上传提示
            showSuccessMessage('图片正在上传并转换为WebP格式，请稍候...');
        } else {
            // 如果用户未上传图片，告知后端使用默认图片
            console.log('[DEBUG] 用户未上传图片，将使用默认图片');
            formData.append('use_default_image', 'true');
        }

        // 提交表单
        const url = isEditing ? `/api/motherboards/${currentMotherboardId}` : '/api/motherboards';
        const method = isEditing ? 'PUT' : 'POST';

        // 显示进度条并禁用提交按钮
        showUploadProgress();
        disableSubmitButton(true);

        // 使用XMLHttpRequest来支持上传进度
        uploadWithProgress(url, method, formData)
        .then(data => {
            showSuccessMessage(isEditing ? '主板信息更新成功' : '主板信息添加成功');
            resetForm();
            loadMotherboards();
        })
        .catch(error => {
            showErrorMessage(error.message || '操作失败');
        })
        .finally(() => {
            // 隐藏进度条并恢复提交按钮
            hideUploadProgress();
            disableSubmitButton(false);
        });
    }

    // 重置表单
    function resetForm() {
        motherboardForm.reset();
        removeImage();
        isEditing = false;
        currentMotherboardId = null;
        // 更改提交按钮文本
        const submitBtn = motherboardForm.querySelector('button[type="submit"]');
        submitBtn.innerHTML = '<i class="fas fa-save mr-1 sm:mr-2"></i> 保存主板信息';
    }

    // 加载主板数据
    function loadMotherboards() {
        // 确保每次都重新获取DOM元素，避免缓存问题
        const searchInput = document.getElementById('motherboardSearch');
        const brandFilterSelect = document.getElementById('brandFilter');
        const chipsetFilterSelect = document.getElementById('chipsetFilter');
        
        // 获取搜索和筛选参数
        const searchTerm = searchInput ? searchInput.value.trim() : '';
        const brandValue = brandFilterSelect ? brandFilterSelect.value : 'all';
        const chipsetValue = chipsetFilterSelect ? chipsetFilterSelect.value : 'all';
        
        console.log('===== 开始加载主板数据 =====');
        console.log('- 当前页:', currentPage);
        console.log('- 搜索条件:', searchTerm ? searchTerm : '(无)');
        console.log('- 品牌筛选:', brandValue);
        console.log('- 芯片组筛选:', chipsetValue);
        
        // 构建查询参数
        const params = new URLSearchParams({
            page: currentPage,
            limit: pageSize
        });
        
        if (searchTerm) {
            params.append('search', searchTerm);
            console.log('* 添加搜索参数:', searchTerm);
        }
        
        if (brandValue && brandValue !== 'all') {
            params.append('brand', brandValue);
            console.log('* 添加品牌筛选参数:', brandValue);
        }
        
        if (chipsetValue && chipsetValue !== 'all') {
            params.append('chipset', chipsetValue);
            console.log('* 添加芯片组筛选参数:', chipsetValue);
        }
        
        const queryString = params.toString();
        console.log('最终查询字符串:', queryString);
        
        // 显示加载状态
        motherboardTableBody.innerHTML = `
            <tr>
                <td colspan="6" class="px-4 py-8 text-center">
                    <div class="flex flex-col items-center">
                        <div class="w-16 h-16 mb-3 flex items-center justify-center">
                            <div class="animate-spin rounded-full h-10 w-10 border-b-2 border-orange-500"></div>
                        </div>
                        <p class="text-gray-500 font-medium">加载数据中...</p>
                        <p class="text-gray-400 text-sm mt-1">请稍候</p>
                    </div>
                </td>
            </tr>
        `;
    
        console.log('开始加载主板数据，当前页:', currentPage, '搜索条件:', searchTerm, '品牌筛选:', brandValue, '芯片组筛选:', chipsetValue);
    
        // 从API获取数据，同时获取权限状态
        Promise.all([
            fetchWithAuth(`${API_ENDPOINTS.MOTHERBOARDS}?${params.toString()}`),
            isAdmin()
        ])
            .then(async ([response, isAdminUser]) => {
                console.log('API 响应状态:', response.status, response.statusText);
                
                if (!response || !response.ok) {
                    const data = await response.json().catch(() => ({}));
                    console.error('API 错误响应:', data);
                    throw new Error(data.message || '数据加载失败');
                }
                const data = await response.json();
                console.log('API 返回数据:', data);
    
                // 确保motherboards是一个数组
                if (Array.isArray(data.items)) {
                    motherboards = data.items;
                } else if (typeof data.items === 'object') {
                    // 处理一些API可能返回对象而不是数组的情况
                    motherboards = Object.values(data.items);
                } else {
                    motherboards = [];
                    console.warn('API返回的items不是数组或对象:', data.items);
                }
                
                // 确保total是数字类型
                const total = parseInt(data.total) || 0;
                totalRecords.textContent = total;
                console.log('记录总数:', total);
    
                // 如果返回的结果为空且当前不是第一页，尝试返回到第一页
                if (motherboards.length === 0 && currentPage > 1) {
                    console.log('当前页没有数据，返回第一页');
                    currentPage = 1;
                    loadMotherboards();
                    return;
                }
    
                console.log('准备渲染主板数据，数量:', motherboards.length, '管理员权限:', isAdminUser);
    
                // 确保表格为空后再渲染
                motherboardTableBody.innerHTML = '';
                
                // 更新全局变量
                isMobile = window.innerWidth < 640;
                
                // 为每个主板添加默认图片路径处理
                motherboards.forEach(motherboard => {
                    if (!motherboard.imageUrl) {
                        // 设置默认图片路径为系统提供的默认图片
                        motherboard.is_default_image = true;
                        motherboard.imageUrl = DEFAULT_MOTHERBOARD_IMAGE;
                    }
                });
                
                // 根据设备类型选择渲染方法
                if (isMobile) {
                    renderMobileCards(motherboards, isAdminUser);
                } else {
                    renderDesktopTable(motherboards, isAdminUser);
                }
                
                updatePagination();
                console.log('===== 主板数据加载完成 =====');
            })
            .catch(error => {
                console.error('数据加载错误:', error);
                motherboardTableBody.innerHTML = `
                    <tr>
                        <td colspan="6" class="px-3 py-4 text-center text-red-500">
                            加载失败: ${error.message}
                        </td>
                    </tr>
                `;
            });
    }

    // 渲染主板表格
    function renderMotherboardTable(data) {
        // 清除之前的内容
        motherboardTableBody.innerHTML = '';

        if (data.length === 0) {
            const emptyRow = document.createElement('tr');
            const emptyCell = document.createElement('td');
            emptyCell.colSpan = 6;
            emptyCell.className = 'px-4 py-8 text-center';
            
            // 创建更美观的空数据提示
            emptyCell.innerHTML = `
                <div class="flex flex-col items-center">
                    <div class="w-16 h-16 mb-4 flex items-center justify-center rounded-full bg-gray-100">
                        <i class="fas fa-database text-gray-400 text-2xl"></i>
                    </div>
                    <p class="text-gray-500 font-medium mb-1">暂无数据</p>
                    <p class="text-gray-400 text-sm">没有找到符合条件的主板记录</p>
                </div>
            `;
            
            emptyRow.appendChild(emptyCell);
            motherboardTableBody.appendChild(emptyRow);
            return;
        }

        // 更新全局变量
        isMobile = window.innerWidth < 640;
        
        // 为每个主板添加默认图片路径处理
        data.forEach(motherboard => {
            if (!motherboard.imageUrl) {
                // 设置默认图片路径为系统提供的默认图片
                motherboard.is_default_image = true;
                motherboard.imageUrl = DEFAULT_MOTHERBOARD_IMAGE;
            }
        });
        
        // 根据设备类型选择渲染方法
        if (isMobile) {
            renderMobileCards(data);
        } else {
            renderDesktopTable(data);
        }
    }

    // 创建占位符图片的base64编码
    function createPlaceholderDataURI() {
        // 如果全局变量不存在，创建一个BASE64编码的图片作为占位符
        if (!window.placeholderDataURI) {
            // 创建一个带有主板图标的SVG
            const svg = `
            <svg xmlns="http://www.w3.org/2000/svg" width="200" height="200" viewBox="0 0 200 200">
                <rect width="200" height="200" fill="#f8f9fa"/>
                <g transform="translate(65,65) scale(0.35)">
                    <path fill="#d1d5db" d="M400 32H48C21.5 32 0 53.5 0 80v352c0 26.5 21.5 48 48 48h352c26.5 0 48-21.5 48-48V80c0-26.5-21.5-48-48-48zM127 384.5c-8.8 0-16-7.2-16-16s7.2-16 16-16 16 7.2 16 16-7.2 16-16 16zm204-248c8.8 0 16 7.2 16 16s-7.2 16-16 16-16-7.2-16-16 7.2-16 16-16zM384 254c0 1.8-.8 3.2-2 4.3-1.3 1.2-3 2-4.8 1.9l-59.5-.7-86.4 60.2c-.9.7-2 1-3.1 1-.5 0-1 0-1.6-.2-.4-.1-.9-.4-1.3-.7l-59.5-45c-.7-.5-1.3-1.3-1.7-2.2-.4-.8-.4-1.8-.4-2.7v-81.8c0-.6.1-1.3.4-1.9.3-.7.7-1.3 1.2-1.8.6-.6 1.3-1 2-1.2s1.5-.4 2.3-.3l80.2 10.1c.7.1 1.3.3 1.9.5.6.3 1.1.7 1.6 1.2.8 1 1.3 2.2 1.4 3.6.2 2.4-1.6 4.6-4 5.1l-77 10c-1.3.2-2.5 1.3-2.7 2.8-.2 1 0 2 .7 2.8.5.5 1.2.9 1.9 1l66.5 8.3c1.6.2 2.9 1.5 3.1 3.2.2 1.7-1.1 3.3-2.7 3.7l-56.1 15.6c-.5.1-.9.4-1.3.7-.9.8-1.2 1.9-1 3.1.1.5.3 1 .6 1.4.8 1 2 1.5 3.4 1.3l40.8-5.2c1.3-.2 2.5.5 3 1.7.2.6.3 1.2.2 1.9-.2 1.5-1.4 2.7-3 2.8l-21.2 1.3c-.8 0-1.6.4-2.2 1-.8.9-1.1 2.1-.7 3.3.4 1.3 1.6 2.2 3 2.3l16.4 1.2c1.3.1 2.4.5 3.5 1.2.9.6 1.7 1.3 2.4 2.1.7.8 1.3 1.8 1.7 2.8.4 1 .6 2.1.6 3.2 0 3-.8 5.8-2.5 8.2-1.4 2-3.3 3.5-5.5 4.3-.9.4-1.9.6-2.9.8-1.1.1-2.2.2-3.3.2H126c-2 0-3.8-.7-5.3-1.8-1.6-1.3-2.7-3-3.3-5-.9-2.8-.6-6 1-8.5.9-1.5 2.2-2.7 3.8-3.6 1.6-.8 3.3-1.3 5.1-1.3h23c1.4 0 2.7-.8 3.3-2.1.6-1.3.4-2.8-.6-3.9-.8-.9-2-1.3-3.3-1.3H88.6c-1 0-1.9.3-2.7.9-.8.6-1.4 1.3-1.7 2.2-.4.8-.4 1.8-.4 2.7v66.2c0 1.2.3 2.4.9 3.5.6 1 1.4 1.9 2.4 2.4 1.6.9 3.7.9 5.3 0l59.5-33.6c.8-.5 1.7-.7 2.6-.7.9 0 1.7.2 2.5.7l59.5 33.6c.9.5 1.9.8 2.9.8s2-.3 2.9-.8c.9-.5 1.7-1.2 2.3-2.1.6-.9.9-1.9.9-3V254zm70-81.9c0 1.2-1 2.1-2.2 2.1h-36.7c-1 0-2-.7-2.1-1.8-.2-1.3.7-2.4 1.9-2.4h29c1 0 2-.7 2.1-1.8.1-.8-.2-1.5-.7-1.9-.5-.3-1.1-.4-1.7-.3h-26.7c-3.8 0-7.2-2.6-8.2-6.2-.9-3.4.5-6.9 3.3-8.8 1.5-1 3.2-1.6 5-1.6h17.2c.9 0 1.7-.5 2-1.3.2-.8 0-1.7-.7-2.2-.5-.3-1.1-.5-1.7-.3h-12.4c-1.2 0-2.1-1-2.1-2.2 0-1.1.8-2 1.8-2.1h54.5c1.2 0 2.2.9 2.2 2s-1 2-2.2 2h-16c-.8 0-1.6.2-2.3.6-.5.3-1 .8-1.2 1.3-.3.6-.4 1.3-.2 2 .3 1.2 1.3 2 2.5 2h18.3c1.2 0 2.2.9 2.2 2.1v6.2c0 1.2-1 2.1-2.2 2.1H414c-1.2 0-2.2 1-2.2 2.1 0 1 .7 1.8 1.7 2 .3.1.6.1.9 0h36.7c1.2 0 2.2.9 2.2 2.1-.1 0-.1 7.2-.3 7.2z"/>
                </g>
            </svg>`;

            // 将SVG转换为Base64编码
            const svgBase64 = 'data:image/svg+xml;base64,' + btoa(svg);
            window.placeholderDataURI = svgBase64;
        }
    }

    // 占位图片创建函数
    function createPlaceholderImage(altText) {
        // 创建占位符容器
        const placeholderDiv = document.createElement('div');
        placeholderDiv.className = 'bg-gray-100 rounded-md flex items-center justify-center shadow-sm';
        
        // 在PC端和移动端使用不同的尺寸
        if (isMobile) {
            placeholderDiv.style.width = '48px';
            placeholderDiv.style.height = '48px';
        } else {
            placeholderDiv.style.width = '40px';
            placeholderDiv.style.height = '40px';
        }
        
        // 如果有生成的占位符图，使用它
        if (window.placeholderDataURI) {
            const img = document.createElement('img');
            img.src = window.placeholderDataURI;
            img.alt = altText || '主板图片';
            img.className = 'w-full h-full object-cover';
            placeholderDiv.appendChild(img);
        } else {
            // 否则使用图标
            const icon = document.createElement('i');
            icon.className = 'fas fa-microchip text-gray-400';
            icon.style.fontSize = isMobile ? '18px' : '16px';
            placeholderDiv.appendChild(icon);
        }
        
        return placeholderDiv;
    }

    // 渲染移动端卡片式布局
    function renderMobileCards(data, isAdminUser) {
        // 清空表格内容
        motherboardTableBody.innerHTML = '';
        
        data.forEach((motherboard, index) => {
            // 创建一行作为容器
            const row = document.createElement('tr');
            row.className = 'motherboard-item';
            
            // 创建单元格包含卡片
            const cell = document.createElement('td');
            cell.colSpan = 6;
            cell.style.padding = '0';
            
            // 创建卡片外层容器，提供动画效果
            const cardOuterContainer = document.createElement('div');
            cardOuterContainer.className = 'motherboard-card-outer-container';
            cardOuterContainer.style.animation = `fadeIn 0.3s ease-in-out ${index * 0.05}s both`;
            
            // 创建卡片结构
            const card = document.createElement('div');
            card.className = 'motherboard-card-new';
            
            // 卡片头部 - 型号和品牌
            const cardHeader = document.createElement('div');
            cardHeader.className = 'motherboard-card-header';
            
            // 获取品牌特定的背景色
            const brandClass = getBrandClass(motherboard.brand);
            let headerBgColor = '#f8f9fa';
            let darkHeaderBgColor = '#1e293b';
            
            if (motherboard.brand === 'ASUS' || motherboard.brand === '华硕') {
                headerBgColor = 'rgba(0, 112, 201, 0.05)';
                darkHeaderBgColor = 'rgba(0, 112, 201, 0.1)';
            } else if (motherboard.brand === 'MSI' || motherboard.brand === '微星') {
                headerBgColor = 'rgba(234, 0, 41, 0.05)';
                darkHeaderBgColor = 'rgba(234, 0, 41, 0.1)';
            } else if (motherboard.brand === 'GIGABYTE' || motherboard.brand === '技嘉') {
                headerBgColor = 'rgba(255, 107, 0, 0.05)';
                darkHeaderBgColor = 'rgba(255, 107, 0, 0.1)';
            } else if (motherboard.brand === 'ASRock' || motherboard.brand === '华擎') {
                headerBgColor = 'rgba(44, 62, 80, 0.05)';
                darkHeaderBgColor = 'rgba(44, 62, 80, 0.1)';
            } else if (motherboard.brand === '七彩虹') {
                headerBgColor = 'rgba(255, 65, 108, 0.05)';
                darkHeaderBgColor = 'rgba(255, 65, 108, 0.1)';
            }
            
            cardHeader.style.backgroundColor = headerBgColor;
            cardHeader.dataset.darkBg = darkHeaderBgColor;
            
            // 显示主板型号，确保能完整显示
            const modelText = document.createElement('div');
            modelText.className = 'text-base font-semibold text-gray-800 dark:text-gray-200 model-text';
            modelText.style.overflow = 'hidden';
            modelText.style.textOverflow = 'ellipsis';
            modelText.style.whiteSpace = 'normal'; // 允许换行
            modelText.style.wordBreak = 'break-word'; // 允许在单词内换行
            modelText.style.flexGrow = '1';
            modelText.style.marginRight = '8px';
            modelText.style.fontSize = '0.9rem'; // 减小字体大小
            modelText.style.lineHeight = '1.2'; // 减小行高
            modelText.textContent = motherboard.motherboardModel || '-';
            
            // 品牌徽章
            const brandBadgeClass = `motherboard-brand-badge brand-${motherboard.brand ? motherboard.brand.toLowerCase() : 'default'}`;
            const brandBadge = document.createElement('span');
            brandBadge.className = brandBadgeClass;
            brandBadge.textContent = motherboard.brand || '未知';
            
            cardHeader.appendChild(modelText);
            cardHeader.appendChild(brandBadge);
            
            // 卡片主体
            const cardBody = document.createElement('div');
            cardBody.className = 'motherboard-card-body';
            
            // 图片容器
            const imgContainer = document.createElement('div');
            imgContainer.style.width = '50px';
            imgContainer.style.height = '50px';
            imgContainer.style.borderRadius = '6px';
            imgContainer.style.border = '1px solid rgba(0,0,0,0.08)';
            imgContainer.style.display = 'flex';
            imgContainer.style.alignItems = 'center';
            imgContainer.style.justifyContent = 'center';
            imgContainer.style.overflow = 'hidden';
            imgContainer.style.flexShrink = '0';
            
            if (motherboard.imageUrl) {
                const imgUrl = supportWebP && motherboard.imageUrl.toLowerCase().endsWith('.jpg') 
                    ? motherboard.imageUrl.replace(/\.jpg$/i, '.webp') 
                    : motherboard.imageUrl;
                
                const img = document.createElement('img');
                img.src = imgUrl;
                img.alt = motherboard.motherboardModel || '主板图片';
                img.style.width = '100%';
                img.style.height = '100%';
                img.style.objectFit = 'contain';
                img.style.cursor = 'pointer';
                img.onclick = () => openImageFullscreen(motherboard.imageUrl);
                
                imgContainer.appendChild(img);
                
                // 如果是默认图片，添加标记
                if (motherboard.is_default_image) {
                    const defaultBadge = document.createElement('div');
                    defaultBadge.className = 'absolute bottom-0 right-0 bg-gray-700 text-white text-xs px-0.5 rounded-tl-md';
                    defaultBadge.style.fontSize = '0.6rem';
                    defaultBadge.textContent = '默认';
                    imgContainer.appendChild(defaultBadge);
                }
                
                img.onerror = function() {
                    this.onerror = null;
                    imgContainer.innerHTML = '';
                    const placeholderText = document.createElement('span');
                    placeholderText.textContent = '主板图片';
                    placeholderText.style.fontSize = '0.7rem';
                    placeholderText.style.color = '#6b7280';
                    placeholderText.className = 'dark:text-gray-400';
                    imgContainer.appendChild(placeholderText);
                };
            } else {
                const placeholderText = document.createElement('span');
                placeholderText.textContent = '主板图片';
                placeholderText.style.fontSize = '0.7rem';
                placeholderText.style.color = '#6b7280';
                placeholderText.className = 'dark:text-gray-400';
                imgContainer.appendChild(placeholderText);
            }
            
            // 信息容器
            const infoContainer = document.createElement('div');
            infoContainer.style.flexGrow = '1';
            infoContainer.style.display = 'flex';
            infoContainer.style.flexDirection = 'column';
            infoContainer.style.justifyContent = 'center';
            infoContainer.style.gap = '6px'; // 增大间距
            infoContainer.style.marginLeft = '12px'; // 增大左边距
            infoContainer.style.fontSize = '0.85rem'; // 略微增大字体
            infoContainer.style.minWidth = '0'; // 允许内容收缩
            infoContainer.style.overflowWrap = 'break-word'; // 允许长词换行
            
            // 芯片组显示 - 使用更舒展的布局
            if (motherboard.chipset) {
                const chipsetText = document.createElement('div');
                chipsetText.innerHTML = `<span class="font-bold text-emerald-700 dark:text-emerald-400 text-[0.9rem]">${motherboard.chipset}</span>`;
                infoContainer.appendChild(chipsetText);
            }
            
            // CPU接口显示
            if (motherboard.cpuSocket) {
                const socketText = document.createElement('div');
                socketText.innerHTML = `<span class="text-gray-500 dark:text-gray-400 text-xs">接口:</span> <span class="text-gray-700 dark:text-gray-300">${motherboard.cpuSocket}</span>`;
                socketText.style.fontSize = '0.8rem';
                socketText.className = 'dark:text-gray-400';
                infoContainer.appendChild(socketText);
            }
            
            // 添加标签组 - 使用更舒展的布局
            const tagGroup = document.createElement('div');
            tagGroup.className = 'motherboard-tag-group';
            tagGroup.style.margin = '6px 0 0 0';
            tagGroup.style.gap = '6px';
            
            // 标签数组
            const tags = [];
            
            // 添加供电相数标签
            if (motherboard.powerPhases) {
                const powerTag = document.createElement('span');
                powerTag.className = 'motherboard-spec-tag tag-power';
                powerTag.innerHTML = `<i class="fas fa-bolt"></i>${motherboard.powerPhases}相`;
                tags.push(powerTag);
            }
            
            // 添加M.2接口标签
            if (motherboard.m2Slots) {
                const m2Tag = document.createElement('span');
                m2Tag.className = 'motherboard-spec-tag tag-cpu';
                m2Tag.innerHTML = `<i class="fas fa-hdd"></i>${motherboard.m2Slots}个`;
                tags.push(m2Tag);
            }
            
            // 添加内存标签
            if (motherboard.memoryGeneration) {
                const memoryTag = document.createElement('span');
                memoryTag.className = 'motherboard-spec-tag tag-memory';
                memoryTag.innerHTML = `<i class="fas fa-memory"></i>${motherboard.memoryGeneration}`;
                tags.push(memoryTag);
            }
            
            // 添加所有标签，提高完整性
            for (let i = 0; i < tags.length; i++) {
                tagGroup.appendChild(tags[i]);
            }
            
            if (tagGroup.children.length > 0) {
                infoContainer.appendChild(tagGroup);
            }
            
            // 显示SATA信息，使用更明显的样式
            if (motherboard.sataSlots) {
                const sataRow = document.createElement('div');
                sataRow.style.fontSize = '0.8rem';
                sataRow.className = 'text-gray-600 dark:text-gray-300 mt-2';
                sataRow.innerHTML = `SATA: <span class="text-gray-700 dark:text-gray-300 font-medium">${motherboard.sataSlots}个</span>`;
                infoContainer.appendChild(sataRow);
            }
            
            cardBody.appendChild(imgContainer);
            cardBody.appendChild(infoContainer);
            
            // 卡片底部 - 操作按钮 - 更舒展的布局
            const cardFooter = document.createElement('div');
            cardFooter.className = 'motherboard-card-footer';
            
            // 操作按钮 - 调整按钮尺寸
            const viewButton = document.createElement('button');
            viewButton.className = 'motherboard-action-btn btn-view';
            viewButton.innerHTML = '<i class="fas fa-eye mr-1"></i>查看';
            viewButton.style.fontSize = '0.8rem';
            viewButton.style.padding = '4px 10px';
            viewButton.addEventListener('click', () => viewMotherboardDetails(motherboard.id));
            
            const editButton = document.createElement('button');
            editButton.className = 'motherboard-action-btn btn-edit';
            editButton.innerHTML = '<i class="fas fa-edit mr-1"></i>编辑';
            editButton.style.fontSize = '0.8rem';
            editButton.style.padding = '4px 10px';
            editButton.addEventListener('click', () => editMotherboard(motherboard.id));
            
            const deleteButton = document.createElement('button');
            deleteButton.className = 'motherboard-action-btn btn-delete';
            deleteButton.innerHTML = '<i class="fas fa-trash mr-1"></i>删除';
            deleteButton.style.fontSize = '0.8rem';
            deleteButton.style.padding = '4px 10px';
            deleteButton.addEventListener('click', () => {
                currentMotherboardId = motherboard.id;
                confirmDelete();
            });
            
            cardFooter.appendChild(viewButton);
            if (isAdminUser) {
                cardFooter.appendChild(editButton);
                cardFooter.appendChild(deleteButton);
            }
            
            // 组装卡片
            card.appendChild(cardHeader);
            card.appendChild(cardBody);
            card.appendChild(cardFooter);
            
            cardOuterContainer.appendChild(card);
            cell.appendChild(cardOuterContainer);
            row.appendChild(cell);
            
            motherboardTableBody.appendChild(row);
            
            // 深色模式适配
            const handleThemeChange = () => {
                const isDark = document.documentElement.classList.contains('dark');
                if (isDark) {
                    cardHeader.style.backgroundColor = cardHeader.dataset.darkBg;
                } else {
                    cardHeader.style.backgroundColor = headerBgColor;
                }
            };
            
            // 初始执行一次
            handleThemeChange();
            
            // 监听主题变化
            const observer = new MutationObserver((mutations) => {
                mutations.forEach((mutation) => {
                    if (mutation.attributeName === 'class' && 
                        mutation.target === document.documentElement) {
                        handleThemeChange();
                    }
                });
            });
            
            observer.observe(document.documentElement, { attributes: true });
        });
    }
    
    // 桌面端表格布局渲染
    function renderDesktopTable(data, isAdminUser) {
        data.forEach((motherboard, index) => {
            const row = document.createElement('tr');
            row.className = 'hover:bg-gray-50 transition-colors border-b border-gray-200';
            // 添加淡入动画和延迟
            row.style.animation = 'fadeIn 0.3s ease-in-out';
            row.style.animationFillMode = 'both';
            row.style.animationDelay = `${index * 0.05}s`;

            // 图片单元格
            const imgCell = document.createElement('td');
            imgCell.className = 'px-2 py-2 sm:px-4 sm:py-3 whitespace-nowrap text-center';
            
            // 如果有图片，显示图片
            if (motherboard.imageUrl) {
                const imgContainer = document.createElement('div');
                imgContainer.className = 'relative inline-block';
                
                const img = document.createElement('img');
                img.src = motherboard.imageUrl;
                img.alt = motherboard.motherboardModel || '主板图片';
                img.className = 'h-10 w-10 rounded-md object-cover cursor-pointer hover:opacity-90';
                img.onclick = () => openImageFullscreen(motherboard.imageUrl);
                
                imgContainer.appendChild(img);
                
                // 如果是默认图片，添加标记
                if (motherboard.is_default_image) {
                    const defaultBadge = document.createElement('div');
                    defaultBadge.className = 'absolute bottom-0 right-0 bg-gray-700 text-white text-xs px-0.5 rounded-tl-md';
                    defaultBadge.style.fontSize = '0.6rem';
                    defaultBadge.textContent = '默认';
                    imgContainer.appendChild(defaultBadge);
                }
                
                imgCell.appendChild(imgContainer);
            } else {
                // 如果没有图片，显示占位符
                imgCell.appendChild(createPlaceholderImage(motherboard.motherboardModel));
            }
            
            // 将图片单元格添加到行中
            row.appendChild(imgCell);

            // 型号列
            const modelCell = document.createElement('td');
            modelCell.className = 'px-2 py-2 sm:px-4 sm:py-3 model-column';
            
            // 创建一个包含图片和文本的容器
            const modelContainer = document.createElement('div');
            modelContainer.className = 'flex items-center';
            
            // 文本信息容器
            const textContainer = document.createElement('div');
            textContainer.className = 'flex flex-col flex-1 min-w-0 py-1';
            
            // 主板型号
            const modelText = document.createElement('div');
            modelText.className = 'model-title font-medium text-gray-900';
            modelText.textContent = motherboard.motherboardModel || '-';
            // 添加完整的title提示
            modelText.title = motherboard.motherboardModel || '-';
            
            // 品牌信息
            const brandText = document.createElement('div');
            brandText.className = 'flex items-center text-xs mt-1';
            
            // 获取品牌类名
            const brandClass = getBrandClass(motherboard.brand);
            
            brandText.innerHTML = `
                <span class="text-gray-500">${getBrandIcon(motherboard.brand)}</span>
                <span class="brand-badge ${brandClass} ml-1">${motherboard.brand || '未知'}</span>
            `;
            
            textContainer.appendChild(modelText);
            textContainer.appendChild(brandText);
            
            modelContainer.appendChild(textContainer);
            modelCell.appendChild(modelContainer);
            row.appendChild(modelCell);

            // 推荐CPU列 - 简化结构确保只显示一行内容
            const cpuCell = document.createElement('td');
            cpuCell.className = 'px-2 py-2 sm:px-4 sm:py-3 cpu-column';
            
            if (motherboard.powerPhases) {
                // 创建供电相数标签
                const powerBadge = document.createElement('span');
                powerBadge.className = 'cpu-badge';
                powerBadge.textContent = motherboard.powerPhases;
                powerBadge.title = motherboard.powerPhases; // 添加title属性显示完整内容
                
                cpuCell.appendChild(powerBadge);
            } else {
                cpuCell.innerHTML = '<span class="text-gray-400">-</span>';
            }
            
            row.appendChild(cpuCell);

            // M.2接口列
            const m2SlotsCell = document.createElement('td');
            m2SlotsCell.className = 'px-2 py-2 sm:px-4 sm:py-3 text-sm cpu-model-column';
            
            // M.2接口数量
            const m2SlotsText = document.createElement('div');
            m2SlotsText.className = 'truncate text-gray-700';
            
            if (motherboard.m2Slots) {
                m2SlotsText.textContent = motherboard.m2Slots + ' 个接口';
                m2SlotsText.title = motherboard.m2Slots + ' 个M.2接口';
            } else {
                m2SlotsText.textContent = '-';
                m2SlotsText.title = '无数据';
            }
            
            m2SlotsCell.appendChild(m2SlotsText);
            row.appendChild(m2SlotsCell);

            // 操作列
            const actionCell = document.createElement('td');
            actionCell.className = 'px-2 py-2 sm:px-4 sm:py-3 text-center action-column';

            // 操作按钮容器
            const actionContainer = document.createElement('div');
            actionContainer.className = 'flex justify-center space-x-2';

            // 查看按钮
            const viewButton = document.createElement('button');
            viewButton.className = 'p-1 text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded-full w-7 h-7 flex items-center justify-center transition-all';
            viewButton.innerHTML = '<i class="fas fa-eye"></i>';
            viewButton.title = '查看详情';
            viewButton.addEventListener('click', () => viewMotherboardDetails(motherboard.id));
            viewButton.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-2px)';
            });
            viewButton.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
            });
            actionContainer.appendChild(viewButton);

            // 编辑按钮
            const editButton = document.createElement('button');
            editButton.className = 'p-1 text-green-600 hover:text-green-800 hover:bg-green-50 rounded-full w-7 h-7 flex items-center justify-center transition-all';
            editButton.innerHTML = '<i class="fas fa-edit"></i>';
            editButton.title = '编辑';
            editButton.addEventListener('click', () => editMotherboard(motherboard.id));
            editButton.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-2px)';
            });
            editButton.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
            });
            actionContainer.appendChild(editButton);

            // 删除按钮
            const deleteButton = document.createElement('button');
            deleteButton.className = 'p-1 text-red-600 hover:text-red-800 hover:bg-red-50 rounded-full w-7 h-7 flex items-center justify-center transition-all';
            deleteButton.innerHTML = '<i class="fas fa-trash"></i>';
            deleteButton.title = '删除';
            deleteButton.addEventListener('click', () => {
                currentMotherboardId = motherboard.id;
                confirmDelete();
            });
            deleteButton.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-2px)';
            });
            deleteButton.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
            });
            actionContainer.appendChild(deleteButton);

            actionCell.appendChild(actionContainer);
            row.appendChild(actionCell);

            motherboardTableBody.appendChild(row);
        });
    }

    // 获取品牌对应的图标
    function getBrandIcon(brand) {
        if (!brand) return '<i class="fas fa-building"></i>';
        
        const brandLower = brand.toLowerCase();
        
        if (brandLower.includes('华硕') || brandLower.includes('asus')) {
            return '<i class="fas fa-laptop"></i>';
        } else if (brandLower.includes('微星') || brandLower.includes('msi')) {
            return '<i class="fas fa-dragon"></i>';
        } else if (brandLower.includes('技嘉') || brandLower.includes('gigabyte')) {
            return '<i class="fas fa-feather-alt"></i>';
        } else if (brandLower.includes('华擎') || brandLower.includes('asrock')) {
            return '<i class="fas fa-shield-alt"></i>';
        } else if (brandLower.includes('七彩虹') || brandLower.includes('colorful')) {
            return '<i class="fas fa-palette"></i>';
        } else {
            return '<i class="fas fa-microchip"></i>';
        }
    }
    
    // 获取品牌对应的CSS类名
    function getBrandClass(brand) {
        if (!brand) return 'brand-other';
        
        const brandLower = brand.toLowerCase();
        
        if (brandLower.includes('华硕') || brandLower.includes('asus')) {
            return 'brand-asus';
        } else if (brandLower.includes('微星') || brandLower.includes('msi')) {
            return 'brand-msi';
        } else if (brandLower.includes('技嘉') || brandLower.includes('gigabyte')) {
            return 'brand-gigabyte';
        } else if (brandLower.includes('华擎') || brandLower.includes('asrock')) {
            return 'brand-asrock';
        } else if (brandLower.includes('七彩虹') || brandLower.includes('colorful')) {
            return 'brand-colorful';
        } else {
            return 'brand-other';
        }
    }
    
    // 检测CPU类型（AMD或Intel）
    function detectCpuType(cpuModel) {
        if (!cpuModel) return '';
        
        const cpuLower = cpuModel.toLowerCase();
        
        if (cpuLower.includes('ryzen') || cpuLower.includes('amd') || cpuLower.includes('锐龙')) {
            return 'amd';
        } else if (cpuLower.includes('intel') || cpuLower.includes('i3') || cpuLower.includes('i5') || 
                  cpuLower.includes('i7') || cpuLower.includes('i9') || cpuLower.includes('英特尔')) {
            return 'intel';
        } else {
            return '';
        }
    }

    // 更新分页
    function updatePagination() {
        const total = parseInt(totalRecords.textContent);
        const totalPages = Math.ceil(total / pageSize) || 1;
        
        // 更新页码显示
        if (currentPageDisplay) currentPageDisplay.textContent = currentPage;
        if (totalPagesDisplay) totalPagesDisplay.textContent = totalPages;
        
        // 更新按钮状态
        prevPage.disabled = currentPage <= 1;
        nextPage.disabled = currentPage >= totalPages || totalPages === 0;
        
        if (firstPage) {
            firstPage.disabled = currentPage <= 1;
            firstPage.classList.toggle('opacity-50', currentPage <= 1);
        }
        
        if (lastPage) {
            lastPage.disabled = currentPage >= totalPages || totalPages === 0;
            lastPage.classList.toggle('opacity-50', currentPage >= totalPages || totalPages === 0);
        }
        
        prevPage.classList.toggle('opacity-50', prevPage.disabled);
        nextPage.classList.toggle('opacity-50', nextPage.disabled);
        
        // 设置页码跳转输入框最大值
        if (pageJump) {
            pageJump.max = totalPages;
            pageJump.placeholder = `1-${totalPages}`;
        }
        
        // 生成页码按钮
        if (pageNumbers) {
            pageNumbers.innerHTML = '';
            
            // 最多显示5个页码按钮
            const maxPageButtons = 5;
            
            // 计算要显示的页码范围
            let startPage = Math.max(1, currentPage - Math.floor(maxPageButtons / 2));
            let endPage = startPage + maxPageButtons - 1;
            
            // 调整结束页码，确保不超过总页数
            if (endPage > totalPages) {
                endPage = totalPages;
                startPage = Math.max(1, endPage - maxPageButtons + 1);
            }
            
            // 创建页码按钮
            for (let i = startPage; i <= endPage; i++) {
                const pageButton = document.createElement('button');
                pageButton.type = 'button';
                
                // 使用更美观的按钮样式
                if (i === currentPage) {
                    pageButton.className = 'px-3 py-1 rounded-md text-sm bg-orange-600 text-white border border-orange-600 transition-colors';
                } else {
                    pageButton.className = 'px-3 py-1 border rounded-md text-sm bg-white text-gray-700 border-gray-300 hover:bg-gray-50 hover:text-orange-600 transition-colors';
                }
                
                pageButton.textContent = i;
                pageButton.addEventListener('click', () => {
                    if (currentPage !== i) {
                    currentPage = i;
                    loadMotherboards();
                    }
                });
                
                pageNumbers.appendChild(pageButton);
            }
        }
    }

    // 切换页面
    function changePage(change) {
        const newPage = currentPage + change;
        const totalPages = Math.ceil(totalRecords.textContent / pageSize);

        if (newPage < 1 || newPage > totalPages) {
            return;
        }

        currentPage = newPage;
        loadMotherboards();
    }

    // 查看主板详情
    async function viewMotherboardDetails(id) {
        // 查看详情时，显示加载状态
        motherboardDetails.innerHTML = `
            <div class="flex justify-center items-center h-40">
                <i class="fas fa-spinner fa-spin mr-2"></i> 加载数据中...
            </div>
        `;
        motherboardModal.classList.remove('hidden');

        try {
            // 从API获取详细数据
            const response = await fetchWithAuth(API_ENDPOINTS.MOTHERBOARD(id));
            if (!response || !response.ok) {
                throw new Error('获取详情失败');
            }
            const motherboard = await response.json();
            const isAdminUser = await isAdmin();

            currentMotherboardId = id;
            renderMotherboardDetails(motherboard);

            // 根据权限显示或隐藏按钮
            editBtn.style.display = isAdminUser ? 'inline-flex' : 'none';
            deleteBtn.style.display = isAdminUser ? 'inline-flex' : 'none';
        } catch (error) {
            console.error('获取详情错误:', error);
            motherboardDetails.innerHTML = `
                <div class="text-center text-red-500">
                    <i class="fas fa-exclamation-circle mr-2"></i> 
                    加载详情失败: ${error.message}
                </div>
            `;
        }
    }

    // 渲染主板详情
    function renderMotherboardDetails(motherboard) {
        if (!motherboard) return;
        
        // 处理默认图片
        if (!motherboard.imageUrl) {
            motherboard.imageUrl = DEFAULT_MOTHERBOARD_IMAGE;
            motherboard.is_default_image = true;
        }

        motherboardDetails.innerHTML = `
            <!-- 主要信息区：图片和基本信息 -->
            <div class="flex flex-col md:flex-row gap-6 mb-6">
                <!-- 左侧主板图片 -->
                <div class="md:w-1/3">
                    <div class="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg shadow-md h-full flex items-center justify-center relative">
                        <img src="${motherboard.imageUrl}" alt="${motherboard.motherboardModel || '主板图片'}" 
                            class="w-full rounded-lg cursor-pointer hover:opacity-90"
                            style="max-height: 300px; object-fit: contain;"
                            onclick="openImageFullscreen('${motherboard.imageUrl}')">
                        ${motherboard.is_default_image ? 
                            `<div class="absolute bottom-2 right-2 bg-gray-700 text-white text-xs px-1.5 py-0.5 rounded-md opacity-80">默认图片</div>` : 
                            ''}
                    </div>
                </div>
                
                <!-- 右侧基本信息 -->
                <div class="md:w-2/3">
                    <!-- 标题和平台 -->
                    <div class="border-b dark:border-gray-600 pb-3 mb-4">
                        <h2 class="text-2xl font-bold text-gray-800 dark:text-gray-200 mb-2">${motherboard.motherboardModel || '未知型号'}</h2>
                        <div class="flex items-center justify-between">
                            <span class="inline-block px-3 py-1 bg-gray-100 dark:bg-gray-700 rounded-md text-gray-700 dark:text-gray-300 font-medium">${motherboard.brand || '-'}</span>
                            <span class="inline-block px-3 py-1 bg-blue-100 dark:bg-blue-900/30 rounded-md text-blue-700 dark:text-blue-300 font-medium">${motherboard.motherboardType || '-'} 平台</span>
                        </div>
                    </div>
                    
                    <!-- 核心规格卡片 -->
                    <div class="grid grid-cols-2 gap-4 mb-6">
                        <div class="bg-indigo-50 dark:bg-indigo-900/20 p-4 rounded-lg shadow-sm">
                            <h3 class="text-sm uppercase tracking-wider text-indigo-600 dark:text-indigo-400 mb-1">芯片组</h3>
                            <p class="text-2xl font-bold text-indigo-700 dark:text-indigo-300">${motherboard.chipset || '-'}</p>
                        </div>
                        <div class="bg-purple-50 dark:bg-purple-900/20 p-4 rounded-lg shadow-sm">
                            <h3 class="text-sm uppercase tracking-wider text-purple-600 dark:text-purple-400 mb-1">CPU接口</h3>
                            <p class="text-2xl font-bold text-purple-700 dark:text-purple-300">${motherboard.cpuSocket || '-'}</p>
                        </div>
                    </div>
                    
                    <!-- 供电与规格 -->
                    <div class="grid grid-cols-2 gap-4 mb-4">
                        <div class="bg-amber-50 dark:bg-amber-900/20 p-4 rounded-lg shadow-sm">
                            <h3 class="text-sm uppercase tracking-wider text-amber-600 dark:text-amber-400 mb-1">供电相数</h3>
                            <p class="text-xl font-bold text-amber-700 dark:text-amber-300">${motherboard.powerPhases || '-'}</p>
                        </div>
                        <div class="bg-emerald-50 dark:bg-emerald-900/20 p-4 rounded-lg shadow-sm">
                            <h3 class="text-sm uppercase tracking-wider text-emerald-600 dark:text-emerald-400 mb-1">主板规格</h3>
                            <p class="text-xl font-bold text-emerald-700 dark:text-emerald-300">${motherboard.formFactor || '-'}</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 详细规格部分 -->
            <div class="mb-6">
                <h3 class="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-4 pb-2 border-b dark:border-gray-700">详细规格</h3>
                
                <!-- 内存规格 -->
                <div class="mb-6">
                    <h4 class="text-md font-medium text-blue-600 dark:text-blue-400 mb-3">
                        <i class="fas fa-memory mr-2"></i>内存规格
                    </h4>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div class="bg-white dark:bg-gray-800 p-3 rounded-lg border border-gray-200 dark:border-gray-700">
                            <h5 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">内存类型</h5>
                            <p class="font-semibold text-gray-800 dark:text-gray-200">${motherboard.memoryGeneration || '-'}</p>
                        </div>
                        <div class="bg-white dark:bg-gray-800 p-3 rounded-lg border border-gray-200 dark:border-gray-700">
                            <h5 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">最大容量</h5>
                            <p class="font-semibold text-gray-800 dark:text-gray-200">${motherboard.memoryCapacity || '-'}</p>
                        </div>
                        <div class="bg-white dark:bg-gray-800 p-3 rounded-lg border border-gray-200 dark:border-gray-700">
                            <h5 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">内存频率</h5>
                            <p class="font-semibold text-gray-800 dark:text-gray-200">${motherboard.memoryFrequency || '-'}</p>
                        </div>
                    </div>
                </div>
                
                <!-- 扩展接口 -->
                <div class="mb-6">
                    <h4 class="text-md font-medium text-green-600 dark:text-green-400 mb-3">
                        <i class="fas fa-plug mr-2"></i>扩展接口
                    </h4>
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                        <div class="bg-white dark:bg-gray-800 p-3 rounded-lg border border-gray-200 dark:border-gray-700">
                            <h5 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">PCIe x16插槽</h5>
                            <p class="font-semibold text-gray-800 dark:text-gray-200">${motherboard.pcieSlots || '-'} 个</p>
                        </div>
                        <div class="bg-white dark:bg-gray-800 p-3 rounded-lg border border-gray-200 dark:border-gray-700">
                            <h5 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">M.2接口</h5>
                            <p class="font-semibold text-gray-800 dark:text-gray-200">${motherboard.m2Slots || '-'} 个</p>
                        </div>
                        <div class="bg-white dark:bg-gray-800 p-3 rounded-lg border border-gray-200 dark:border-gray-700">
                            <h5 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">SATA接口</h5>
                            <p class="font-semibold text-gray-800 dark:text-gray-200">${motherboard.sataSlots || '-'} 个</p>
                        </div>
                        <div class="bg-white dark:bg-gray-800 p-3 rounded-lg border border-gray-200 dark:border-gray-700">
                            <h5 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">USB 3.2接口</h5>
                            <p class="font-semibold text-gray-800 dark:text-gray-200">${motherboard.usbSlots || '-'} 个</p>
                        </div>
                    </div>
                </div>
                
                <!-- 电源与散热 -->
                <div class="mb-6">
                    <h4 class="text-md font-medium text-amber-600 dark:text-amber-400 mb-3">
                        <i class="fas fa-bolt mr-2"></i>电源与散热
                    </h4>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div class="bg-white dark:bg-gray-800 p-3 rounded-lg border border-gray-200 dark:border-gray-700">
                            <h5 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">电源接口</h5>
                            <p class="font-semibold text-gray-800 dark:text-gray-200">${motherboard.powerConnector || '-'}</p>
                        </div>
                        <div class="bg-white dark:bg-gray-800 p-3 rounded-lg border border-gray-200 dark:border-gray-700">
                            <h5 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">VRM散热</h5>
                            <p class="font-semibold text-gray-800 dark:text-gray-200">${motherboard.vrmCooling || '-'}</p>
                        </div>
                        <div class="bg-white dark:bg-gray-800 p-3 rounded-lg border border-gray-200 dark:border-gray-700">
                            <h5 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">最大TDP</h5>
                            <p class="font-semibold text-gray-800 dark:text-gray-200">${motherboard.maxTdp ? motherboard.maxTdp + ' W' : '-'}</p>
                        </div>
                    </div>
                </div>
                
                <!-- CPU支持 -->
                <div class="mb-6">
                    <h4 class="text-md font-medium text-red-600 dark:text-red-400 mb-3">
                        <i class="fas fa-microchip mr-2"></i>CPU支持
                    </h4>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div class="bg-white dark:bg-gray-800 p-3 rounded-lg border border-gray-200 dark:border-gray-700">
                            <h5 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">支持CPU</h5>
                            <p class="font-semibold text-gray-800 dark:text-gray-200">${motherboard.cpuModel || '-'}</p>
                        </div>
                        <div class="bg-white dark:bg-gray-800 p-3 rounded-lg border border-gray-200 dark:border-gray-700">
                            <h5 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">超频支持</h5>
                            <p class="font-semibold text-gray-800 dark:text-gray-200">${motherboard.overclockSupport || '-'}</p>
                        </div>
                    </div>
                </div>
                
                <!-- 其他特性 -->
                <div>
                    <h4 class="text-md font-medium text-purple-600 dark:text-purple-400 mb-3">
                        <i class="fas fa-cog mr-2"></i>其他特性
                    </h4>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div class="bg-white dark:bg-gray-800 p-3 rounded-lg border border-gray-200 dark:border-gray-700">
                            <h5 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">板载WiFi</h5>
                            <p class="font-semibold text-gray-800 dark:text-gray-200">${motherboard.wifiSupport || '-'}</p>
                        </div>
                        <div class="bg-white dark:bg-gray-800 p-3 rounded-lg border border-gray-200 dark:border-gray-700">
                            <h5 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">BIOS版本</h5>
                            <p class="font-semibold text-gray-800 dark:text-gray-200">${motherboard.biosVersion || '-'}</p>
                        </div>
                        <div class="bg-white dark:bg-gray-800 p-3 rounded-lg border border-gray-200 dark:border-gray-700">
                            <h5 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">发布日期</h5>
                            <p class="font-semibold text-gray-800 dark:text-gray-200">${motherboard.releaseDate || '-'}</p>
                        </div>
                        <div class="bg-white dark:bg-gray-800 p-3 rounded-lg border border-gray-200 dark:border-gray-700">
                            <h5 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">质保期限</h5>
                            <p class="font-semibold text-gray-800 dark:text-gray-200">${motherboard.warranty ? motherboard.warranty + ' 年' : '-'}</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 备注信息 -->
            ${motherboard.notes ? `
            <div class="mt-6 p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg border border-yellow-200 dark:border-yellow-800/30">
                <h4 class="font-medium text-yellow-800 dark:text-yellow-300 mb-2 flex items-center">
                    <i class="fas fa-sticky-note mr-2"></i>备注信息
                </h4>
                <p class="text-gray-700 dark:text-gray-300">${motherboard.notes}</p>
            </div>
            ` : ''}
        `;
    }

    // 编辑主板
    async function editMotherboard(id) {
        const isAdminUser = await isAdmin();
        if (!isAdminUser) {
            return showErrorMessage('权限不足，无法编辑。');
        }

        const motherboard = motherboards.find(mb => mb.id === id);
        if (!motherboard) {
            showErrorMessage('未找到该主板信息');
            return;
        }

        // 智能设置select元素值的辅助函数
        function setSelectValue(selectElement, value, fieldName) {
            if (!value) {
                selectElement.value = '';
                return;
            }

            // 尝试直接匹配
            selectElement.value = value;

            // 如果直接匹配失败，尝试智能匹配
            if (!selectElement.value) {
                const options = Array.from(selectElement.options);
                const matchedOption = options.find(option =>
                    option.value === value ||
                    option.textContent === value ||
                    option.value.toLowerCase() === value.toLowerCase() ||
                    option.textContent.toLowerCase() === value.toLowerCase()
                );

                if (matchedOption) {
                    selectElement.value = matchedOption.value;
                } else {
                    console.warn(`无法匹配${fieldName}选项:`, value);
                    selectElement.value = '';
                }
            }
        }

        // 设置表单值
        motherboardModel.value = motherboard.motherboardModel || '';
        setSelectValue(brand, motherboard.brand, '品牌');
        setSelectValue(motherboardType, motherboard.motherboardType, '主板类型');
        setSelectValue(chipset, motherboard.chipset, '芯片组');
        setSelectValue(formFactor, motherboard.formFactor, '主板尺寸');
        setSelectValue(memoryCapacity, motherboard.memoryCapacity, '内存容量');
        setSelectValue(memoryGeneration, motherboard.memoryGeneration, '内存代数');
        memoryFrequency.value = motherboard.memoryFrequency || '';
        biosVersion.value = motherboard.biosVersion || '';
        releaseDate.value = motherboard.releaseDate || '';
        setSelectValue(cpuSocket, motherboard.cpuSocket, 'CPU接口');
        cpuModel.value = motherboard.cpuModel || '';
        maxTdp.value = motherboard.maxTdp || '';
        setSelectValue(overclockSupport, motherboard.overclockSupport, '超频支持');
        pcieSlots.value = motherboard.pcieSlots || '';
        m2Slots.value = motherboard.m2Slots || '';
        sataSlots.value = motherboard.sataSlots || '';
        usbSlots.value = motherboard.usbSlots || '';
        setSelectValue(wifiSupport, motherboard.wifiSupport, 'WiFi支持');
        powerPhases.value = motherboard.powerPhases || '';
        vrmCooling.value = motherboard.vrmCooling || '';
        powerConnector.value = motherboard.powerConnector || '';
        warranty.value = motherboard.warranty || '';
        notes.value = motherboard.notes || '';

        // 显示图片预览
        if (motherboard.imageUrl) {
            imagePreview.src = motherboard.imageUrl;
            imagePreview.onclick = () => openImageFullscreen(motherboard.imageUrl);
            imagePreview.classList.add('cursor-pointer');
            imagePreviewContainer.classList.remove('hidden');
        } else {
            imagePreviewContainer.classList.add('hidden');
        }

        // 设置编辑状态
        isEditing = true;
        currentMotherboardId = id;

        // 更改提交按钮文本
        const submitBtn = motherboardForm.querySelector('button[type="submit"]');
        submitBtn.innerHTML = '<i class="fas fa-save mr-1 sm:mr-2"></i> 更新主板信息';

        // 滚动到表单顶部
        motherboardForm.scrollIntoView({ behavior: 'smooth' });

        // 关闭模态框
        motherboardModal.classList.add('hidden');
    }

    // 确认删除
    async function confirmDelete() {
        const isAdminUser = await isAdmin();
        if (!isAdminUser) {
            return showErrorMessage('权限不足，无法删除。');
        }

        if (confirm('确定要删除这条主板信息吗？删除后无法恢复。')) {
            deleteMotherboard();
        }
    }

    // 删除主板
    async function deleteMotherboard() {
        const isAdminUser = await isAdmin();
        if (!isAdminUser) {
            return showErrorMessage('权限不足，无法删除。');
        }
        
        // 显示加载状态
        const modalButtons = motherboardModal.querySelectorAll('button');
        modalButtons.forEach(btn => {
            btn.disabled = true;
            if (btn.id === 'deleteBtn') {
                btn.innerHTML = '<i class="fas fa-spinner fa-spin mr-1"></i> 删除中...';
            }
        });

        // 发送删除请求
        fetchWithAuth(API_ENDPOINTS.MOTHERBOARD(currentMotherboardId), {
            method: 'DELETE',
        })
            .then(response => {
                if (!response || !response.ok) {
                    return response.json().then(data => {
                        throw new Error(data.message || '删除失败');
                    });
                }

                // 删除成功
                showSuccessMessage('主板信息已删除');
                motherboardModal.classList.add('hidden');

                // 重新加载数据
                loadMotherboards();
            })
            .catch(error => {
                console.error('删除错误:', error);
                showErrorMessage(`删除失败: ${error.message}`);

                // 恢复按钮状态
                modalButtons.forEach(btn => {
                    btn.disabled = false;
                    if (btn.id === 'deleteBtn') {
                        btn.innerHTML = '<i class="fas fa-trash mr-1"></i> 删除';
                    }
                });
            });
    }

    // 执行编辑操作
    function handleEdit() {
        editMotherboard(currentMotherboardId);
    }

    // 显示成功消息
    function showSuccessMessage(message) {
        const alertDiv = document.createElement('div');
        alertDiv.className = 'fixed top-4 right-4 bg-green-100 dark:bg-green-900/50 border border-green-400 dark:border-green-700 text-green-700 dark:text-green-300 px-4 py-3 rounded z-50 fade-in';
        alertDiv.innerHTML = `
            <div class="flex items-center">
                <i class="fas fa-check-circle mr-2"></i>
                <span>${message}</span>
            </div>
        `;
        document.body.appendChild(alertDiv);

        // 3秒后自动消失
        setTimeout(() => {
            alertDiv.style.opacity = '0';
            alertDiv.style.transition = 'opacity 0.5s';
            setTimeout(() => alertDiv.remove(), 500);
        }, 3000);
    }

    // 显示错误消息
    function showErrorMessage(message) {
        const alertDiv = document.createElement('div');
        alertDiv.className = 'fixed top-4 right-4 bg-red-100 dark:bg-red-900/50 border border-red-400 dark:border-red-700 text-red-700 dark:text-red-300 px-4 py-3 rounded z-50 fade-in';
        alertDiv.innerHTML = `
            <div class="flex items-center">
                <i class="fas fa-exclamation-circle mr-2"></i>
                <span>${message}</span>
            </div>
        `;
        document.body.appendChild(alertDiv);
        
        // 3秒后自动消失
        setTimeout(() => {
            alertDiv.style.opacity = '0';
            alertDiv.style.transition = 'opacity 0.5s';
            setTimeout(() => alertDiv.remove(), 500);
        }, 3000);
    }

    // 防抖函数
    function debounce(func, delay) {
        let timeout;
        return function() {
            const context = this;
            const args = arguments;
            clearTimeout(timeout);
            timeout = setTimeout(() => func.apply(context, args), delay);
        };
    }

    // 跳转到首页
    function handleFirstPage() {
        if (currentPage !== 1) {
            currentPage = 1;
            loadMotherboards();
        }
    }

    // 跳转到末页
    function handleLastPage() {
        const totalPages = Math.ceil(parseInt(totalRecords.textContent) / pageSize);
        if (currentPage !== totalPages) {
            currentPage = totalPages;
            loadMotherboards();
        }
    }

    // 跳转到指定页
    function handleGoToPage() {
        if (pageJump && pageJump.value) {
            const pageNum = parseInt(pageJump.value);
            const totalPages = Math.ceil(parseInt(totalRecords.textContent) / pageSize);
            
            if (!isNaN(pageNum) && pageNum >= 1 && pageNum <= totalPages) {
                currentPage = pageNum;
                loadMotherboards();
            }
            
            // 清空输入
            pageJump.value = '';
        }
    }

    // Make the viewMotherboardDetails function globally available
    window.viewMotherboardDetails = viewMotherboardDetails;
    
    // 解析主板信息的主函数
    async function parseMotherboardInfo() {
        let input = document.getElementById('smartInput').value.trim();
        if (!input) return showErrorMessage('请输入主板参数');

        document.getElementById('autoFillBtn').disabled = true;
        document.getElementById('autoFillBtn').innerHTML = '<i class="fas fa-sync fa-spin mr-2"></i>正在解析...';

        try {
            console.log(`[Parse Start] Initial Input: "${input}"`);

            // 使用正则表达式解析
            console.log('[Parse] Using regex parsing');
            const regexResult = parseWithRegex(input);
            
            if (regexResult) {
                console.log('[Parse] Using regex parsing result:', regexResult);
                fillFormWithData(regexResult);
                document.getElementById('autoFillBtn').disabled = false;
                document.getElementById('autoFillBtn').innerHTML = '<i class="fas fa-magic mr-2"></i>智能识别';
                console.log('[Parse End] Parsing process finished.');
                return;
            }

            // 如果正则解析失败，回退到本地解析
            console.log('[Parse] Regex parsing failed, falling back to local parsing');

            // 本地解析逻辑
            const result = {
                motherboardModel: '',
                brand: '',
                motherboardType: '',
                chipset: '',
                formFactor: '',
                memoryCapacity: '',
                memoryGeneration: '',
                memoryFrequency: '',
                biosVersion: '',
                releaseDate: '',
                cpuSocket: '',
                cpuModel: '',
                maxTdp: '',
                overclockSupport: '',
                pcieSlots: '',
                m2Slots: '',
                sataSlots: '',
                usbSlots: '',
                wifiSupport: '',
                powerPhases: '',
                vrmCooling: '',
                powerConnector: '',
                warranty: '',
                notes: ''
            };

            const originalInputForNotes = input;
            let remainingInput = input;
            console.log(`[Parse Detail] Initial remainingInput: "${remainingInput}"`);

            function extractAndRemove(regex, targetField, processFunc) {
                console.log(`[Parse Detail] Attempting to extract '${targetField}' with regex: ${regex}`);
                const match = remainingInput.match(regex);
                if (match && match[1]) {
                    let value = match[1].trim();
                    console.log(`[Parse Detail] Regex Matched for '${targetField}'. Raw matched value: "${match[0]}", Extracted part: "${value}"`);
                    if (processFunc) {
                        const processedValue = processFunc(value, match);
                        console.log(`[Parse Detail] Processed value for '${targetField}': "${processedValue}" (Original: "${value}")`);
                        value = processedValue;
                    }
                    result[targetField] = value;
                    const oldRemainingInput = remainingInput;
                    remainingInput = remainingInput.replace(match[0], '').trim();
                    console.log(`[Parse Detail] Successfully extracted '${targetField}': "${value}". Remaining input: "${remainingInput}" (was: "${oldRemainingInput}")`);
                    return true;
                } else {
                    console.log(`[Parse Detail] No match for '${targetField}' with regex: ${regex} on input: "${remainingInput}"`);
                }
                return false;
            }

            const labeledFields = {
                '主板型号': { field: 'motherboardModel' },
                '型号': { field: 'motherboardModel' },
                '品牌': { field: 'brand' },
                '主板类型': { field: 'motherboardType' },
                '芯片组(型号)?': { field: 'chipset' },
                '主板尺寸': { field: 'formFactor' },
                '内存容量': { field: 'memoryCapacity' },
                '内存代数': { field: 'memoryGeneration' },
                '内存频率': { field: 'memoryFrequency' },
                '推荐CPU': { field: 'biosVersion' },
                '发布日期': { field: 'releaseDate' },
                '支持的CPU接口类型|CPU接口|插槽': { field: 'cpuSocket' },
                '支持的CPU型号': { field: 'cpuModel' },
                '支持的最大CPU功耗': { 
                    field: 'maxTdp', 
                    process: (val) => {
                        const numMatch = val.match(/(\d+)/);
                        if (numMatch) return numMatch[1];
                        return val;
                    } 
                },
                '超频支持': { field: 'overclockSupport' },
                'PCIe x16插槽数量': { 
                    field: 'pcieSlots', 
                    process: (val) => {
                        const numMatch = val.match(/(\d+)/);
                        if (numMatch) return numMatch[1];
                        return val;
                    } 
                },
                'M.2接口数量': { 
                    field: 'm2Slots', 
                    process: (val) => {
                        const numMatch = val.match(/(\d+)/);
                        if (numMatch) return numMatch[1];
                        return val;
                    } 
                },
                'SATA接口数量': { 
                    field: 'sataSlots', 
                    process: (val) => {
                        const numMatch = val.match(/(\d+)/);
                        if (numMatch) return numMatch[1];
                        return val;
                    } 
                },
                'USB 3.2 Gen接口数量': { 
                    field: 'usbSlots', 
                    process: (val) => {
                        const numMatch = val.match(/(\d+)/);
                        if (numMatch) return numMatch[1];
                        return val;
                    } 
                },
                '板载WiFi支持': { field: 'wifiSupport' },
                '供电相数': { field: 'powerPhases' },
                'VRM散热设计': { field: 'vrmCooling' },
                '额外供电接口': { field: 'powerConnector' },
                '保修期': { field: 'warranty' },
                '备注': { field: 'notes' }
            };

            console.log('[Parse Phase] Starting Labeled Field Extraction.');
            for (const labelKey in labeledFields) {
                const { field, process } = labeledFields[labelKey];
                const escapedLabelKey = labelKey.replace(/[.*+?^${}()|[\]\\]/g, '\\\\$&');
                const regex = new RegExp(`${escapedLabelKey}[：:]\\s*([^、，,。]+)`, 'i');
                extractAndRemove(regex, field, process);
            }

            // 提取品牌
            if (!result.brand) {
                const brandList = [
                    '华硕', 'ASUS', '微星', 'MSI', '技嘉', 'GIGABYTE', '华擎', 'ASRock', 
                    '七彩虹', 'Colorful', '铭瑄', 'MAXSUN', '映泰', 'BIOSTAR'
                ];
                const brandRegex = new RegExp(`(${brandList.join('|')})`, 'i');
                extractAndRemove(brandRegex, 'brand');
            }

            // 提取主板类型
            if (!result.motherboardType) {
                if (remainingInput.match(/Intel|英特尔/i)) {
                    result.motherboardType = 'Intel';
                } else if (remainingInput.match(/AMD|锐龙|Ryzen/i)) {
                    result.motherboardType = 'AMD';
                }
            }

            // 提取芯片组型号
            if (!result.chipset) {
                const intelChipsets = ['Z890', 'Z790', 'Z690', 'Z590', 'Z490', 'Z390', 'Z370', 'Z270', 'Z170', 
                                      'B860', 'B760', 'B660', 'B560', 'B460', 'B360', 
                                      'H770', 'H670', 'H610', 'H510', 'H410'];
                const amdChipsets = ['X890', 'X670E', 'X670', 'X570', 'X470', 'X370', 
                                    'B650E', 'B650', 'B550', 'B450', 'B350', 
                                    'A620', 'A520', 'A320', 'X300', 'A300'];
                
                const allChipsets = [...intelChipsets, ...amdChipsets];
                const chipsetRegex = new RegExp(`(${allChipsets.join('|')})`, 'i');
                extractAndRemove(chipsetRegex, 'chipset');
            }

            // 提取主板尺寸
            if (!result.formFactor) {
                const formFactors = ['ATX', 'Micro-ATX', 'Mini-ITX', 'E-ATX'];
                const formFactorRegex = new RegExp(`(${formFactors.join('|')})`, 'i');
                extractAndRemove(formFactorRegex, 'formFactor');
            }

            // 提取内存代数
            if (!result.memoryGeneration) {
                const memoryGens = ['DDR5', 'DDR4', 'DDR3', 'DDR2'];
                const memoryGenRegex = new RegExp(`(${memoryGens.join('|')})`, 'i');
                extractAndRemove(memoryGenRegex, 'memoryGeneration');
            }

            // 提取CPU插槽
            if (!result.cpuSocket) {
                const intelSockets = ['LGA 1851', 'LGA 1700', 'LGA 1200', 'LGA 1151', 'LGA 1155', 'LGA 2066', 'LGA 2011', 'LGA 1366'];
                const amdSockets = ['AM5', 'AM4', 'sTRX4', 'TR4', 'SP5', 'SP6'];
                
                const allSockets = [...intelSockets, ...amdSockets];
                const socketRegex = new RegExp(`(${allSockets.join('|')})`, 'i');
                extractAndRemove(socketRegex, 'cpuSocket');
            }

            // 最后使用fillFormWithData函数更新UI
            fillFormWithData(result);

            document.getElementById('autoFillBtn').disabled = false;
            document.getElementById('autoFillBtn').innerHTML = '<i class="fas fa-magic mr-2"></i>智能识别';
            console.log('[Parse End] Parsing process finished.');
        } catch (error) {
            console.error('[Parse Error] Error during parsing:', error);
            showErrorMessage('解析参数时发生错误: ' + error.message);
            document.getElementById('autoFillBtn').disabled = false;
            document.getElementById('autoFillBtn').innerHTML = '<i class="fas fa-magic mr-2"></i>智能识别';
            console.log('[Parse End] Parsing process finished.');
        }
    }

    // 使用正则表达式解析主板参数
    function parseWithRegex(inputText) {
        try {
            console.log('[Regex Parse] Starting regex parsing with input:', inputText);

            // 预处理输入：替换特定替换字符
            let processedInput = inputText
                .replace(/：/g, ':') // 统一冒号
                .replace(/，/g, '、') // 统一分隔符
                .replace(/,/g, '、');

            // 初始化结果对象
            const result = {
                motherboardModel: '',
                brand: '',
                motherboardType: '',
                chipset: '',
                formFactor: '',
                memoryCapacity: '',
                memoryGeneration: '',
                memoryFrequency: '',
                biosVersion: '',
                releaseDate: '',
                cpuSocket: '',
                cpuModel: '',
                maxTdp: '',
                overclockSupport: '',
                pcieSlots: '',
                m2Slots: '',
                sataSlots: '',
                usbSlots: '',
                wifiSupport: '',
                powerPhases: '',
                vrmCooling: '',
                powerConnector: '',
                warranty: '',
                notes: ''
            };

            // 分割键值对
            const pairs = processedInput.split(/、(?=[^:]+:)/);
            console.log('[Regex Parse] Split pairs:', pairs);

            // 字段映射表
            const fieldMappings = [
                { regex: /^主板型号$|^型号$/i, field: 'motherboardModel' },
                { regex: /^品牌$/i, field: 'brand' },
                { regex: /^主板类型$/i, field: 'motherboardType' },
                { regex: /^芯片组(型号)?$/i, field: 'chipset' },
                { regex: /^主板尺寸$/i, field: 'formFactor' },
                { regex: /^内存容量$/i, field: 'memoryCapacity' },
                { regex: /^内存代数$/i, field: 'memoryGeneration' },
                { regex: /^内存频率$/i, field: 'memoryFrequency' },
                { regex: /^推荐CPU$/i, field: 'biosVersion' },
                { regex: /^发布日期$/i, field: 'releaseDate' },
                { regex: /^支持的CPU接口类型$|^CPU接口$|^插槽$/i, field: 'cpuSocket' },
                { regex: /^支持的CPU型号$/i, field: 'cpuModel' },
                { regex: /^支持的最大CPU功耗$/i, field: 'maxTdp' },
                { regex: /^超频支持$/i, field: 'overclockSupport' },
                { regex: /^PCIe x16插槽数量$/i, field: 'pcieSlots' },
                { regex: /^M.2接口数量$/i, field: 'm2Slots' },
                { regex: /^SATA接口数量$/i, field: 'sataSlots' },
                { regex: /^USB 3.2 Gen接口数量$/i, field: 'usbSlots' },
                { regex: /^板载WiFi支持$/i, field: 'wifiSupport' },
                { regex: /^供电相数$/i, field: 'powerPhases' },
                { regex: /^VRM散热设计$/i, field: 'vrmCooling' },
                { regex: /^额外供电接口$/i, field: 'powerConnector' },
                { regex: /^保修期$/i, field: 'warranty' },
                { regex: /^备注$/i, field: 'notes' }
            ];

            // 解析每个字段
            pairs.forEach(pair => {
                // 对于没有冒号的部分（可能是型号或备注）
                if (!pair.includes(':')) {
                    // 如果很短，可能是型号
                    if (pair.trim().length < 30 && !result.motherboardModel) {
                        result.motherboardModel = pair.trim();
                        console.log(`[Regex Parse] Found standalone model: "${result.motherboardModel}"`);
                    }
                    // 否则可能是一个备注
                    else if (!result.notes) {
                        result.notes = pair.trim();
                        console.log(`[Regex Parse] Found standalone notes: "${result.notes}"`);
                    }
                    return;
                }

                // 处理正常的键值对
                const match = pair.match(/([^:]+):\s*(.*)/);
                if (match) {
                    const key = match[1].trim();
                    let value = match[2].trim();

                    // 使用正则表达式匹配字段
                    let field = null;
                    for (const mapping of fieldMappings) {
                        if (mapping.regex.test(key)) {
                            field = mapping.field;
                            console.log(`[Regex Parse] Field "${key}" matched to "${field}" via regex`);
                            break;
                        }
                    }

                    if (field) {
                        // 特殊字段处理
                        if (field === 'maxTdp' || field === 'pcieSlots' || field === 'm2Slots' || field === 'sataSlots' || field === 'usbSlots') {
                            // 提取数字部分
                            const numMatch = value.match(/(\d+)/);
                            if (numMatch) {
                                result[field] = numMatch[1];
                            } else {
                                result[field] = value;
                            }
                        }
                        // 其他字段直接赋值
                        else {
                            result[field] = value;
                        }
                        console.log(`[Regex Parse] Mapped "${key}" to field "${field}" with value: "${result[field]}"`);
                    } else {
                        console.log(`[Regex Parse] Unknown field: "${key}" with value: "${value}"`);
                        // 如果是未知字段且备注为空，添加到备注中
                        if (!result.notes) {
                            result.notes = `${key}: ${value}`;
                        } else {
                            result.notes += `; ${key}: ${value}`;
                        }
                    }
                }
            });

            // 特殊情况：如果没有型号但有完整文本的第一行，可能是型号
            if (!result.motherboardModel && inputText) {
                const firstLine = inputText.split(/[\r\n,，、]/)[0].trim();
                if (firstLine && firstLine.length < 30) {
                    result.motherboardModel = firstLine;
                    console.log(`[Regex Parse] Using first line as model: "${result.motherboardModel}"`);
                }
            }

            // 验证至少有一些基本信息被提取
            let filledFields = 0;
            for (const key in result) {
                if (result[key] && result[key].trim() !== '') {
                    filledFields++;
                }
            }

            if (filledFields < 2) {
                console.log('[Regex Parse] Not enough fields were parsed, result may be incomplete');
                return null;
            }

            console.log('[Regex Parse] Final result:', result);
            return result;
        } catch (error) {
            console.error('[Regex Parse] Error during regex parsing:', error);
            return null; // 解析失败返回null
        }
    }

    // 将解析结果填充到表单中
    function fillFormWithData(data) {
        console.log('[UI Update] Updating UI with recognized data:', data);
        
        // 遍历识别出的数据，更新表单字段
        Object.entries(data).forEach(([field, value]) => {
            if (value && value !== '暂无数据') {
                const element = document.getElementById(field);
                if (element) {
                    // 对于select元素，需要特殊处理
                    if (element.tagName === 'SELECT') {
                        // 尝试找到匹配的选项
                        const options = Array.from(element.options);
                        const option = options.find(opt => 
                            opt.value.toLowerCase() === value.toLowerCase() || 
                            opt.textContent.toLowerCase().includes(value.toLowerCase())
                        );
                        
                        if (option) {
                            element.value = option.value;
                        } else {
                            console.log(`[UI Update] No matching option found for ${field}: ${value}`);
                        }
                    } else {
                        element.value = value;
                    }
                    console.log(`[UI Update] Set field ${field} to ${value}`);
                } else {
                    console.log(`[UI Update] Element not found for field: ${field}`);
                }
            }
        });
        
        showSuccessMessage('参数识别完成！');
    }
}); 

// 图片全屏查看函数，作为全局函数
function openImageFullscreen(src) {
    console.log('[DEBUG] openImageFullscreen called with src:', src);
    
    if (!src) {
        console.error('[ERROR] Invalid image source provided to openImageFullscreen');
        return;
    }
    
    try {
    // 创建一个临时的图片容器
    const container = document.createElement('div');
    container.className = 'viewer-container';
    container.style.display = 'none';
    document.body.appendChild(container);
        console.log('[DEBUG] Created viewer container');

    // 创建图片元素
    const img = document.createElement('img');
    img.src = src;
    container.appendChild(img);
        console.log('[DEBUG] Added image to container');

        // 检查 Viewer 是否可用
        if (typeof Viewer === 'undefined') {
            console.error('[ERROR] Viewer.js is not loaded!');
            alert('图片查看器未加载，请刷新页面重试');
            document.body.removeChild(container);
            return;
        }

    // 初始化 Viewer
    const viewer = new Viewer(img, {
        backdrop: true,          // 启用背景遮罩
        button: true,           // 显示关闭按钮
        navbar: false,          // 隐藏底部导航栏（只有一张图片时不需要）
        title: false,           // 不显示标题
        toolbar: {              // 自定义工具栏
            zoomIn: true,       // 放大按钮
            zoomOut: true,      // 缩小按钮
            oneToOne: true,     // 1:1 尺寸按钮
            reset: true,        // 重置按钮
            prev: false,        // 上一张（隐藏，因为只有一张图片）
            play: false,        // 播放按钮（隐藏）
            next: false,        // 下一张（隐藏）
            rotateLeft: true,   // 向左旋转
            rotateRight: true,  // 向右旋转
            flipHorizontal: true, // 水平翻转
            flipVertical: true,  // 垂直翻转
        },
        viewed() {
            // 图片加载完成后自动打开查看器
                console.log('[DEBUG] Image viewed, zooming to 1');
                if (isMobile) {
                    viewer.zoomTo(0.8);  // 移动设备使用较小的初始缩放比例
                } else {
                    viewer.zoomTo(1);    // 桌面设备使用正常的缩放比例
                }
        },
        hidden() {
            // 查看器关闭后移除临时元素
                console.log('[DEBUG] Viewer hidden, destroying viewer');
            viewer.destroy();
            document.body.removeChild(container);
        },
        maxZoomRatio: 5,        // 最大缩放比例
        minZoomRatio: 0.1,      // 最小缩放比例
        transition: true,       // 启用过渡效果
        keyboard: true,         // 启用键盘支持
    });

        console.log('[DEBUG] Viewer created, showing');
    // 显示查看器
    viewer.show();
    } catch (error) {
        console.error('[ERROR] Error in openImageFullscreen:', error);
        alert('预览图片时发生错误，请查看控制台获取详情');
    }
} 










// 初始化权限控制
function initPermissions() {
    console.log('初始化权限控制...');
    
    // 如果没有权限控制模块，发出警告
    if (typeof setupPermissionBasedUI !== 'function') {
        console.warn('警告：权限控制模块未加载！请确保已引入permission-helper.js');
        return;
    }
    
    // 执行权限控制初始化
    setupPermissionBasedUI().then(() => {
        // 检查编辑按钮
        const editButtons = document.querySelectorAll('.btn-edit, [data-action="edit"]');
        const deleteButtons = document.querySelectorAll('.btn-delete, [data-action="delete"]');
        
        // 根据用户角色处理按钮
        isAdmin().then(isAdminUser => {
            if (!isAdminUser) {
                // 禁用表单
                const form = document.querySelector('form');
                if (form) {
                    const inputs = form.querySelectorAll('input, select, textarea');
                    inputs.forEach(input => {
                        input.setAttribute('disabled', 'disabled');
                    });
                    
                    const submitBtn = form.querySelector('button[type="submit"]');
                    if (submitBtn) {
                        submitBtn.setAttribute('disabled', 'disabled');
                        submitBtn.title = '只有管理员可以添加或修改数据';
                    }
                }
                
                // 隐藏或禁用编辑、删除按钮
                editButtons.forEach(btn => {
                    btn.style.display = 'none';
                });
                
                deleteButtons.forEach(btn => {
                    btn.style.display = 'none';
                });
                
                // 为所有删除操作添加权限验证拦截器
                interceptDeleteOperations();
            } else {
                // 添加管理员标识
                const header = document.querySelector('h1, h2');
                if (header) {
                    const adminBadge = document.createElement('span');
                    adminBadge.className = 'bg-green-500 text-white text-xs px-2 py-1 rounded ml-2';
                    adminBadge.innerText = '管理员';
                    header.appendChild(adminBadge);
                }
            }
        });
    });
}

// 拦截所有删除操作的请求
function interceptDeleteOperations() {
    // 保存原始的fetch函数
    const originalFetch = window.fetch;
    
    // 重写fetch函数以拦截删除请求
    window.fetch = async function(url, options) {
        // 检查是否为删除操作
        if (options && options.method === 'DELETE') {
            console.log('拦截到删除操作请求:', url);
            
            // 检查用户权限
            const isAdminUser = await isAdmin();
            if (!isAdminUser) {
                console.warn('权限不足，普通用户无法执行删除操作');
                // 返回权限错误响应
                return Promise.resolve(new Response(JSON.stringify({
                    error: '权限不足',
                    message: '只有管理员可以删除数据'
                }), {
                    status: 403,
                    headers: {
                        'Content-Type': 'application/json'
                    }
                }));
            }
        }
        
        // 对于非删除请求或管理员的删除请求，正常执行
        return originalFetch.apply(this, arguments);
    };
    
    // 添加全局点击事件拦截
    document.addEventListener('click', async function(event) {
        // 查找可能触发删除操作的元素
        const deleteButton = event.target.closest('.btn-delete, [data-action="delete"]');
        if (deleteButton) {
            const isAdminUser = await isAdmin();
            if (!isAdminUser) {
                // 阻止事件传播
                event.preventDefault();
                event.stopPropagation();
                console.warn('权限不足，普通用户无法执行删除操作');
                
                // 可选：显示提示消息（使用toast或alert）
                if (typeof showToast === 'function') {
                    showToast('只有管理员可以删除数据', 'error');
                } else {
                    console.error('权限不足，只有管理员可以删除数据');
                }
                
                return false;
            }
        }
    }, true);
}

// 进度条相关函数
function showUploadProgress() {
    const progressContainer = document.getElementById('uploadProgressContainer');
    const progressBar = document.getElementById('uploadProgressBar');
    const progressText = document.getElementById('uploadProgressText');
    const progressPercent = document.getElementById('uploadProgressPercent');

    if (progressContainer) {
        progressContainer.classList.remove('hidden');
        progressBar.style.width = '0%';
        if (progressText) {
            progressText.innerHTML = '<i id="uploadProgressIcon" class="fas fa-clock mr-1"></i>准备上传...';
        }
        if (progressPercent) {
            progressPercent.textContent = '0%';
        }
    }
}

function hideUploadProgress() {
    const progressContainer = document.getElementById('uploadProgressContainer');
    if (progressContainer) {
        setTimeout(() => {
            progressContainer.classList.add('hidden');
        }, 1000); // 延迟1秒隐藏，让用户看到完成状态
    }
}

function updateUploadProgress(percent, text, icon = 'fa-upload') {
    const progressBar = document.getElementById('uploadProgressBar');
    const progressText = document.getElementById('uploadProgressText');
    const progressPercent = document.getElementById('uploadProgressPercent');

    if (progressBar) {
        progressBar.style.width = percent + '%';
    }
    if (progressText && text) {
        const iconElement = `<i id="uploadProgressIcon" class="fas ${icon} mr-1"></i>`;
        progressText.innerHTML = iconElement + text;
    }
    if (progressPercent) {
        progressPercent.textContent = Math.round(percent) + '%';
    }
}

function disableSubmitButton(disabled) {
    const submitBtn = document.querySelector('button[type="submit"]');
    if (submitBtn) {
        submitBtn.disabled = disabled;
        if (disabled) {
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i> 正在处理...';
            submitBtn.classList.add('opacity-50', 'cursor-not-allowed');
        } else {
            const isEditingMode = submitBtn.innerHTML.includes('更新');
            if (isEditingMode) {
                submitBtn.innerHTML = '<i class="fas fa-save mr-1 sm:mr-2"></i> 更新主板信息';
            } else {
                submitBtn.innerHTML = '<i class="fas fa-save mr-1 sm:mr-2"></i> 保存主板信息';
            }
            submitBtn.classList.remove('opacity-50', 'cursor-not-allowed');
        }
    }
}

// 带进度的上传函数
function uploadWithProgress(url, method, formData) {
    return new Promise((resolve, reject) => {
        const xhr = new XMLHttpRequest();

        // 设置超时时间（30秒）
        xhr.timeout = 30000;

        // 上传进度监听
        xhr.upload.addEventListener('progress', (e) => {
            if (e.lengthComputable) {
                const percentComplete = (e.loaded / e.total) * 70; // 上传占70%
                const uploadedMB = (e.loaded / 1024 / 1024).toFixed(1);
                const totalMB = (e.total / 1024 / 1024).toFixed(1);
                updateUploadProgress(percentComplete, `正在上传图片... (${uploadedMB}MB / ${totalMB}MB)`, 'fa-upload');
            }
        });

        // 请求状态变化监听
        xhr.addEventListener('readystatechange', () => {
            if (xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {
                updateUploadProgress(75, '上传完成，正在处理数据...', 'fa-check-circle');
            } else if (xhr.readyState === XMLHttpRequest.LOADING) {
                updateUploadProgress(85, '正在转换为WebP格式...', 'fa-sync fa-spin');
            }
        });

        // 请求完成监听
        xhr.addEventListener('load', () => {
            updateUploadProgress(100, '处理完成！', 'fa-check-circle');

            if (xhr.status >= 200 && xhr.status < 300) {
                try {
                    const response = JSON.parse(xhr.responseText);
                    resolve(response);
                } catch (e) {
                    reject(new Error('响应解析失败'));
                }
            } else {
                try {
                    const errorResponse = JSON.parse(xhr.responseText);
                    reject(new Error(errorResponse.message || '操作失败'));
                } catch (e) {
                    reject(new Error(`HTTP ${xhr.status}: ${xhr.statusText}`));
                }
            }
        });

        // 错误监听
        xhr.addEventListener('error', () => {
            reject(new Error('网络错误'));
        });

        // 超时监听
        xhr.addEventListener('timeout', () => {
            reject(new Error('请求超时'));
        });

        // 打开请求
        xhr.open(method, url);

        // 设置请求头（必须在open之后）
        const token = localStorage.getItem('token');
        if (token) {
            xhr.setRequestHeader('Authorization', `Bearer ${token}`);
        }

        // 发送请求
        xhr.send(formData);
    });
}

// 页面加载完成后执行权限初始化
document.addEventListener('DOMContentLoaded', initPermissions);
