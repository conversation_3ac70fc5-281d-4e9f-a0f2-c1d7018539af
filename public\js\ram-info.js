// 定义一个变量追踪当前是否有活跃的预览
let activeViewer = null;

// 定义openImageFullscreen函数在全局作用域
function openImageFullscreen(src) {
    if (!src) return;
    console.log('[DEBUG] Opening image in fullscreen:', src);
    
    // 如果已经有活跃的预览，先关闭它
    if (activeViewer) {
        try {
            activeViewer.destroy();
            activeViewer = null;
            // 找到并移除可能存在的容器
            const oldContainer = document.querySelector('.viewer-container');
            if (oldContainer && oldContainer.parentNode) {
                oldContainer.parentNode.removeChild(oldContainer);
            }
        } catch (e) {
            console.error('Error destroying existing viewer:', e);
        }
    }
    
    // 创建一个临时的图片容器
    const container = document.createElement('div');
    container.className = 'viewer-container';
    container.style.display = 'none';
    document.body.appendChild(container);

    // 创建图片元素
    const img = document.createElement('img');
    img.src = src;
    container.appendChild(img);

    // 初始化 Viewer
    const viewer = new Viewer(img, {
        backdrop: true,          // 启用背景遮罩
        button: true,           // 显示关闭按钮
        navbar: false,          // 隐藏底部导航栏（只有一张图片时不需要）
        title: false,           // 不显示标题
        toolbar: {              // 自定义工具栏
            zoomIn: true,       // 放大按钮
            zoomOut: true,      // 缩小按钮
            oneToOne: true,     // 1:1 尺寸按钮
            reset: true,        // 重置按钮
            prev: false,        // 上一张（隐藏，因为只有一张图片）
            play: false,        // 播放按钮（隐藏）
            next: false,        // 下一张（隐藏）
            rotateLeft: true,   // 向左旋转
            rotateRight: true,  // 向右旋转
            flipHorizontal: true, // 水平翻转
            flipVertical: true,  // 垂直翻转
        },
        viewed() {
            // 图片加载完成后自动打开查看器
            if (isMobile) {
                viewer.zoomTo(0.8);  // 移动设备使用较小的初始缩放比例
            } else {
                viewer.zoomTo(1);    // 桌面设备使用正常的缩放比例
            }
        },
        hidden() {
            // 查看器关闭后移除临时元素
            viewer.destroy();
            activeViewer = null;
            if (container && container.parentNode) {
                container.parentNode.removeChild(container);
            }
        },
        maxZoomRatio: 5,        // 最大缩放比例
        minZoomRatio: 0.1,      // 最小缩放比例
        transition: true,       // 启用过渡效果
        keyboard: true,         // 启用键盘支持
    });

    // 保存活跃的预览引用
    activeViewer = viewer;

    // 显示查看器
    viewer.show();
}

// 立即将函数暴露到全局作用域
window.openImageFullscreen = openImageFullscreen;

document.addEventListener('DOMContentLoaded', function () {
    // DOM元素
    const ramForm = document.getElementById('ramForm');
    const brand = document.getElementById('brand');
    const model = document.getElementById('model');
    const capacity = document.getElementById('capacity');
    const memoryType = document.getElementById('memoryType');
    const speed = document.getElementById('speed');
    const modules = document.getElementById('modules');
    const casLatency = document.getElementById('casLatency');
    const voltage = document.getElementById('voltage');
    const heatSpreader = document.getElementById('heatSpreader');
    const rgbLighting = document.getElementById('rgbLighting');
    const color = document.getElementById('color');
    const price = document.getElementById('price');
    const ramImage = document.getElementById('ramImage');
    const imagePreview = document.getElementById('imagePreview');
    const imagePreviewContainer = document.getElementById('imagePreviewContainer');
    const removeImageBtn = document.getElementById('removeImageBtn');
    const notes = document.getElementById('notes');
    const ramTableBody = document.getElementById('ramTableBody');
    const ramSearch = document.getElementById('searchInput');
    const brandFilter = document.getElementById('brandFilter');
    const capacityFilter = document.getElementById('capacityFilter');
    const totalCount = document.getElementById('totalCount');
    const prevPage = document.getElementById('prevPageBtn');
    const nextPage = document.getElementById('nextPageBtn');
    const pageInfo = document.getElementById('pageInfo');
    const ramModal = document.getElementById('ramModal');
    const closeModal = document.getElementById('closeModal');
    const closeModalBtn = document.getElementById('closeModalBtn');
    const ramDetails = document.getElementById('ramDetails');
    const editBtn = document.getElementById('editBtn');
    const deleteBtn = document.getElementById('deleteBtn');
    const colorValue = document.getElementById('color').value.trim();

    // 全局变量
    let currentPage = 1;
    const pageSize = 10;
    let totalRecords = 0;
    let rams = [];
    let imageFile = null;
    let currentRamId = null;
    let isEditing = false;
    let isDarkMode = false;

    // 在全局变量下添加API端点常量
    const API_ENDPOINTS = {
        RAMS: '/api/rams',
        RAM: (id) => `/api/rams/${id}`
    };

    // 初始化
    init();

    // 初始化函数
    function init() {
        setupEventListeners();
        loadRams();
        initDarkMode();
        initPermissions(); // 添加权限初始化
    }

    // 初始化暗夜模式
    function initDarkMode() {
        // 检查本地存储中的暗夜模式设置
        isDarkMode = localStorage.getItem('darkMode') === 'true'; 
        if (isDarkMode) {
            document.body.classList.add('dark-mode');
            updateDarkModeIcon(true);
        } else {
            document.body.classList.remove('dark-mode');
            updateDarkModeIcon(false);
        }
    }

    // 更新暗夜模式图标
    function updateDarkModeIcon(isDark) {
        const darkModeToggle = document.getElementById('darkModeToggle');
        if (darkModeToggle) {
            if (isDark) {
                darkModeToggle.innerHTML = '<i class="fas fa-sun"></i>';
                darkModeToggle.classList.remove('bg-gray-200', 'hover:bg-gray-300', 'text-gray-700');
                darkModeToggle.classList.add('bg-yellow-400', 'hover:bg-yellow-500', 'text-yellow-900');
                darkModeToggle.title = '切换至亮色模式';
                darkModeToggle.style.boxShadow = '0 0 10px rgba(251, 191, 36, 0.5)';
            } else {
                darkModeToggle.innerHTML = '<i class="fas fa-moon"></i>';
                darkModeToggle.classList.remove('bg-yellow-400', 'hover:bg-yellow-500', 'text-yellow-900');
                darkModeToggle.classList.add('bg-gray-200', 'hover:bg-gray-300', 'text-gray-700');
                darkModeToggle.title = '切换至暗夜模式';
                darkModeToggle.style.boxShadow = 'none';
            }
        }
    }

    // 切换暗夜模式
    function toggleDarkMode() {
        isDarkMode = !isDarkMode;
        localStorage.setItem('darkMode', isDarkMode);
        
        // 直接切换类，不使用额外的嵌套函数
        document.body.classList.toggle('dark-mode');
        updateDarkModeIcon(isDarkMode);
        
        // 如果在移动端，立即更新卡片
        if (window.innerWidth < 640) {
            loadRams();
        }
    }

    // 设置事件监听器
    function setupEventListeners() {
        // 暗夜模式切换
        const darkModeToggle = document.getElementById('darkModeToggle');
        if (darkModeToggle) {
            darkModeToggle.addEventListener('click', toggleDarkMode);
        }

        // 图片上传
        if (ramImage) {
            ramImage.addEventListener('change', handleImageUpload);
        }

        if (removeImageBtn) {
            removeImageBtn.addEventListener('click', removeImage);
        }

        // 表单提交
        if (ramForm) {
            ramForm.addEventListener('submit', handleFormSubmit);
        }

        // 智能识别按钮
        const autoFillBtn = document.getElementById('autoFillBtn');
        if (autoFillBtn) {
            autoFillBtn.addEventListener('click', parseRamInfo);
        }

        // 搜索和筛选
        if (ramSearch) {
            ramSearch.addEventListener('input', debounce(() => {
                currentPage = 1;
                loadRams();
            }, 300));
        }

        if (brandFilter) {
            brandFilter.addEventListener('change', () => {
                currentPage = 1;
                loadRams();
            });
        }
        
        if (capacityFilter) {
            capacityFilter.addEventListener('change', () => {
                currentPage = 1;
                loadRams();
            });
        }
        
        // 重置按钮
        const resetFilterBtn = document.getElementById('resetFilterBtn');
        if (resetFilterBtn) {
            resetFilterBtn.addEventListener('click', () => {
                if (ramSearch) ramSearch.value = '';
                if (brandFilter) brandFilter.value = 'all';
                if (capacityFilter) capacityFilter.value = 'all';
                currentPage = 1;
                loadRams();
            });
        }

        // 分页
        if (prevPage) {
            prevPage.addEventListener('click', () => changePage(currentPage - 1));
        }

        if (nextPage) {
            nextPage.addEventListener('click', () => changePage(currentPage + 1));
        }
        
        // 首页和尾页
        const firstPageBtn = document.getElementById('firstPage');
        if (firstPageBtn) {
            firstPageBtn.addEventListener('click', () => changePage(1));
        }

        const lastPageBtn = document.getElementById('lastPage');
        if (lastPageBtn) {
            lastPageBtn.addEventListener('click', () => {
                const totalPages = Math.ceil(totalRecords / pageSize) || 1;
                changePage(totalPages);
            });
        }

        // 页码跳转
        const goToPageBtn = document.getElementById('goToPage');
        if (goToPageBtn) {
            goToPageBtn.addEventListener('click', () => {
                const pageJumpInput = document.getElementById('pageJump');
                if (pageJumpInput) {
                    let pageNum = parseInt(pageJumpInput.value);
                    const totalPages = Math.ceil(totalRecords / pageSize) || 1;
                    
                    if (isNaN(pageNum) || pageNum < 1) {
                        pageNum = 1;
                    } else if (pageNum > totalPages) {
                        pageNum = totalPages;
                    }
                    
                    changePage(pageNum);
                    pageJumpInput.value = '';
                }
            });
        }

        // 监听页码输入框的回车事件
        const pageJumpInput = document.getElementById('pageJump');
        if (pageJumpInput) {
            pageJumpInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    const goToPageBtn = document.getElementById('goToPage');
                    if (goToPageBtn) {
                        goToPageBtn.click();
                    }
                }
            });
        }

        // 模态框
        if (closeModal) {
            closeModal.addEventListener('click', () => {
                if (ramModal) {
                    ramModal.classList.add('hidden');
                } else {
                    const detailModal = document.getElementById('ramDetailModal');
                    if (detailModal) {
                        detailModal.classList.add('hidden');
                    }
                }
            });
        }

        if (closeModalBtn) {
            closeModalBtn.addEventListener('click', () => {
                if (ramModal) {
                    ramModal.classList.add('hidden');
                } else {
                    const detailModal = document.getElementById('ramDetailModal');
                    if (detailModal) {
                        detailModal.classList.add('hidden');
                    }
                }
            });
        }

        // 详情模态框关闭按钮
        const closeDetailModalBtn = document.getElementById('closeDetailModalBtn');
        if (closeDetailModalBtn) {
            closeDetailModalBtn.addEventListener('click', () => {
                const detailModal = document.getElementById('ramDetailModal');
                if (detailModal) {
                    detailModal.classList.add('hidden');
                }
            });
        }

        if (editBtn) {
            editBtn.addEventListener('click', handleEdit);
        }

        if (deleteBtn) {
            deleteBtn.addEventListener('click', () => {
                if (typeof confirmDelete === 'function') {
                    confirmDelete();
                } else {
                    // 如果confirmDelete不是一个函数，尝试使用confirmDeleteRam
                    if (currentRamId && typeof confirmDeleteRam === 'function') {
                        confirmDeleteRam(currentRamId);
                    } else {
                        console.error('Error: confirmDelete function not found');
                    }
                }
            });
        }

        // 添加全局事件委托处理图片点击 - 添加一个标志防止重复触发
        let isOpeningImage = false; // 添加标志防止重复触发
        
        document.addEventListener('click', function(e) {
            // 检查点击的是否是带有preview-image类的图片，并且当前没有正在打开预览
            if (e.target && e.target.classList.contains('preview-image') && !isOpeningImage) {
                e.preventDefault();
                e.stopPropagation();
                isOpeningImage = true; // 设置标志
                
                const src = e.target.dataset.src || e.target.src;
                if (src) {
                    console.log('[DEBUG] Clicked on preview image, opening fullscreen with src:', src);
                    openImageFullscreen(src);
                    
                    // 设置定时器在预览打开后重置标志
                    setTimeout(() => {
                        isOpeningImage = false;
                    }, 500);
                } else {
                    isOpeningImage = false; // 如果没有src，立即重置标志
                }
            }
        });
    }

    // 处理图片上传
    function handleImageUpload(e) {
        const file = e.target.files[0];
        if (!file) return;

        // 验证文件类型
        const validTypes = ['image/jpeg', 'image/png', 'image/gif'];
        if (!validTypes.includes(file.type)) {
            alert('请上传JPG、PNG或GIF格式的图片');
            return;
        }

        // 验证文件大小
        if (file.size > 5 * 1024 * 1024) { // 5MB
            alert('图片大小不能超过5MB');
            return;
        }

        imageFile = file;
        const reader = new FileReader();
        reader.onload = function (event) {
            imagePreview.src = event.target.result;
            imagePreviewContainer.classList.remove('hidden');

            // 添加点击预览功能
            imagePreview.onclick = () => {
                openImageFullscreen(event.target.result);
            };
            imagePreview.style.cursor = 'pointer';
            imagePreview.title = '点击查看大图';
            
            // 添加WebP转换提示
            if (supportWebP) {
                const fileSize = (file.size / 1024).toFixed(1);
                const estimatedWebPSize = (file.size * 0.7 / 1024).toFixed(1); // 估计WebP大约为原图70%大小
                console.log(`[图片优化] 文件大小: ${fileSize}KB, 转换后预估: ${estimatedWebPSize}KB (WebP格式)`);
                
                // 显示转换提示
                const uploadHint = document.createElement('p');
                uploadHint.className = 'text-xs text-green-600 mt-1 upload-notification';
                uploadHint.innerHTML = `<i class="fas fa-info-circle mr-1"></i>图片将在上传时自动转换为WebP格式 (预计可减小约${Math.round((1 - 0.7) * 100)}%)`;
                
                // 如果已有提示则替换，否则添加
                const existingHint = imagePreviewContainer.querySelector('.upload-notification');
                if (existingHint) {
                    existingHint.replaceWith(uploadHint);
                } else {
                    imagePreviewContainer.appendChild(uploadHint);
                }
            }
        };
        reader.readAsDataURL(file);
    }

    // 移除图片
    function removeImage() {
        ramImage.value = '';
        imagePreview.src = '';
        imagePreviewContainer.classList.add('hidden');
        imageFile = null;
    }

    // 处理表单提交
    function handleFormSubmit(e) {
        e.preventDefault();

        // 表单验证
        if (!model.value.trim()) {
            showErrorMessage('请输入内存型号');
            model.focus();
            return;
        }

        if (!brand.value) {
            showErrorMessage('请选择内存品牌');
            brand.focus();
            return;
        }

        if (!capacity.value) {
            showErrorMessage('请输入内存容量');
            capacity.focus();
            return;
        }

        if (!memoryType.value) {
            showErrorMessage('请选择内存类型');
            memoryType.focus();
            return;
        }

        // 验证CAS延迟和电压范围以防止数据库错误
        if (casLatency.value && (casLatency.value < 0 || casLatency.value > 100)) {
            showErrorMessage('CAS延迟超出合理范围(0-100)');
            casLatency.focus();
            return;
        }

        if (voltage.value && (voltage.value < 0 || voltage.value > 5)) {
            showErrorMessage('电压超出合理范围(0-5V)');
            voltage.focus();
            return;
        }

        if (speed.value && (speed.value < 0 || speed.value > 10000)) {
            showErrorMessage('频率超出合理范围(0-10000MHz)');
            speed.focus();
            return;
        }

        // 创建FormData对象
        const formData = new FormData();
        formData.append('brand', brand.value);
        formData.append('model', model.value.trim());
        formData.append('capacity', capacity.value);
        formData.append('type', memoryType.value);
        formData.append('speed', speed.value);
        formData.append('kit_size', modules.value);
        formData.append('casLatency', casLatency.value);
        formData.append('voltage', voltage.value);
        formData.append('heatSpreader', heatSpreader.value);
        formData.append('rgb', rgbLighting.value);
        formData.append('color', color.value.trim());

        formData.append('price', price.value);
        formData.append('notes', notes.value.trim());

        if (imageFile) {
            formData.append('image', imageFile);
            // 添加调试信息
            console.log('[DEBUG] 正在上传内存图片:', imageFile.name, '类型:', imageFile.type, '大小:', (imageFile.size / 1024).toFixed(1) + 'KB', '(将自动转换为WebP格式以优化加载速度)');
            
            // 更新上传提示
            const uploadHint = document.querySelector('.upload-notification');
            if (uploadHint) {
                uploadHint.innerHTML = '<i class="fas fa-cloud-upload-alt mr-1"></i>图片正在上传并转换为WebP格式...';
            }
        }

        // 提交表单
        const url = isEditing ? `/api/rams/${currentRamId}` : '/api/rams';
        const method = isEditing ? 'PUT' : 'POST';

        // 显示进度条并禁用提交按钮
        showUploadProgress();
        disableSubmitButton(true, isEditing);

        // 使用XMLHttpRequest来支持上传进度
        uploadWithProgress(url, method, formData)
        .then(data => {
            showSuccessMessage(isEditing ? '内存信息更新成功' : '内存信息添加成功');
            resetForm();
            loadRams();
        })
        .catch(error => {
            console.error('Error:', error);
            showErrorMessage(error.message || '操作失败');
        })
        .finally(() => {
            // 隐藏进度条并恢复提交按钮
            hideUploadProgress();
            disableSubmitButton(false, isEditing);
        });
    }

    // 重置表单
    function resetForm() {
        ramForm.reset();
        removeImage();
        isEditing = false;
        currentRamId = null;
        // 更改提交按钮文本
        const submitBtn = ramForm.querySelector('button[type="submit"]');
        submitBtn.innerHTML = '<i class="fas fa-save mr-1 sm:mr-2"></i> 保存内存信息';
    }

    // 加载内存列表
    function loadRams() {
        // 添加对HTML元素的null检查
        const searchTerm = ramSearch ? ramSearch.value.trim() : '';
        const selectedBrand = brandFilter ? brandFilter.value : 'all';
        const selectedCapacity = capacityFilter ? capacityFilter.value : 'all';

        let url = `${API_ENDPOINTS.RAMS}?page=${currentPage}&limit=${pageSize}`;

        if (searchTerm) {
            url += `&search=${encodeURIComponent(searchTerm)}`;
        }

        if (selectedBrand && selectedBrand !== 'all') {
            url += `&brand=${encodeURIComponent(selectedBrand)}`;
        }
        
        if (selectedCapacity && selectedCapacity !== 'all') {
            url += `&capacity=${encodeURIComponent(selectedCapacity)}`;
        }

        fetchWithAuth(url)
            .then(response => {
                if (!response || !response.ok) {
                    throw new Error('加载内存列表失败');
                }
                return response.json();
            })
            .then(data => {
                // 适应重构后的API响应格式
                rams = data.data || data.rams || [];
                totalRecords = data.total || 0;
                renderRamTable(rams);
                updatePagination();
            })
            .catch(error => {
                console.error('Error:', error);
                showErrorMessage(error.message);
            });
    }

    // 渲染内存表格
    function renderRamTable(data) {
        console.log('[DEBUG] Rendering RAM table with', data.length, 'records');
        ramTableBody.innerHTML = '';

        if (!data || data.length === 0) {
            console.log('[DEBUG] No data to display');
            const emptyRow = document.createElement('tr');
            const emptyCell = document.createElement('td');
            emptyCell.colSpan = 7;  // 更新列数
            emptyCell.className = 'px-3 py-4 text-center text-gray-500';
            emptyCell.textContent = '暂无数据';
            emptyRow.appendChild(emptyCell);
            ramTableBody.appendChild(emptyRow);
            return;
        }

        // Debug the first item to see its structure
        console.log('[DEBUG] First item data structure:', data[0]);
        
        // 检测是否为移动设备
        const isMobile = window.innerWidth < 640;
        
        // 根据设备类型选择渲染方法
        if (isMobile) {
            renderMobileCards(data);
        } else {
            // PC端表格布局 (增强版)
            data.forEach((ram, index) => {
                const row = document.createElement('tr');
                // 添加淡入动画和悬停效果
                row.className = 'hover:bg-gray-50 transition-colors border-b border-gray-200';
                row.style.animation = 'fadeIn 0.3s ease-in-out';
                row.style.animationFillMode = 'both';
                row.style.animationDelay = `${index * 0.05}s`;

                // 图片列
                const imageCell = document.createElement('td');
                imageCell.className = 'px-2 py-2 sm:px-3 sm:py-3 hidden sm:table-cell text-center';
                
                const imgContainer = document.createElement('div');
                imgContainer.className = 'w-10 h-10 mx-auto relative group';
                
                const defaultImage = '/images/default-ram.png';
                const imgElement = document.createElement('img');
                imgElement.src = ram.image_url || defaultImage;
                imgElement.alt = ram.model || 'RAM图片';
                imgElement.className = `w-10 h-10 object-contain mx-auto rounded-md border border-gray-200 ${!ram.image_url ? 'opacity-60' : ''} transition-transform group-hover:scale-110 cursor-pointer preview-image`;
                imgElement.dataset.src = ram.image_url || defaultImage; // 添加data-src属性
                imgElement.onerror = () => {
                    console.log('[DEBUG] Image failed to load, using default');
                    imgElement.src = defaultImage;
                    imgElement.dataset.src = defaultImage;
                };
                
                // 添加查看图标提示 - 修改为也具有点击预览功能，不阻止事件
                const viewOverlay = document.createElement('div');
                viewOverlay.className = 'absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 rounded-md flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none'; // 添加pointer-events-none避免挡住点击
                viewOverlay.innerHTML = '<i class="fas fa-search-plus text-white text-opacity-80"></i>';
                
                imgContainer.appendChild(imgElement);
                imgContainer.appendChild(viewOverlay);
                imageCell.appendChild(imgContainer);
                row.appendChild(imageCell);

                // 型号列
                const modelCell = document.createElement('td');
                modelCell.className = 'px-2 py-2 sm:px-3 sm:py-3';
            
                const modelText = document.createElement('div');
                modelText.className = 'text-sm font-medium text-gray-900 text-truncate flex items-center';
                modelText.title = ram.model || '-';

                // 添加RAM品牌图标
                const brandIconContainer = document.createElement('span');
                brandIconContainer.className = 'inline-block mr-2';
                
                // 根据品牌名称显示不同图标
                if (ram.brand && ram.brand.includes('芝奇')) {
                    brandIconContainer.innerHTML = '<i class="fas fa-bolt text-red-600"></i>';
                } else if (ram.brand && ram.brand.includes('海盗船')) {
                    brandIconContainer.innerHTML = '<i class="fas fa-ship text-yellow-600"></i>';
                } else if (ram.brand && ram.brand.includes('金士顿')) {
                    brandIconContainer.innerHTML = '<i class="fas fa-crown text-blue-600"></i>';
                } else if (ram.brand && ram.brand.includes('威刚')) {
                    brandIconContainer.innerHTML = '<i class="fas fa-shield-alt text-green-600"></i>';
                } else if (ram.brand && ram.brand.includes('英睿达')) {
                    brandIconContainer.innerHTML = '<i class="fas fa-microchip text-purple-600"></i>';
                } else {
                    brandIconContainer.innerHTML = '<i class="fas fa-memory text-gray-500"></i>';
                }
                
                modelText.appendChild(brandIconContainer);
                
                // 型号文本
                const modelName = document.createElement('span');
                modelName.textContent = ram.model || '-';
                modelText.appendChild(modelName);
                
                modelCell.appendChild(modelText);
                row.appendChild(modelCell);

                // 品牌列
                const brandCell = document.createElement('td');
                brandCell.className = 'px-2 py-2 sm:px-3 sm:py-3 hidden sm:table-cell';
                
                // 品牌标签
                const brandBadge = document.createElement('span');
                brandBadge.className = 'px-2 py-1 rounded-md text-xs font-medium bg-green-100 text-green-800 border border-green-200 transition-transform hover:scale-105';
                brandBadge.textContent = ram.brand || '-';
                brandCell.appendChild(brandBadge);
                
                row.appendChild(brandCell);

                // 容量/类型合并列
                const capacityCell = document.createElement('td');
                capacityCell.className = 'px-2 py-2 sm:px-3 sm:py-3';
                
                const capacityIcon = document.createElement('span');
                capacityIcon.className = 'mr-1 text-purple-600';
                capacityIcon.innerHTML = '<i class="fas fa-database"></i>';
                
                const capacityTag = document.createElement('span');
                capacityTag.className = 'inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-purple-100 text-purple-800 border border-purple-200 transition-all hover:shadow-sm';
                capacityTag.appendChild(capacityIcon);
                
                const capacityText = document.createElement('span');
                
                // 同时显示容量和内存类型
                let displayText = '';
                if (ram.capacity) {
                    displayText = `${ram.capacity}GB`;
                    if (ram.memory_type) {
                        displayText += ` ${ram.memory_type}`;
                    }
                } else if (ram.memory_type) {
                    displayText = ram.memory_type;
                } else {
                    displayText = '-';
                }
                
                capacityText.textContent = displayText;
                capacityTag.appendChild(capacityText);
                
                capacityCell.appendChild(capacityTag);
                row.appendChild(capacityCell);

                // 频率列
                const speedCell = document.createElement('td');
                speedCell.className = 'px-2 py-2 sm:px-3 sm:py-3 hidden sm:table-cell';
                
                const speedIcon = document.createElement('span');
                speedIcon.className = 'mr-1 text-amber-600';
                speedIcon.innerHTML = '<i class="fas fa-tachometer-alt"></i>';
                
                const speedTag = document.createElement('span');
                speedTag.className = 'inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-amber-100 text-amber-800 border border-amber-200 transition-all hover:shadow-sm';
                speedTag.appendChild(speedIcon);
                
                const speedText = document.createElement('span');
                speedText.textContent = ram.speed ? `${ram.speed}MHz` : '-';
                speedTag.appendChild(speedText);
                
                speedCell.appendChild(speedTag);
                row.appendChild(speedCell);
                
                // CAS延迟列
                const casLatencyCell = document.createElement('td');
                casLatencyCell.className = 'px-2 py-2 sm:px-3 sm:py-3 hidden sm:table-cell';
                
                const casLatencyIcon = document.createElement('span');
                casLatencyIcon.className = 'mr-1 text-indigo-600';
                casLatencyIcon.innerHTML = '<i class="fas fa-clock"></i>';
                
                const casLatencyTag = document.createElement('span');
                casLatencyTag.className = 'inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-indigo-100 text-indigo-800 border border-indigo-200 transition-all hover:shadow-sm';
                casLatencyTag.appendChild(casLatencyIcon);
                
                const casLatencyText = document.createElement('span');
                casLatencyText.textContent = ram.cas_latency ? `CL${ram.cas_latency}` : '-';
                casLatencyTag.appendChild(casLatencyText);
                
                casLatencyCell.appendChild(casLatencyTag);
                row.appendChild(casLatencyCell);

                // 操作列
                const actionCell = document.createElement('td');
                actionCell.className = 'px-2 py-2 sm:px-3 sm:py-3 text-center';

                // 操作按钮容器
                const actionContainer = document.createElement('div');
                actionContainer.className = 'flex justify-center space-x-2';

                // 查看按钮
                const viewButton = document.createElement('button');
                viewButton.className = 'p-1 text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded-full w-7 h-7 flex items-center justify-center transition-all';
                viewButton.innerHTML = '<i class="fas fa-eye"></i>';
                viewButton.title = '查看详情';
                viewButton.dataset.id = ram.id;
                viewButton.addEventListener('click', () => viewRamDetails(ram.id));
                viewButton.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-2px)';
                });
                viewButton.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                });
                actionContainer.appendChild(viewButton);

                // 编辑按钮
                const editButton = document.createElement('button');
                editButton.className = 'p-1 text-green-600 hover:text-green-800 hover:bg-green-50 rounded-full w-7 h-7 flex items-center justify-center transition-all';
                editButton.innerHTML = '<i class="fas fa-edit"></i>';
                editButton.title = '编辑';
                editButton.dataset.id = ram.id;
                editButton.addEventListener('click', () => editRam(ram.id));
                editButton.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-2px)';
                });
                editButton.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                });
                actionContainer.appendChild(editButton);

                // 删除按钮
                const deleteButton = document.createElement('button');
                deleteButton.className = 'p-1 text-red-600 hover:text-red-800 hover:bg-red-50 rounded-full w-7 h-7 flex items-center justify-center transition-all';
                deleteButton.innerHTML = '<i class="fas fa-trash"></i>';
                deleteButton.title = '删除';
                deleteButton.dataset.id = ram.id;
                deleteButton.addEventListener('click', () => {
                    confirmDelete(ram.id);
                });
                deleteButton.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-2px)';
                });
                deleteButton.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                });
                actionContainer.appendChild(deleteButton);

                actionCell.appendChild(actionContainer);
                row.appendChild(actionCell);

                ramTableBody.appendChild(row);
            });
        }
        
        console.log('[DEBUG] Table rendering complete');
    }
    
    // 更新分页控件
    function updatePagination() {
        console.log('[DEBUG] Updating pagination with', totalRecords, 'total records and current page', currentPage);
        
        // 计算总页数
        const totalPages = Math.ceil(totalRecords / pageSize) || 1;
        
        // 更新总记录数显示
        if (totalCount) {
            totalCount.textContent = `共 ${totalRecords} 条记录`;
        }
        
        // 更新当前页和总页数显示
        const currentPageDisplay = document.getElementById('currentPageDisplay');
        const totalPagesDisplay = document.getElementById('totalPagesDisplay');
        
        if (currentPageDisplay) {
            currentPageDisplay.textContent = currentPage;
        }
        
        if (totalPagesDisplay) {
            totalPagesDisplay.textContent = totalPages;
        }
        
        // 更新页码按钮 (如果存在)
        const pageNumbers = document.getElementById('pageNumbers');
        if (pageNumbers) {
            pageNumbers.innerHTML = '';
            
            // 确定显示哪些页码按钮
            const maxVisibleButtons = 5;
            let startPage = Math.max(1, currentPage - Math.floor(maxVisibleButtons / 2));
            let endPage = Math.min(totalPages, startPage + maxVisibleButtons - 1);
            
            // 调整起始页，确保显示足够数量的页码按钮
            if (endPage - startPage + 1 < maxVisibleButtons) {
                startPage = Math.max(1, endPage - maxVisibleButtons + 1);
            }
            
            // 生成页码按钮
            for (let i = startPage; i <= endPage; i++) {
                const pageButton = document.createElement('button');
                pageButton.textContent = i;
                pageButton.className = i === currentPage
                    ? 'px-3 py-1 border border-green-500 bg-green-100 text-green-700 rounded-md text-sm font-medium'
                    : 'px-3 py-1 border border-gray-300 bg-white text-gray-700 rounded-md text-sm hover:bg-gray-50';
                pageButton.addEventListener('click', () => changePage(i));
                pageNumbers.appendChild(pageButton);
            }
        }
        
        // 启用/禁用上一页和下一页按钮
        if (prevPage) {
            prevPage.disabled = currentPage <= 1;
            prevPage.classList.toggle('opacity-50', currentPage <= 1);
            prevPage.classList.toggle('cursor-not-allowed', currentPage <= 1);
        }
        
        if (nextPage) {
            nextPage.disabled = currentPage >= totalPages;
            nextPage.classList.toggle('opacity-50', currentPage >= totalPages);
            nextPage.classList.toggle('cursor-not-allowed', currentPage >= totalPages);
        }
        
        // 首页和尾页按钮
        const firstPageBtn = document.getElementById('firstPage');
        const lastPageBtn = document.getElementById('lastPage');
        
        if (firstPageBtn) {
            firstPageBtn.disabled = currentPage <= 1;
            firstPageBtn.classList.toggle('opacity-50', currentPage <= 1);
            firstPageBtn.classList.toggle('cursor-not-allowed', currentPage <= 1);
        }
        
        if (lastPageBtn) {
            lastPageBtn.disabled = currentPage >= totalPages;
            lastPageBtn.classList.toggle('opacity-50', currentPage >= totalPages);
            lastPageBtn.classList.toggle('cursor-not-allowed', currentPage >= totalPages);
        }
        
        // 设置页码输入框的最大值
        const pageJump = document.getElementById('pageJump');
        if (pageJump) {
            pageJump.max = totalPages;
        }
        
        console.log('[DEBUG] Pagination updated. Total pages:', totalPages);
    }
    
    // 更改页码
    function changePage(pageNum) {
        if (pageNum < 1) pageNum = 1;
        
        // 获取总页数
        const totalPages = Math.ceil(totalRecords / pageSize) || 1;
        
        // 确保页码不超过总页数
        if (pageNum > totalPages) pageNum = totalPages;
        
        // 如果页码没有变化，不执行操作
        if (pageNum === currentPage) return;
        
        // 更新当前页码并加载数据
        currentPage = pageNum;
        loadRams();
    }

    // 移动端卡片式布局渲染
    function renderMobileCards(data) {
        ramTableBody.innerHTML = '';
        const tableElement = ramTableBody.closest('table');
        if (tableElement) {
            tableElement.style.display = 'block';
            tableElement.style.width = '100%';
            tableElement.style.maxWidth = '100%';
            tableElement.style.borderCollapse = 'collapse';
            tableElement.style.borderSpacing = '0';
            const theadElement = tableElement.querySelector('thead');
            if (theadElement) {
                theadElement.style.display = 'none';
            }
        }
        ramTableBody.style.display = 'block';
        ramTableBody.style.width = '100%';
        ramTableBody.style.maxWidth = '100%';

        data.forEach((ram, index) => {
            const cardOuterContainer = document.createElement('div');
            cardOuterContainer.className = 'ram-card-outer-container';
            cardOuterContainer.style.width = '100%';
            cardOuterContainer.style.maxWidth = '100vw';
            cardOuterContainer.style.boxSizing = 'border-box';
            cardOuterContainer.style.padding = '0 4px'; // 调整左右边距为4px
            cardOuterContainer.style.marginBottom = '12px'; // 减小卡片间距
            cardOuterContainer.style.position = 'relative';
            cardOuterContainer.style.animation = `fadeIn 0.3s ease-in-out ${index * 0.05}s both`;

            const card = document.createElement('div');
            card.className = 'ram-card';
            card.style.width = '100%';
            card.style.borderRadius = '12px'; // 增加圆角
            card.style.overflow = 'hidden';
            card.style.backgroundColor = isDarkMode ? 'var(--bg-secondary)' : 'white';
            card.style.boxShadow = isDarkMode ? '0 5px 15px rgba(0,0,0,0.3)' : '0 5px 15px rgba(0,0,0,0.07)';
            card.style.display = 'flex';
            card.style.flexDirection = 'column';
            card.style.minWidth = '0';
            card.style.transition = 'transform 0.2s ease, box-shadow 0.2s ease';
            card.style.border = isDarkMode ? '1px solid rgba(255, 255, 255, 0.05)' : 'none';

            card.addEventListener('touchstart', function() {
                this.style.transform = 'scale(0.99)'; 
                this.style.boxShadow = isDarkMode ? '0 2px 8px rgba(0,0,0,0.4)' : '0 2px 8px rgba(0,0,0,0.05)';
            });
            card.addEventListener('touchend', function() {
                this.style.transform = 'scale(1)';
                this.style.boxShadow = isDarkMode ? '0 5px 15px rgba(0,0,0,0.3)' : '0 5px 15px rgba(0,0,0,0.07)';
            });
            
            let themeColor, lightThemeColor, borderThemeColor, headerBgColor;
            const memTypeLower = (ram.memory_type || '').toLowerCase();

            if (memTypeLower.includes('ddr5')) {
                themeColor = isDarkMode ? '#60a5fa' : '#3B82F6';
                lightThemeColor = isDarkMode ? 'rgba(96, 165, 250, 0.1)' : 'rgba(59, 130, 246, 0.08)';
                borderThemeColor = isDarkMode ? 'rgba(96, 165, 250, 0.2)' : 'rgba(59, 130, 246, 0.15)';
                headerBgColor = isDarkMode ? 'rgba(96, 165, 250, 0.05)' : '#f0f7ff';
            } else if (memTypeLower.includes('ddr3')) {
                themeColor = isDarkMode ? '#a78bfa' : '#8B5CF6';
                lightThemeColor = isDarkMode ? 'rgba(167, 139, 250, 0.1)' : 'rgba(139, 92, 246, 0.08)';
                borderThemeColor = isDarkMode ? 'rgba(167, 139, 250, 0.2)' : 'rgba(139, 92, 246, 0.15)';
                headerBgColor = isDarkMode ? 'rgba(167, 139, 250, 0.05)' : '#f5f3ff';
            } else { // DDR4 和其他
                themeColor = isDarkMode ? '#4ade80' : '#16a34a';
                lightThemeColor = isDarkMode ? 'rgba(74, 222, 128, 0.1)' : 'rgba(76, 175, 80, 0.08)';
                borderThemeColor = isDarkMode ? 'rgba(74, 222, 128, 0.2)' : 'rgba(76, 175, 80, 0.15)';
                headerBgColor = isDarkMode ? 'rgba(74, 222, 128, 0.05)' : '#f1f8f1';
            }
            
            const cardHeader = document.createElement('div');
            cardHeader.style.padding = '12px 16px';
            cardHeader.style.backgroundColor = headerBgColor;
            cardHeader.style.borderBottom = `1px solid ${borderThemeColor}`;
            cardHeader.style.display = 'flex';
            cardHeader.style.alignItems = 'center';

            const imgContainer = document.createElement('div');
            imgContainer.className = 'flex-shrink-0 mr-3';
            const imgElement = document.createElement('img');
            imgElement.src = ram.image_url || '/images/default-ram.png';
            imgElement.alt = ram.model || 'RAM图片';
            imgElement.className = 'h-12 w-12 object-contain rounded-md shadow-sm cursor-pointer preview-image';
            imgElement.style.border = `1px solid ${borderThemeColor}`;
            imgElement.dataset.src = ram.image_url || '/images/default-ram.png'; // 添加data-src属性
            // 移除单独的点击事件处理，统一使用全局事件委托
            imgElement.onerror = function() { 
                this.onerror = null; 
                this.src = '/images/default-ram.png';
                this.dataset.src = '/images/default-ram.png';
            };
            imgContainer.appendChild(imgElement);

            const titleContainer = document.createElement('div');
            titleContainer.className = 'flex-1 overflow-hidden';
            titleContainer.style.minWidth = '0';

            const modelText = document.createElement('div');
            modelText.style.fontSize = '1rem'; // 调整字体大小
            modelText.style.fontWeight = '600'; // 调整字重
            modelText.style.color = isDarkMode ? 'var(--text-primary)' : '#2d3748';
            modelText.style.marginBottom = '4px'; // 增加与下方徽章的间距
            modelText.style.lineHeight = '1.35';
            modelText.style.wordBreak = 'break-word';
            modelText.textContent = ram.model || '未知型号';

            const brandContainer = document.createElement('div');
            brandContainer.className = 'flex items-center text-xs flex-wrap'; // 移除 mt-1
            brandContainer.style.gap = '6px';

            const brandBadge = createBadge(ram.brand || '未知', themeColor, lightThemeColor, borderThemeColor, getRamBrandIcon(ram.brand));
            brandContainer.appendChild(brandBadge);

            if (ram.capacity) {
                const capacityBadge = createBadge(`${ram.capacity}GB`, '#4A5568', 'rgba(226, 232, 240, 0.5)', 'rgba(203, 213, 225, 0.5)', 'fas fa-database');
                brandContainer.appendChild(capacityBadge);
            }
            if (ram.memory_type) {
                const typeBadge = createBadge(ram.memory_type, themeColor, lightThemeColor, borderThemeColor, 'fas fa-microchip');
                brandContainer.appendChild(typeBadge);
            }

            titleContainer.appendChild(modelText);
            titleContainer.appendChild(brandContainer);
            cardHeader.appendChild(imgContainer);
            cardHeader.appendChild(titleContainer);

            const cardBody = document.createElement('div');
            cardBody.style.padding = '12px 16px';
            cardBody.style.backgroundColor = isDarkMode ? 'var(--bg-secondary)' : 'white';

            const specsHighlight = document.createElement('div');
            specsHighlight.style.display = 'flex';
            specsHighlight.style.justifyContent = 'space-around'; // 改为space-around使其均匀分布
            specsHighlight.style.alignItems = 'stretch'; // 让子项高度一致
            specsHighlight.style.marginBottom = '12px';
            specsHighlight.style.backgroundColor = lightThemeColor;
            specsHighlight.style.padding = '12px 8px'; // 调整内边距
            specsHighlight.style.borderRadius = '8px';
            
            let hasHighlight = false;
            if (ram.speed) {
                specsHighlight.appendChild(createHighlightItem('频率', `${ram.speed} MHz`, 'fas fa-tachometer-alt', themeColor));
                hasHighlight = true;
            }
            if (ram.cas_latency) {
                specsHighlight.appendChild(createHighlightItem('延迟', `CL${ram.cas_latency}`, 'fas fa-clock', themeColor));
                hasHighlight = true;
            }
            if (ram.voltage) {
                specsHighlight.appendChild(createHighlightItem('电压', `${ram.voltage}V`, 'fas fa-bolt', themeColor));
                hasHighlight = true;
            }
            if(hasHighlight) cardBody.appendChild(specsHighlight);

            const infoGrid = document.createElement('div');
            infoGrid.style.display = 'grid';
            infoGrid.style.gridTemplateColumns = 'repeat(auto-fit, minmax(100px, 1fr))'; // 调整minmax
            infoGrid.style.gap = '10px'; // 调整gap
            infoGrid.style.marginBottom = '12px';

            if (ram.modules) infoGrid.appendChild(createInfoItem('条数', `${ram.modules}条`, 'fas fa-layer-group', themeColor));
            
            const tagGroup = document.createElement('div');
            tagGroup.className = 'flex flex-wrap gap-2';

            if (ram.heat_spreader && ram.heat_spreader !== '0' && ram.heat_spreader !== 'No') {
                const heatSpreaderColor = isDarkMode ? '#f6e05e' : '#D69E2E';
                const heatSpreaderBgColor = isDarkMode ? 'rgba(246, 224, 94, 0.1)' : 'rgba(246, 224, 179, 0.4)';
                tagGroup.appendChild(createSpecTag('散热片', 'fas fa-snowflake', heatSpreaderColor, heatSpreaderBgColor));
            }
            if (ram.rgb_lighting && ram.rgb_lighting !== '0' && ram.rgb_lighting !== 'No') {
                const rgbColor = isDarkMode ? '#b794f4' : '#805AD5';
                const rgbBgColor = isDarkMode ? 'rgba(183, 148, 244, 0.1)' : 'rgba(219, 203, 245, 0.4)';
                tagGroup.appendChild(createSpecTag('RGB灯效', 'fas fa-lightbulb', rgbColor, rgbBgColor));
            }
            if (ram.color) {
                const colorColor = isDarkMode ? '#a0aec0' : '#4A5568';
                const colorBgColor = isDarkMode ? 'rgba(160, 174, 192, 0.1)' : 'rgba(226, 232, 240, 0.5)';
                tagGroup.appendChild(createSpecTag(ram.color, 'fas fa-palette', colorColor, colorBgColor));
            }
            if (infoGrid.children.length > 0) cardBody.appendChild(infoGrid);
            if (tagGroup.children.length > 0) cardBody.appendChild(tagGroup);

            if (ram.notes) {
                const notesContainer = document.createElement('div');
                notesContainer.className = 'text-xs mt-3 p-2.5 rounded-md'; // 增加padding
                notesContainer.style.backgroundColor = isDarkMode ? 'rgba(255,255,255,0.03)' : '#f7fafc';
                notesContainer.style.border = `1px solid ${isDarkMode ? 'rgba(255,255,255,0.07)' : '#e2e8f0'}`;
                notesContainer.style.lineHeight = '1.5';
                notesContainer.style.color = isDarkMode ? 'var(--text-secondary)' : '#4A5568';
                notesContainer.innerHTML = `<i class="fas fa-sticky-note text-gray-400 mr-1.5"></i> ${ram.notes.length > 60 ? ram.notes.substring(0, 60) + '...' : ram.notes}`;
                cardBody.appendChild(notesContainer);
            }

            const cardFooter = document.createElement('div');
            cardFooter.style.padding = '12px 16px';
            cardFooter.style.backgroundColor = isDarkMode ? 'rgba(0,0,0,0.1)' : '#fdfdff';
            cardFooter.style.borderTop = `1px solid ${borderThemeColor}`;
            cardFooter.style.display = 'flex';
            cardFooter.style.justifyContent = 'space-around'; // 让按钮均匀分布
            cardFooter.style.gap = '12px'; // 调整按钮间距

            const viewButton = createActionButton('<i class="fas fa-eye"></i>', '查看详情', lightThemeColor, themeColor, () => viewRamDetails(ram.id));
            const editButton = createActionButton('<i class="fas fa-edit"></i>', '编辑', 'rgba(16, 185, 129, 0.08)', '#10b981', () => editRam(ram.id));
            const deleteButton = createActionButton('<i class="fas fa-trash"></i>', '删除', 'rgba(239, 68, 68, 0.08)', '#ef4444', () => confirmDelete(ram.id));
            
            cardFooter.appendChild(viewButton);
            cardFooter.appendChild(editButton);
            cardFooter.appendChild(deleteButton);

            card.appendChild(cardHeader);
            card.appendChild(cardBody);
            card.appendChild(cardFooter);
            cardOuterContainer.appendChild(card);
            ramTableBody.appendChild(cardOuterContainer);
        });
    }

    function createInfoItem(label, value, iconClass, themeColor) {
        const item = document.createElement('div');
        item.style.display = 'flex';
        item.style.flexDirection = 'column';
        item.style.alignItems = 'center';
        item.style.padding = '6px 4px'; // 调整内边距
        item.style.textAlign = 'center';

        const iconEl = document.createElement('div');
        iconEl.innerHTML = `<i class="${iconClass}" style="color:${themeColor}; font-size: 0.9rem;"></i>`; // 调整图标大小
        iconEl.style.marginBottom = '4px';
        
        const valueEl = document.createElement('div');
        valueEl.className = 'text-xs font-medium'; // 调整字体大小
        valueEl.textContent = value;
        valueEl.style.color = isDarkMode ? 'var(--text-primary)' : '#4A5568';
        valueEl.style.marginBottom = '2px';
        
        const labelEl = document.createElement('div');
        labelEl.className = 'text-xxs text-gray-500'; // 更小的标签字体
        labelEl.textContent = label;
        labelEl.style.textTransform = 'uppercase'; // 标签大写

        item.appendChild(iconEl);
        item.appendChild(valueEl);
        item.appendChild(labelEl);
        return item;
    }

    function createHighlightItem(label, value, iconClass, themeColor) {
        const item = document.createElement('div');
        item.className = 'text-center flex-1 px-1'; // 增加水平padding
        item.style.minWidth = '70px'; // 调整最小宽度

        const iconEl = document.createElement('div');
        iconEl.className = 'mb-1.5'; // 调整间距
        iconEl.innerHTML = `<i class="${iconClass}" style="color:${themeColor}; font-size: 1.2rem;"></i>`; // 调整图标大小
        
        const valueEl = document.createElement('div');
        valueEl.className = 'font-semibold'; // 改为semibold
        valueEl.style.fontSize = '1.1rem'; // 调整字体大小
        valueEl.style.color = themeColor;
        valueEl.style.lineHeight = '1.2';
        valueEl.textContent = value;
        
        const labelEl = document.createElement('div');
        labelEl.className = 'text-xs text-gray-500 mt-0.5'; // 调整间距
        labelEl.textContent = label;

        item.appendChild(iconEl);
        item.appendChild(valueEl);
        item.appendChild(labelEl);
        return item;
    }

    function createSpecTag(text, iconClass, color, bgColor) {
        const tag = document.createElement('span');
        tag.className = 'inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium'; // 使用rounded-full
        tag.style.backgroundColor = bgColor;
        tag.style.color = color;
        tag.style.border = `1px solid ${color}26`; // 更淡的边框
        tag.innerHTML = `<i class="${iconClass} mr-1.5"></i> ${text}`;
        return tag;
    }

    function createBadge(text, textColor, bgColor, borderColor, iconClass = null) {
        const badge = document.createElement('span');
        badge.className = 'inline-flex items-center';
        badge.style.padding = '3px 8px';
        badge.style.backgroundColor = bgColor;
        badge.style.color = textColor;
        badge.style.border = `1px solid ${borderColor}`;
        badge.style.fontWeight = '500'; // 调整字重
        badge.style.borderRadius = '6px'; // 调整圆角
        badge.style.fontSize = '0.7rem'; // 调整字体大小
        if (iconClass) {
            badge.innerHTML = `<i class="${iconClass} mr-1.5"></i> ${text}`;
        } else {
            badge.textContent = text;
        }
        return badge;
    }

    function createActionButton(innerHTML, title, bgColor, textColor, onClick) {
        const button = document.createElement('button');
        button.style.width = '40px'; // 调整按钮大小
        button.style.height = '40px';
        button.style.borderRadius = '50%';
        button.style.display = 'flex';
        button.style.alignItems = 'center';
        button.style.justifyContent = 'center';
        button.style.backgroundColor = bgColor;
        button.style.color = textColor;
        button.style.transition = 'all 0.15s ease-out'; // 调整动画
        button.style.boxShadow = '0 1px 3px rgba(0,0,0,0.05)'; // 更细微的阴影
        button.innerHTML = innerHTML;
        button.title = title;
        button.addEventListener('click', onClick);
        button.addEventListener('touchstart', function() { this.style.transform = 'scale(0.92)'; this.style.boxShadow = '0 0 0 rgba(0,0,0,0.05)';});
        button.addEventListener('touchend', function() { this.style.transform = 'scale(1)'; this.style.boxShadow = '0 1px 3px rgba(0,0,0,0.05)';});
        return button;
    }
    
    function getRamBrandIcon(brandName) {
        if (!brandName) return 'fas fa-memory';
        const lowerBrand = brandName.toLowerCase();
        if (lowerBrand.includes('芝奇') || lowerBrand.includes('g.skill')) return 'fas fa-bolt';
        if (lowerBrand.includes('海盗船') || lowerBrand.includes('corsair')) return 'fas fa-ship';
        if (lowerBrand.includes('金士顿') || lowerBrand.includes('kingston')) return 'fas fa-crown';
        if (lowerBrand.includes('威刚') || lowerBrand.includes('adata')) return 'fas fa-shield-alt';
        if (lowerBrand.includes('英睿达') || lowerBrand.includes('crucial')) return 'fas fa-microchip';
        return 'fas fa-memory';
    }

    // 确认删除
    function confirmDelete(id) {
        // 如果没有传入ID，使用当前选中的ID
        const ramId = id || currentRamId;
        if (!ramId) {
            showErrorMessage('没有选择要删除的内存');
            return;
        }

        if (confirm('确定要删除这条内存信息吗？删除后无法恢复。')) {
            deleteRam(ramId);
        }
    }

    // 查看内存详情
    window.viewRamDetails = function (id) {
        fetch(`${API_ENDPOINTS.RAM(id)}`)
            .then(response => {
                if (!response.ok) {
                    throw new Error('加载内存详情失败');
                }
                return response.json();
            })
            .then(data => {
                currentRamId = id;
                renderRamDetails(data);
                // 检查ramModal元素是否存在
                if (ramModal) {
                    ramModal.classList.remove('hidden');
                } else {
                    // 尝试获取正确的模态框元素
                    const detailModal = document.getElementById('ramDetailModal');
                    if (detailModal) {
                        detailModal.classList.remove('hidden');
                        
                        // 检测是否为移动设备，处理关闭按钮
                        const isMobile = window.innerWidth < 640;
                        const mobileCloseBtn = detailModal.querySelector('.mobile-close-button');
                        if (mobileCloseBtn) {
                            if (isMobile) {
                                mobileCloseBtn.classList.remove('hidden');
                            } else {
                                mobileCloseBtn.classList.add('hidden');
                            }
                        }
                        
                        // 移动端下固定body防止背景滚动
                        if (isMobile) {
                            document.body.style.overflow = 'hidden';
                        }
                    }
                }
                
                // 在详情模态框打开后额外检查一次权限
                // 确保非管理员用户的按钮有正确的事件处理
                isAdmin().then(isAdminUser => {
                    if (!isAdminUser) {
                        const modalEditBtn = document.getElementById('editBtn');
                        const modalDeleteBtn = document.getElementById('deleteBtn');
                        
                        if (modalEditBtn) {
                            // 清除可能的旧事件监听器
                            modalEditBtn.replaceWith(modalEditBtn.cloneNode(true));
                            const newEditBtn = document.getElementById('editBtn');
                            
                            newEditBtn.addEventListener('click', function(e) {
                                e.preventDefault();
                                e.stopPropagation();
                                showErrorMessage('权限不足，只有管理员可以编辑数据');
                                return false;
                            });
                        }
                        
                        if (modalDeleteBtn) {
                            // 清除可能的旧事件监听器
                            modalDeleteBtn.replaceWith(modalDeleteBtn.cloneNode(true));
                            const newDeleteBtn = document.getElementById('deleteBtn');
                            
                            newDeleteBtn.addEventListener('click', function(e) {
                                e.preventDefault();
                                e.stopPropagation();
                                showErrorMessage('权限不足，只有管理员可以删除数据');
                                return false;
                            });
                        }
                    }
                });
            })
            .catch(error => {
                console.error('Error:', error);
                showErrorMessage(error.message);
            });
    };

    // 渲染内存详情
    function renderRamDetails(ram) {
        const container = document.getElementById('ramDetails') 
                       || document.getElementById('ramDetailContent');
        if (!container) {
            console.error('Error: ramDetails element not found');
            return;
        }
    
        const booleanDisplay = value => {
            if (value === null || value === undefined || value === '') return '-';
            return value === 1 || value === true || value === '1' || value === '有' ? '有' : '无';
        };
    
        // 内存类型优先 memory_type 或 type
        const memoryType = ram.memory_type || ram.type || '-';
    
        // 准备图片HTML - 修改为使用data-src属性而不是内联onclick
        let imgHtml = '';
        if (ram.image_url) {
            imgHtml = `
                        <div class="w-full md:w-1/3">
                <div class="bg-gray-50 p-2 rounded-lg shadow-md h-full flex items-center justify-center">
                    <img src="${ram.image_url}" alt="${ram.model}" class="w-full rounded-lg cursor-pointer hover:opacity-90 preview-image" 
                        data-src="${ram.image_url}" style="max-height: 220px; object-fit: contain;">
                </div>
            </div>
            `;
        }
    
        container.innerHTML = `
            <!-- 主要信息区：图片和重要信息并排显示 -->
            <div class="flex flex-col md:flex-row gap-6">
                <!-- 左侧内存图片 -->
                ${imgHtml ? imgHtml : `
                <div class="md:w-1/3">
                    <div class="bg-gray-100 p-6 rounded-lg h-48 flex items-center justify-center shadow-inner">
                        <div class="text-center text-gray-400">
                            <i class="fas fa-memory text-5xl mb-2"></i>
                            <p>暂无图片</p>
                    </div>
                </div>
                </div>
                `}
    
                <!-- 右侧重要信息 -->
                <div class="md:w-2/3">
                    <!-- 标题和价格区域 -->
                    <div class="border-b pb-3 mb-4">
                        <h2 class="text-xl font-bold text-gray-800 mb-1">${ram.model || '未知型号'}</h2>
                        <div class="flex items-center justify-between">
                            <p class="text-gray-600">${ram.brand || '-'} | ${memoryType}</p>
                            <p class="text-xl font-bold text-green-600">${ram.price ? `¥${ram.price}` : '价格未知'}</p>
                    </div>
                </div>
    
                    <!-- 关键信息区域 -->
                    <div class="grid grid-cols-2 gap-4 mb-4">
                        <div class="bg-blue-50 p-3 rounded-lg">
                            <h4 class="font-semibold text-md mb-2 text-blue-700">容量</h4>
                            <p class="text-2xl font-bold text-blue-700">${ram.capacity ? `${ram.capacity} GB` : '-'}</p>
                            <p class="text-sm text-gray-600">${ram.kit_size ? `${ram.kit_size} 条装` : '-'}</p>
                        </div>
                        <div class="bg-green-50 p-3 rounded-lg">
                            <h4 class="font-semibold text-md mb-2 text-green-700">频率</h4>
                            <p class="text-2xl font-bold text-green-700">${ram.speed || '-'}<span class="text-sm"> MHz</span></p>
                            <p class="text-sm text-gray-600">CL${ram.cas_latency || '-'}</p>
                    </div>
                </div>
    
                    <!-- 特性信息 -->
                    <div class="grid grid-cols-3 gap-4 mb-4">
                        <div>
                            <h5 class="text-sm font-medium text-gray-700">散热片</h5>
                            <p class="font-semibold">${booleanDisplay(ram.heat_spreader)}</p>
                        </div>
                        <div>
                            <h5 class="text-sm font-medium text-gray-700">XMP</h5>
                            <p class="font-semibold">${booleanDisplay(ram.xmp)}</p>
                        </div>
                        <div>
                            <h5 class="text-sm font-medium text-gray-700">RGB灯效</h5>
                            <p class="font-semibold">${booleanDisplay(ram.rgb)}</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 分隔线 -->
            <div class="border-t my-6"></div>
            
            <!-- 详细规格区域 -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <div class="border border-gray-200 rounded-lg p-4 bg-white shadow-sm">
                    <h4 class="font-semibold text-lg mb-2 text-green-700 border-b border-gray-200 pb-2">基本信息</h4>
                    <div class="space-y-2">
                        <p><span class="font-medium">型号:</span> ${ram.model || '-'}</p>
                        <p><span class="font-medium">品牌:</span> ${ram.brand || '-'}</p>
                        <p><span class="font-medium">类型:</span> ${memoryType}</p>
                        <p><span class="font-medium">容量:</span> ${ram.capacity ? ram.capacity + ' GB' : '-'}</p>
                        <p><span class="font-medium">频率:</span> ${ram.speed ? ram.speed + ' MHz' : '-'}</p>
                        <p><span class="font-medium">CAS 延迟:</span> ${ram.cas_latency || '-'}</p>
                    </div>
                </div>
                <div class="border border-gray-200 rounded-lg p-4 bg-white shadow-sm">
                    <h4 class="font-semibold text-lg mb-2 text-blue-700 border-b border-gray-200 pb-2">规格详情</h4>
                    <div class="space-y-2">
                        <p><span class="font-medium">电压:</span> ${ram.voltage ? ram.voltage + ' V' : '-'}</p>
                        <p><span class="font-medium">内存条根数:</span> ${ram.kit_size || '-'}</p>
                        <p><span class="font-medium">散热片:</span> ${booleanDisplay(ram.heat_spreader)}</p>
                        <p><span class="font-medium">XMP 支持:</span> ${booleanDisplay(ram.xmp)}</p>
                        <p><span class="font-medium">RGB 灯效:</span> ${booleanDisplay(ram.rgb)}</p>
                        <p><span class="font-medium">颜色:</span> ${ram.color || '-'}</p>
                    </div>
                </div>
            </div>
            <div class="border border-gray-200 rounded-lg p-4 bg-white shadow-sm mb-4">
                <h4 class="font-semibold text-lg mb-2 text-blue-700 border-b border-gray-200 pb-2">其他信息</h4>
                <div class="space-y-2">
                    <p><span class="font-medium">价格:</span> ${ram.price ? `¥${ram.price}` : '-'}</p>
                    <p><span class="font-medium">备注:</span> ${ram.notes || '-'}</p>
                    <p><span class="font-medium">添加时间:</span> ${ram.created_at ? new Date(ram.created_at).toLocaleString() : '-'}</p>
                    <p><span class="font-medium">最后更新:</span> ${ram.updated_at ? new Date(ram.updated_at).toLocaleString() : '-'}</p>
                </div>
            </div>
        `;

        // 移除单独的事件绑定，使用全局事件委托替代
        // 原来的单独绑定代码如下，现在移除它：
        /*
        // 绑定详情模态框中的图片点击事件
        const previewImages = container.querySelectorAll('.preview-image');
        previewImages.forEach(img => {
            img.addEventListener('click', function() {
                const src = this.getAttribute('data-src');
                if (src) {
                    openImageFullscreen(src);
                }
            });
        });
        */
    }
    

    // 编辑内存信息
    window.editRam = function (id) {
        fetch(`${API_ENDPOINTS.RAM(id)}`)
            .then(response => {
                if (!response.ok) {
                    throw new Error('加载内存详情失败');
                }
                return response.json();
            })
            .then(ram => {
                // 检查表单元素是否存在
                if (!ramForm || !brand || !model || !capacity || !memoryType || !speed ||
                    !modules || !casLatency || !voltage || !heatSpreader ||
                    !rgbLighting || !color || !price || !notes) {
                    console.error('Error: One or more form elements not found');
                    return;
                }

                console.log('编辑内存数据:', ram); // 调试输出

                // 填充表单
                brand.value = ram.brand || '';
                model.value = ram.model || '';
                // capacity.value = ram.capacity || '';
                capacity.value = ram.capacity ? ram.capacity.toString() : '';

                // 优先处理type字段，如果不存在则使用memory_type字段
                if (ram.type && ram.type !== '') {
                    memoryType.value = ram.type;
                } else if (ram.memory_type && ram.memory_type !== '') {
                    memoryType.value = ram.memory_type;
                } else {
                    memoryType.value = '';
                }

                speed.value = ram.speed || '';

                // 处理模块数量，优先使用modules，然后尝试kitSize
                if (ram.modules && ram.modules !== '') {
                    modules.value = ram.modules;
                } else if (ram.kitSize && ram.kitSize !== '') {
                    modules.value = ram.kitSize;
                } else {
                    modules.value = '';
                }

                casLatency.value = ram.cas_latency || '';
                voltage.value = ram.voltage || '';
                heatSpreader.value = ram.heat_spreader === null ? '' : ram.heat_spreader;
                rgbLighting.value = ram.rgb ? "1" : "0"; 
                color.value = ram.color || '';
                modules.value = ram.kit_size || '';
                price.value = ram.price || '';
                notes.value = ram.notes || '';

                // 检查imagePreviewContainer元素是否存在
                if (imagePreviewContainer) {
                    // 如果有图片，显示预览
                    if (ram.image_url && imagePreview) {
                        imagePreview.src = ram.image_url;
                        imagePreviewContainer.classList.remove('hidden');

                        // 添加点击预览功能
                        imagePreview.onclick = () => {
                            openImageFullscreen(ram.image_url);
                        };
                        imagePreview.style.cursor = 'pointer';
                        imagePreview.title = '点击查看大图';
                    } else {
                        imagePreviewContainer.classList.add('hidden');
                    }
                }

                // 更改标志和按钮文本
                isEditing = true;
                currentRamId = id;
                const submitBtn = ramForm.querySelector('button[type="submit"]');
                if (submitBtn) {
                    submitBtn.innerHTML = '<i class="fas fa-save mr-1 sm:mr-2"></i> 更新内存信息';
                }

                // 滚动到表单
                if (ramForm) {
                    ramForm.scrollIntoView({ behavior: 'smooth' });
                }

                // 如果是从模态框打开，则关闭模态框
                if (ramModal) {
                    ramModal.classList.add('hidden');
                } else {
                    // 尝试获取正确的模态框元素
                    const detailModal = document.getElementById('ramDetailModal');
                    if (detailModal) {
                        detailModal.classList.add('hidden');
                    }
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showErrorMessage(error.message);
            });
    };

    // 删除内存信息
    function deleteRam(id) {
        fetch(`${API_ENDPOINTS.RAM(id)}`, {
            method: 'DELETE'
        })
            .then(response => {
                if (!response.ok) {
                    return response.json().then(data => {
                        throw new Error(data.message || '删除失败');
                    });
                }
                // 尝试解析JSON，如果响应为空则返回成功状态
                return response.text().then(text => {
                    return text ? JSON.parse(text) : { success: true, message: '删除成功' };
                });
            })
            .then(data => {
                showSuccessMessage(data.message || '内存信息删除成功');

                // 如果正在编辑该记录，重置表单
                if (isEditing && currentRamId === id && typeof resetForm === 'function') {
                    resetForm();
                }

                // 关闭模态框
                if (ramModal && !ramModal.classList.contains('hidden')) {
                    ramModal.classList.add('hidden');
                } else {
                    // 尝试获取正确的模态框元素
                    const detailModal = document.getElementById('ramDetailModal');
                    if (detailModal) {
                        detailModal.classList.add('hidden');
                    }
                }

                // 重新加载列表
                loadRams();
            })
            .catch(error => {
                console.error('Error:', error);
                showErrorMessage(error.message);
            });
    }

    // 处理编辑按钮点击
    function handleEdit() {
        editRam(currentRamId);
    }

    // 显示成功消息
    function showSuccessMessage(message) {
        const alertElement = document.createElement('div');
        alertElement.className = 'fixed top-4 right-4 bg-green-50 border-l-4 border-green-500 p-4 opacity-0 transition-opacity duration-300 shadow-md fade-in';
        alertElement.innerHTML = `
            <div class="flex items-center">
                <div class="flex-shrink-0 text-green-500">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="ml-3">
                    <p class="text-sm text-green-700">${message}</p>
                </div>
            </div>
        `;
        document.body.appendChild(alertElement);

        setTimeout(() => alertElement.classList.add('opacity-100'), 10);
        setTimeout(() => {
            alertElement.classList.remove('opacity-100');
            setTimeout(() => document.body.removeChild(alertElement), 300);
        }, 3000);
    }

    // 显示错误消息
    function showErrorMessage(message) {
        const alertElement = document.createElement('div');
        alertElement.className = 'fixed top-4 right-4 bg-red-50 border-l-4 border-red-500 p-4 opacity-0 transition-opacity duration-300 shadow-md fade-in';
        alertElement.innerHTML = `
            <div class="flex items-center">
                <div class="flex-shrink-0 text-red-500">
                    <i class="fas fa-exclamation-circle"></i>
                </div>
                <div class="ml-3">
                    <p class="text-sm text-red-700">${message}</p>
                </div>
            </div>
        `;
        document.body.appendChild(alertElement);

        setTimeout(() => alertElement.classList.add('opacity-100'), 10);
        setTimeout(() => {
            alertElement.classList.remove('opacity-100');
            setTimeout(() => document.body.removeChild(alertElement), 300);
        }, 3000);
    }

    // 防抖函数
    function debounce(func, delay) {
        let timeout;
        return function() {
            const context = this;
            const args = arguments;
            clearTimeout(timeout);
            timeout = setTimeout(() => func.apply(context, args), delay);
        };
    }

    // 暴露需要全局访问的函数
    window.viewRamDetails = viewRamDetails;
    window.editRam = editRam;
    window.confirmDeleteRam = confirmDeleteRam;
    window.openImageFullscreen = openImageFullscreen;

    // 解析内存信息的主函数
    async function parseRamInfo() {
        let input = document.getElementById('smartInput').value.trim();
        if (!input) return showToast('请输入内存参数', 'red');

        document.getElementById('autoFillBtn').disabled = true;
        document.getElementById('autoFillBtn').innerHTML = '<i class="fas fa-sync fa-spin mr-2"></i>正在解析...';

        try {
            console.log(`[Parse Start] Initial Input: "${input}"`);

            // 使用正则表达式解析
            console.log('[Parse] Using regex parsing');
            const regexResult = parseWithRegex(input);
            
            if (regexResult) {
                console.log('[Parse] Using regex parsing result:', regexResult);
                fillFormWithData(regexResult);
                document.getElementById('autoFillBtn').disabled = false;
                document.getElementById('autoFillBtn').innerHTML = '<i class="fas fa-magic mr-2"></i>智能识别';
                console.log('[Parse End] Parsing process finished.');
                return;
            }

            // 如果正则解析失败，回退到本地解析
            console.log('[Parse] Regex parsing failed, falling back to local parsing');

            // 本地解析逻辑
            const result = {
                brand: '',
                model: '',
                memoryType: '',
                capacity: '',
                speed: '',
                modules: '',
                casLatency: '',
                voltage: '',
                heatSpreader: '',
                rgbLighting: '',
                color: '',
                price: '',
                notes: ''
            };

            const originalInputForNotes = input;
            let remainingInput = input;
            console.log(`[Parse Detail] Initial remainingInput: "${remainingInput}"`);

            function extractAndRemove(regex, targetField, processFunc) {
                console.log(`[Parse Detail] Attempting to extract '${targetField}' with regex: ${regex}`);
                const match = remainingInput.match(regex);
                if (match && match[1]) {
                    let value = match[1].trim();
                    console.log(`[Parse Detail] Regex Matched for '${targetField}'. Raw matched value: "${match[0]}", Extracted part: "${value}"`);
                    if (processFunc) {
                        const processedValue = processFunc(value, match);
                        console.log(`[Parse Detail] Processed value for '${targetField}': "${processedValue}" (Original: "${value}")`);
                        value = processedValue;
                    }
                    result[targetField] = value;
                    const oldRemainingInput = remainingInput;
                    remainingInput = remainingInput.replace(match[0], '').trim();
                    console.log(`[Parse Detail] Successfully extracted '${targetField}': "${value}". Remaining input: "${remainingInput}" (was: "${oldRemainingInput}")`);
                    return true;
                } else {
                    console.log(`[Parse Detail] No match for '${targetField}' with regex: ${regex} on input: "${remainingInput}"`);
                }
                return false;
            }

            const labeledFields = {
                '内存型号': { field: 'model' },
                '型号': { field: 'model' },
                '品牌': { field: 'brand' },
                '类型': { field: 'memoryType' },
                '容量(?:\\s*\\(GB\\))?': {
                    field: 'capacity',
                    process: (val) => {
                        const numMatch = val.match(/(\d+(?:\.\d+)?)/);
                        if (numMatch) {
                            let numVal = parseFloat(numMatch[1]);
                            return numVal;
                        }
                        return val;
                    }
                },
                '频率(?:\\s*\\(MHz\\))?': { field: 'speed', process: (val) => val.match(/(\d+)/)?.[1] || val },
                '根数|内存根数': { field: 'modules', process: (val) => val.match(/(\d+)/)?.[1] || val },
                'CAS延迟|CL': { field: 'casLatency', process: (val) => val.match(/(\d+(?:-\d+)*)/)?.[1] || val },
                '电压(?:\\s*\\(V\\))?': { field: 'voltage', process: (val) => val.match(/(\d+\.\d+)/)?.[1] || val },
                '散热片': { 
                    field: 'heatSpreader', 
                    process: (val) => {
                        if (val.includes('有') || val.includes('是') || val === '1') return '1';
                        if (val.includes('无') || val.includes('否') || val === '0') return '0';
                        return val;
                    } 
                },
                'RGB灯效|RGB': { 
                    field: 'rgbLighting', 
                    process: (val) => {
                        if (val.includes('有') || val.includes('是') || val === '1') return '1';
                        if (val.includes('无') || val.includes('否') || val === '0') return '0';
                        return val;
                    } 
                },
                '颜色': { field: 'color' },
                '价格': { field: 'price', process: (val) => val.match(/(\d+(?:\.\d+)?)/)?.[1] || val },
                '备注': { field: 'notes' }
            };

            console.log('[Parse Phase] Starting Labeled Field Extraction.');
            for (const labelKey in labeledFields) {
                const { field, process } = labeledFields[labelKey];
                const escapedLabelKey = labelKey.replace(/[.*+?^${}()|[\]\\]/g, '\\\\$&');
                const regex = new RegExp(`${escapedLabelKey}[：:]\\s*([^、，,。]+)`, 'i');
                extractAndRemove(regex, field, process);
            }

            // 提取常见的内存型号模式
            if (!result.model) {
                // 常见的内存型号模式: 品牌 + 系列名 + 规格
                const modelPatterns = [
                    // 芝奇 皇家戟 DDR4 3200 16GB
                    /(\w+)\s+(\w+(?:\s+\w+)?)\s+(DDR\d)\s+(\d+)\s+(\d+GB)/i,
                    // 芝奇皇家戟 3200MHz 16G
                    /(\w+)(\w+)\s+(\d+)(?:MHz)?\s+(\d+)G/i,
                    // 皇家戟 DDR4 3200 CL16
                    /(\w+(?:\s+\w+)?)\s+(DDR\d)\s+(\d+)\s+CL(\d+)/i
                ];

                for (const pattern of modelPatterns) {
                    const match = remainingInput.match(pattern);
                    if (match) {
                        result.model = match[0];
                        remainingInput = remainingInput.replace(match[0], '').trim();
                        console.log(`[Parse Detail] Extracted model using pattern: "${result.model}"`);
                        break;
                    }
                }
            }

            // 提取品牌
            if (!result.brand) {
                const brandList = ['芝奇', '金士顿', '海盗船', '美商海盗船', '英睿达', '威刚', '雷克沙', '宇瞻', '光威', '十铨', '阿斯加特', '金百达', '机械师'];
                const brandRegex = new RegExp(`(${brandList.join('|')})`, 'i');
                extractAndRemove(brandRegex, 'brand');
            }

            // 提取内存类型
            if (!result.memoryType) {
                extractAndRemove(/(DDR[2-5])/i, 'memoryType', val => val.toUpperCase());
            }

            // 提取容量
            if (!result.capacity) {
                extractAndRemove(/(\d+)\s*(?:GB|G)\b/i, 'capacity', (val, match) => match[1]);
            }

            // 提取频率
            if (!result.speed) {
                extractAndRemove(/(\d{3,5})\s*(?:MHz|MT\/s)?\b/i, 'speed', (val, match) => match[1]);
            }

            // 提取CL值
            if (!result.casLatency) {
                extractAndRemove(/C[L]?(\d+(?:-\d+)*)/i, 'casLatency', (val, match) => match[1]);
                
                // 尝试提取完整的时序参数
                if (!result.casLatency) {
                    const timingPattern = /(\d+)-(\d+)-(\d+)-(\d+)/;
                    const timingMatch = remainingInput.match(timingPattern);
                    if (timingMatch) {
                        result.casLatency = timingMatch[1];
                        remainingInput = remainingInput.replace(timingMatch[0], '').trim();
                        console.log(`[Parse Detail] Extracted CL from timing pattern: "${result.casLatency}"`);
                    }
                }
            }

            // 提取电压
            if (!result.voltage) {
                extractAndRemove(/(\d+\.\d+)\s*[V]/i, 'voltage', (val, match) => match[1]);
            }

            // 最后使用fillFormWithData函数更新UI
            fillFormWithData(result);

            document.getElementById('autoFillBtn').disabled = false;
            document.getElementById('autoFillBtn').innerHTML = '<i class="fas fa-magic mr-2"></i>智能识别';
            console.log('[Parse End] Parsing process finished.');
        } catch (error) {
            console.error('[Parse Error] Error during parsing:', error);
            showToast('解析参数时发生错误: ' + error.message, 'red');
            document.getElementById('autoFillBtn').disabled = false;
            document.getElementById('autoFillBtn').innerHTML = '<i class="fas fa-magic mr-2"></i>智能识别';
            console.log('[Parse End] Parsing process finished.');
        }
    }

    // 使用正则表达式解析内存参数
    function parseWithRegex(inputText) {
        try {
            console.log('[Regex Parse] Starting regex parsing with input:', inputText);

            // 预处理输入：替换特定替换字符
            let processedInput = inputText
                .replace(/：/g, ':') // 统一冒号
                .replace(/，/g, '、') // 统一分隔符
                .replace(/,/g, '、');

            // 初始化结果对象
            const result = {
                brand: '',
                model: '',
                memoryType: '',
                capacity: '',
                speed: '',
                modules: '',
                casLatency: '',
                voltage: '',
                heatSpreader: '',
                rgbLighting: '',
                color: '',
                price: '',
                notes: ''
            };

            // 分割键值对
            const pairs = processedInput.split(/、(?=[^:]+:)/);
            console.log('[Regex Parse] Split pairs:', pairs);

            // 字段映射表
            const fieldMappings = [
                { regex: /^品牌$/i, field: 'brand' },
                { regex: /^(内存)?型号$/i, field: 'model' },
                { regex: /^类型$/i, field: 'memoryType' },
                { regex: /^容量(\s*\(GB\))?$/i, field: 'capacity' },
                { regex: /^频率(\s*\(MHz\))?$/i, field: 'speed' },
                { regex: /^(内存)?根数$/i, field: 'modules' },
                { regex: /^CAS延迟$|^CL$/i, field: 'casLatency' },
                { regex: /^电压(\s*\(V\))?$/i, field: 'voltage' },
                { regex: /^散热片$/i, field: 'heatSpreader' },
                { regex: /^RGB灯效$|^RGB$/i, field: 'rgbLighting' },
                { regex: /^颜色$/i, field: 'color' },
                { regex: /^价格$/i, field: 'price' },
                { regex: /^备注$/i, field: 'notes' }
            ];

            // 解析每个字段
            pairs.forEach(pair => {
                // 对于没有冒号的部分（可能是型号或备注）
                if (!pair.includes(':')) {
                    // 如果很短，可能是型号
                    if (pair.trim().length < 30 && !result.model) {
                        result.model = pair.trim();
                        console.log(`[Regex Parse] Found standalone model: "${result.model}"`);
                    }
                    // 否则可能是一个备注
                    else if (!result.notes) {
                        result.notes = pair.trim();
                        console.log(`[Regex Parse] Found standalone notes: "${result.notes}"`);
                    }
                    return;
                }

                // 处理正常的键值对
                const match = pair.match(/([^:]+):\s*(.*)/);
                if (match) {
                    const key = match[1].trim();
                    let value = match[2].trim();

                    // 使用正则表达式匹配字段
                    let field = null;
                    for (const mapping of fieldMappings) {
                        if (mapping.regex.test(key)) {
                            field = mapping.field;
                            console.log(`[Regex Parse] Field "${key}" matched to "${field}" via regex`);
                            break;
                        }
                    }

                    if (field) {
                        // 特殊字段处理
                        if (field === 'capacity') {
                            // 提取数字部分
                            const numMatch = value.match(/(\d+)/);
                            if (numMatch) {
                                result[field] = numMatch[1];
                            } else {
                                result[field] = value;
                            }
                        }
                        else if (field === 'speed') {
                            // 提取频率数字部分
                            const numMatch = value.match(/(\d+)/);
                            if (numMatch) {
                                result[field] = numMatch[1];
                            } else {
                                result[field] = value;
                            }
                        }
                        else if (field === 'voltage') {
                            // 提取电压数字部分
                            const numMatch = value.match(/(\d+\.\d+)/);
                            if (numMatch) {
                                result[field] = numMatch[1];
                            } else {
                                result[field] = value;
                            }
                        }
                        else if (field === 'heatSpreader' || field === 'rgbLighting') {
                            // 转换是/否、有/无为1/0
                            if (value.includes('有') || value.includes('是') || value === '1') {
                                result[field] = '1';
                            } else if (value.includes('无') || value.includes('否') || value === '0') {
                                result[field] = '0';
                            } else {
                                result[field] = value;
                            }
                        }
                        else if (field === 'price') {
                            // 提取价格数字部分
                            const numMatch = value.match(/(\d+(?:\.\d+)?)/);
                            if (numMatch) {
                                result[field] = numMatch[1];
                            } else {
                                result[field] = value;
                            }
                        }
                        // 其他字段直接赋值
                        else {
                            result[field] = value;
                        }
                        console.log(`[Regex Parse] Mapped "${key}" to field "${field}" with value: "${result[field]}"`);
                    } else {
                        console.log(`[Regex Parse] Unknown field: "${key}" with value: "${value}"`);
                        // 如果是未知字段且备注为空，添加到备注中
                        if (!result.notes) {
                            result.notes = `${key}: ${value}`;
                        } else {
                            result.notes += `; ${key}: ${value}`;
                        }
                    }
                }
            });

            // 特殊情况：如果没有型号但有完整文本的第一行，可能是型号
            if (!result.model && inputText) {
                const firstLine = inputText.split(/[\r\n,，、]/)[0].trim();
                if (firstLine && firstLine.length < 30) {
                    result.model = firstLine;
                    console.log(`[Regex Parse] Using first line as model: "${result.model}"`);
                }
            }

            // 验证至少有一些基本信息被提取
            let filledFields = 0;
            for (const key in result) {
                if (result[key] && result[key].trim() !== '') {
                    filledFields++;
                }
            }

            if (filledFields < 2) {
                console.log('[Regex Parse] Not enough fields were parsed, result may be incomplete');
                return null;
            }

            console.log('[Regex Parse] Final result:', result);
            return result;
        } catch (error) {
            console.error('[Regex Parse] Error during regex parsing:', error);
            return null; // 解析失败返回null
        }
    }

    // 将解析结果填充到表单中
    function fillFormWithData(data) {
        console.log('[UI Update] Updating UI with recognized data:', data);
        
        // 遍历识别出的数据，更新表单字段
        Object.entries(data).forEach(([field, value]) => {
            if (value && value !== '暂无数据') {
                const element = document.getElementById(field);
                if (element) {
                    // 对于select元素，需要特殊处理
                    if (element.tagName === 'SELECT') {
                        // 尝试找到匹配的选项
                        const options = Array.from(element.options);
                        const option = options.find(opt => 
                            opt.value.toLowerCase() === value.toLowerCase() || 
                            opt.textContent.toLowerCase().includes(value.toLowerCase())
                        );
                        
                        if (option) {
                            element.value = option.value;
                        } else {
                            console.log(`[UI Update] No matching option found for ${field}: ${value}`);
                        }
                    } else {
                        element.value = value;
                    }
                    console.log(`[UI Update] Set field ${field} to ${value}`);
                } else {
                    console.log(`[UI Update] Element not found for field: ${field}`);
                }
            }
        });
        
        showToast('参数识别完成！', 'green');
    }

    // 显示Toast通知
    function showToast(message, color = '#4F46E5', autoRemove = true) {
        const toast = document.createElement('div');
        toast.className = 'fixed bottom-4 right-4 px-4 py-2 rounded-md text-white shadow-lg z-50';
        toast.style.backgroundColor = color;
        toast.textContent = message;
        document.body.appendChild(toast);

        if (autoRemove) {
            setTimeout(() => toast.remove(), 3000);
        }

        return toast;
    }

    // 确认删除RAM
    function confirmDeleteRam(id) {
        // 如果没有传入ID，使用当前选中的ID
        const ramId = id || currentRamId;
        if (!ramId) {
            showErrorMessage('没有选择要删除的内存');
            return;
        }

        if (confirm('确定要删除这条内存信息吗？删除后无法恢复。')) {
            deleteRam(ramId);
        }
    }
}); 










// 初始化权限控制
function initPermissions() {
    console.log('初始化权限控制...');
    
    // 如果没有权限控制模块，发出警告
    if (typeof setupPermissionBasedUI !== 'function') {
        console.warn('警告：权限控制模块未加载！请确保已引入permission-helper.js');
        return;
    }
    
    // 执行权限控制初始化
    setupPermissionBasedUI().then(() => {
        // 检查编辑按钮
        const editButtons = document.querySelectorAll('.btn-edit, [data-action="edit"], button[title="编辑"]');
        const deleteButtons = document.querySelectorAll('.btn-delete, [data-action="delete"], button[title="删除"]');
        
        // 根据用户角色处理按钮
        isAdmin().then(isAdminUser => {
            if (!isAdminUser) {
                // 禁用表单
                const form = document.querySelector('form');
                if (form) {
                    const inputs = form.querySelectorAll('input, select, textarea');
                    inputs.forEach(input => {
                        input.setAttribute('disabled', 'disabled');
                    });
                    
                    const submitBtn = form.querySelector('button[type="submit"]');
                    if (submitBtn) {
                        submitBtn.setAttribute('disabled', 'disabled');
                        submitBtn.title = '只有管理员可以添加或修改数据';
                    }
                }
                
                // 隐藏或禁用编辑、删除按钮
                editButtons.forEach(btn => {
                    btn.style.display = 'none';
                });
                
                deleteButtons.forEach(btn => {
                    btn.style.display = 'none';
                });
                
                // 特别处理详情模态框中的按钮
                const handleDetailModalButtons = () => {
                    const modalEditBtn = document.getElementById('editBtn');
                    const modalDeleteBtn = document.getElementById('deleteBtn');
                    
                    if (modalEditBtn) {
                        // 对于模态框中的编辑按钮，保留显示但添加权限检查
                        modalEditBtn.style.display = 'inline-flex';
                        modalEditBtn.addEventListener('click', function(e) {
                            e.preventDefault();
                            e.stopPropagation();
                            showErrorMessage('权限不足，只有管理员可以编辑数据');
                            return false;
                        }, true);
                    }
                    
                    if (modalDeleteBtn) {
                        // 对于模态框中的删除按钮，保留显示但添加权限检查
                        modalDeleteBtn.style.display = 'inline-flex';
                        modalDeleteBtn.addEventListener('click', function(e) {
                            e.preventDefault();
                            e.stopPropagation();
                            showErrorMessage('权限不足，只有管理员可以删除数据');
                            return false;
                        }, true);
                    }
                };
                
                // 初始化时执行一次
                handleDetailModalButtons();
                
                // 每次模态框打开时也执行
                const ramDetailModal = document.getElementById('ramDetailModal');
                if (ramDetailModal) {
                    // 创建一个MutationObserver来监视模态框的显示状态
                    const observer = new MutationObserver(function(mutations) {
                        mutations.forEach(function(mutation) {
                            if (mutation.attributeName === 'class' && 
                                !ramDetailModal.classList.contains('hidden')) {
                                // 模态框显示了，处理按钮
                                handleDetailModalButtons();
                            }
                        });
                    });
                    
                    // 开始观察模态框
                    observer.observe(ramDetailModal, { attributes: true });
                }
                
                // 为所有删除操作添加权限验证拦截器
                interceptDeleteOperations();
            } else {
                // 添加管理员标识
                const header = document.querySelector('h1, h2');
                if (header) {
                    // 检查是否已经添加过徽章
                    if (!header.querySelector('.admin-badge')) {
                        const adminBadge = document.createElement('span');
                        adminBadge.className = 'bg-green-500 text-white text-xs px-2 py-1 rounded ml-2 admin-badge';
                        adminBadge.innerText = '管理员';
                        header.appendChild(adminBadge);
                    }
                }
            }
        });
    });
}

// 拦截所有删除操作的请求
function interceptDeleteOperations() {
    // 保存原始的fetch函数
    const originalFetch = window.fetch;
    
    // 重写fetch函数以拦截删除请求
    window.fetch = async function(url, options) {
        // 检查是否为删除操作
        if (options && options.method === 'DELETE') {
            console.log('拦截到删除操作请求:', url);
            
            // 检查用户权限
            const isAdminUser = await isAdmin();
            if (!isAdminUser) {
                console.warn('权限不足，普通用户无法执行删除操作');
                // 返回权限错误响应
                return Promise.resolve(new Response(JSON.stringify({
                    error: '权限不足',
                    message: '只有管理员可以删除数据'
                }), {
                    status: 403,
                    headers: {
                        'Content-Type': 'application/json'
                    }
                }));
            }
        }
        
        // 对于非删除请求或管理员的删除请求，正常执行
        return originalFetch.apply(this, arguments);
    };
    
    // 添加全局点击事件拦截
    document.addEventListener('click', async function(event) {
        // 查找可能触发删除操作的元素
        const deleteButton = event.target.closest('.btn-delete, [data-action="delete"], button[title="删除"]');
        if (deleteButton) {
            const isAdminUser = await isAdmin();
            if (!isAdminUser) {
                // 阻止事件传播
                event.preventDefault();
                event.stopPropagation();
                console.warn('权限不足，普通用户无法执行删除操作');
                
                // 显示提示消息
                if (typeof showToast === 'function') {
                    showToast('只有管理员可以删除数据', 'red');
                } else if (typeof showErrorMessage === 'function') {
                    showErrorMessage('权限不足，只有管理员可以删除数据');
                } else {
                    console.error('权限不足，只有管理员可以删除数据');
                }
                
                return false;
            }
        }
    }, true);
}

// 移除页面加载事件监听器，因为我们已经在init函数中调用了initPermissions
