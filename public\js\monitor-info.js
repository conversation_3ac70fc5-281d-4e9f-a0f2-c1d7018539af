document.addEventListener('DOMContentLoaded', function () {
    // DOM 元素
    const monitorForm = document.getElementById('monitorForm');
    const brand = document.getElementById('brand');
    const model = document.getElementById('model');
    const screen_size = document.getElementById('screen_size');
    const resolution = document.getElementById('resolution');
    const panel_type = document.getElementById('panel_type');
    const refresh_rate = document.getElementById('refresh_rate');
    const response_time = document.getElementById('response_time');
    const aspect_ratio = document.getElementById('aspect_ratio');
    const brightness = document.getElementById('brightness');
    const contrast_ratio = document.getElementById('contrast_ratio');
    const color_gamut = document.getElementById('color_gamut');
    const hdr_support = document.getElementById('hdr_support');
    const connectivity = document.getElementById('connectivity');
    const speakers = document.getElementById('speakers');
    const vesa_mount = document.getElementById('vesa_mount');
    const adjustable_stand = document.getElementById('adjustable_stand');
    const price = document.getElementById('price');
    const notes = document.getElementById('notes');
    const monitorImage = document.getElementById('monitorImage');
    const imagePreview = document.getElementById('imagePreview');
    const imagePreviewContainer = document.getElementById('imagePreviewContainer');
    const removeImageBtn = document.getElementById('removeImageBtn');
    const monitorTableBody = document.getElementById('monitorTableBody');
    const monitorSearch = document.getElementById('monitorSearch');
    const brandFilter = document.getElementById('brandFilter');
    const resetFilterBtn = document.getElementById('resetFilterBtn');
    const totalCount = document.getElementById('totalCount');
    const prevPage = document.getElementById('prevPage');
    const nextPage = document.getElementById('nextPage');
    const pageInfo = document.getElementById('pageInfo');
    const monitorDetailModal = document.getElementById('monitorDetailModal');
    const closeDetailModal = document.getElementById('closeDetailModal');
    const closeDetailModalBtn = document.getElementById('closeDetailModalBtn');
    const monitorDetailContent = document.getElementById('monitorDetailContent');
    const detailEditBtn = document.getElementById('detailEditBtn');
    const detailDeleteBtn = document.getElementById('detailDeleteBtn');
    const monitorIdInput = document.getElementById('monitorId');
    const submitBtn = document.getElementById('submitBtn');
    const submitBtnText = document.getElementById('submitBtnText');
    const smartParseInput = document.getElementById('smartParseInput');
    const parseButton = document.getElementById('parseButton');

    // 全局变量
    let currentPage = 1;
    const pageSize = 10;
    let totalRecords = 0;
    let monitors = [];
    let imageFile = null;
    let currentMonitorId = null;
    let isEditing = false;
    let isDarkMode = false;
    let allBrands = [];
    const maxPageButtons = 5;
    let activeViewer = null;

    const API_ENDPOINTS = {
        MONITORS: '/api/monitors',
        MONITOR: (id) => `/api/monitors/${id}`,
        BRANDS: '/api/monitors/brands'
    };

    const DEFAULT_MONITOR_IMAGE = '/images/default-monitor.png';

    // 初始化
    init();

    async function init() {
        const isAdminUser = await isAdmin();
        if (!isAdminUser) {
            if (monitorForm) {
                monitorForm.querySelectorAll('input, select, textarea').forEach(el => el.disabled = true);
            }
            if (parseButton) {
                parseButton.disabled = true;
                parseButton.title = '只有管理员可以操作';
            }
            if(submitBtn){
                submitBtn.disabled = true;
                submitBtn.title = '只有管理员可以操作';
            }
        }

        initDarkMode();
        setupEventListeners();
        loadBrandFilter();
        loadMonitors();
    }

    function initDarkMode() {
        isDarkMode = localStorage.getItem('darkMode') === 'true';
        document.body.classList.toggle('dark-mode', isDarkMode);
        updateDarkModeIcon(isDarkMode);
        imageFile = null;
    }

    function toggleDarkMode() {
        isDarkMode = !isDarkMode;
        localStorage.setItem('darkMode', isDarkMode);
        document.body.classList.toggle('dark-mode', isDarkMode);
        updateDarkModeIcon(isDarkMode);
        loadMonitors();
    }

    function updateDarkModeIcon(isDark) {
        const darkModeToggle = document.getElementById('darkModeToggle');
        if (!darkModeToggle) return;
        if (isDark) {
            darkModeToggle.innerHTML = '<i class="fas fa-sun"></i>';
            darkModeToggle.title = '切换至亮色模式';
        } else {
            darkModeToggle.innerHTML = '<i class="fas fa-moon"></i>';
            darkModeToggle.title = '切换至暗黑模式';
        }
    }

    function loadBrandFilter() {
        if (!brandFilter) return;

        fetchWithAuth(API_ENDPOINTS.BRANDS)
            .then(response => response.json())
            .then(data => {
                allBrands = data || [];
                allBrands.sort((a, b) => a.localeCompare(b, 'zh-CN'));
                brandFilter.innerHTML = '<option value="all">所有品牌</option>';
                allBrands.forEach(brandName => {
                    const option = document.createElement('option');
                    option.value = brandName;
                    option.textContent = brandName;
                    brandFilter.appendChild(option);
                });
            })
            .catch(error => {
                console.error('获取品牌列表失败:', error);
            });
    }

    function setupEventListeners() {
        const darkModeToggle = document.getElementById('darkModeToggle');
        if (darkModeToggle) {
            darkModeToggle.addEventListener('click', toggleDarkMode);
        }
        
        if (parseButton) {
            parseButton.addEventListener('click', handleSmartParse);
        }
        
        if (monitorImage) {
            monitorImage.addEventListener('change', handleImageUpload);
        }
        if (removeImageBtn) {
            removeImageBtn.addEventListener('click', removeImage);
        }

        if (monitorForm) {
            monitorForm.addEventListener('submit', handleFormSubmit);
        }

        if (monitorSearch) {
            monitorSearch.addEventListener('input', debounce(() => {
                currentPage = 1;
                loadMonitors();
            }, 300));
        }

        if (brandFilter) {
            brandFilter.addEventListener('change', () => {
                currentPage = 1;
                loadMonitors();
            });
        }

        if (resetFilterBtn) {
            resetFilterBtn.addEventListener('click', () => {
                if (monitorSearch) monitorSearch.value = '';
                if (brandFilter) brandFilter.value = 'all';
                currentPage = 1;
                loadMonitors();
                showSuccessMessage('已重置所有筛选条件');
            });
        }

        if (prevPage) {
            prevPage.addEventListener('click', () => changePage(currentPage - 1));
        }
        if (nextPage) {
            nextPage.addEventListener('click', () => changePage(currentPage + 1));
        }
        const firstPageBtn = document.getElementById('firstPage');
        if (firstPageBtn) {
            firstPageBtn.addEventListener('click', () => changePage(1));
        }
        const lastPageBtn = document.getElementById('lastPage');
        if (lastPageBtn) {
            lastPageBtn.addEventListener('click', () => {
                const totalPages = Math.ceil(totalRecords / pageSize) || 1;
                changePage(totalPages);
            });
        }

        const goToPageBtn = document.getElementById('goToPage');
        if (goToPageBtn) {
            goToPageBtn.addEventListener('click', () => {
                const pageJumpInput = document.getElementById('pageJump');
                if (pageJumpInput) {
                    let pageNum = parseInt(pageJumpInput.value);
                    const totalPages = Math.ceil(totalRecords / pageSize) || 1;
                    if (!isNaN(pageNum) && pageNum >= 1 && pageNum <= totalPages) {
                    changePage(pageNum);
                    }
                    pageJumpInput.value = '';
                }
            });
        }

        if (closeDetailModal) {
            closeDetailModal.addEventListener('click', () => monitorDetailModal.classList.add('hidden'));
        }
        if (closeDetailModalBtn) {
            closeDetailModalBtn.addEventListener('click', () => monitorDetailModal.classList.add('hidden'));
        }
        if (detailEditBtn) {
            detailEditBtn.addEventListener('click', () => {
                if (currentMonitorId) {
                    monitorDetailModal.classList.add('hidden');
                    editMonitor(currentMonitorId);
                }
            });
        }
        if (detailDeleteBtn) {
            detailDeleteBtn.addEventListener('click', () => {
                if (currentMonitorId) {
                    monitorDetailModal.classList.add('hidden');
                    confirmDeleteMonitor(currentMonitorId);
                }
            });
        }
        
        document.addEventListener('click', function (e) {
            if (e.target.classList.contains('js-preview-image')) {
                e.preventDefault();
                const src = e.target.getAttribute('data-src') || e.target.getAttribute('src');
                if (src) {
                    openImageFullscreen(src);
                }
            }
        });
    }

    function handleImageUpload(e) {
        const file = e.target.files[0];
        if (!file) return;
        imageFile = file;
        const reader = new FileReader();
        reader.onload = function (event) {
            if (imagePreview) {
                imagePreview.src = event.target.result;
                if (imagePreviewContainer) {
                    imagePreviewContainer.classList.remove('hidden');
                }

                // 添加点击预览功能
                imagePreview.onclick = () => {
                    openImageFullscreen(event.target.result);
                };
                imagePreview.style.cursor = 'pointer';
                imagePreview.title = '点击查看大图';
            }
        };
        reader.readAsDataURL(file);
    }

    function removeImage() {
        if (monitorImage) monitorImage.value = '';
        if (imagePreview) imagePreview.src = '';
        if (imagePreviewContainer) imagePreviewContainer.classList.add('hidden');
        imageFile = null;
    }

    async function handleFormSubmit(e) {
        e.preventDefault();

        const isAdminUser = await isAdmin();
        if (!isAdminUser) {
            return showErrorMessage('权限不足，无法提交。');
        }

        const formData = new FormData(monitorForm);
        
        if (imageFile) {
            formData.set('image', imageFile);
        } else if (!isEditing) {
            formData.delete('image');
        }

        const url = isEditing ? `${API_ENDPOINTS.MONITOR(currentMonitorId)}` : API_ENDPOINTS.MONITORS;
        const method = isEditing ? 'PUT' : 'POST';

        // 显示进度条并禁用提交按钮
        showUploadProgress();
        disableSubmitButton(true);

        // 使用XMLHttpRequest来支持上传进度
        uploadWithProgress(url, method, formData)
        .then(data => {
            showSuccessMessage(data.message || (isEditing ? '更新成功' : '添加成功'));
            resetForm();
            loadMonitors();
        })
        .catch(error => {
            showErrorMessage(error.message || '操作失败');
        })
        .finally(() => {
            // 隐藏进度条并恢复提交按钮
            hideUploadProgress();
            disableSubmitButton(false);
        });
    }

    function resetForm() {
        monitorForm.reset();
        removeImage();
        isEditing = false;
        if (monitorIdInput) monitorIdInput.value = '';
        if (submitBtnText) submitBtnText.textContent = '保存';
        submitBtn.classList.remove('bg-green-600', 'hover:bg-green-700');
        submitBtn.classList.add('highlight-bg');
    }

    function loadMonitors() {
        if (!monitorTableBody) return;
        const searchTerm = monitorSearch ? monitorSearch.value.trim() : '';
        const selectedBrand = brandFilter ? brandFilter.value : 'all';
        let url = `${API_ENDPOINTS.MONITORS}?page=${currentPage}&limit=${pageSize}`;
        if (selectedBrand !== 'all') {
            url += `&brand=${encodeURIComponent(selectedBrand)}`;
        }
        if (searchTerm) {
            url += `&search=${encodeURIComponent(searchTerm)}`;
        }
        
        showLoading(true);
        Promise.all([
            fetchWithAuth(url),
            isAdmin()
        ])
            .then(([response, isAdminUser]) => {
                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }
                return Promise.all([response.json(), isAdminUser]);
            })
            .then(([data, isAdminUser]) => {
                monitors = data.items || [];
                totalRecords = data.total || 0;
                renderMonitorTable(monitors, isAdminUser);
                updatePagination();
            })
            .catch(error => {
                console.error('加载显示器列表失败:', error);
                showErrorMessage('加载显示器列表失败');
                monitorTableBody.innerHTML = `<tr><td colspan="5" class="px-3 py-4 text-center text-red-500">加载失败</td></tr>`;
            })
            .finally(() => {
                showLoading(false);
            });
    }

    function renderMonitorTable(data, isAdminUser) {
        if (!monitorTableBody) return;
        monitorTableBody.innerHTML = '';
        if (!data.length) {
            monitorTableBody.innerHTML = `<tr><td colspan="5" class="px-3 py-4 text-center text-gray-500">暂无数据</td></tr>`;
            if (totalCount) totalCount.textContent = `共 0 条记录`;
            return;
        }

        // 检测移动端
        const isMobile = window.innerWidth < 640;

        if (isMobile) {
            // 移动端卡片式布局
            renderMobileCards(data, isAdminUser);
        } else {
            // PC端表格布局
            renderDesktopTable(data, isAdminUser);
        }
        
        if (totalCount) totalCount.textContent = `共 ${totalRecords} 条记录`;
    }

    // 渲染移动端卡片式布局
    function renderMobileCards(data, isAdminUser) {
        // 获取表格和其父容器
        const tableElement = monitorTableBody.closest('table');
        const tableParent = tableElement ? tableElement.parentNode : null;
        
        // 确保我们能找到表格的父元素
        if (!tableParent) {
            console.error('无法找到表格父元素');
            return;
        }
        
        // 完全隐藏原始表格
        if (tableElement) {
            tableElement.style.display = 'none';
        }
        
        // 移除任何旧的显示器卡片容器
        const oldContainer = document.getElementById('mobile-monitor-cards');
        if (oldContainer) {
            oldContainer.parentNode.removeChild(oldContainer);
        }
        
        // 创建新的显示器卡片容器
        const cardsContainer = document.createElement('div');
        cardsContainer.id = 'mobile-monitor-cards';
        cardsContainer.className = 'w-full';
        cardsContainer.style.display = 'flex';
        cardsContainer.style.flexDirection = 'column';
        cardsContainer.style.gap = '8px';
        cardsContainer.style.padding = '0';
        cardsContainer.style.marginBottom = '10px';
        
        // 在表格前插入卡片容器
        tableParent.insertBefore(cardsContainer, tableElement);
        
        // 如果没有数据，显示"无数据"消息
        if (!data || data.length === 0) {
            const noDataMessage = document.createElement('div');
            noDataMessage.className = 'text-center py-6 text-gray-500';
            noDataMessage.textContent = '暂无显示器数据';
            cardsContainer.appendChild(noDataMessage);
            return;
        }
        
        // 为每个显示器创建卡片
        data.forEach((monitor, index) => {
            // 创建卡片外层容器
            const card = document.createElement('div');
            
            // 根据暗黑模式设置不同的卡片样式
            const cardBg = document.body.classList.contains('dark-mode') 
                ? 'bg-gray-800' 
                : 'bg-white';
                
            const cardBorder = document.body.classList.contains('dark-mode')
                ? 'border border-gray-700'
                : 'border border-gray-200';
                
            const cardShadow = document.body.classList.contains('dark-mode')
                ? 'shadow-md'
                : 'shadow-sm';
                
            card.className = `${cardBg} ${cardBorder} rounded-sm ${cardShadow} overflow-hidden monitor-card`;
            card.style.animation = `fadeIn 0.3s ease-in-out ${index * 0.05}s both`;
            
            // 卡片标题行 - 品牌与型号
            const header = document.createElement('div');
            
            const borderClass = document.body.classList.contains('dark-mode') 
                ? 'border-b border-gray-700' 
                : 'border-b border-gray-100';
                
            const bgClass = document.body.classList.contains('dark-mode')
                ? 'bg-gray-800'
                : 'bg-white';
                
            header.className = `flex justify-between items-center ${borderClass} ${bgClass} monitor-card-header`;
            header.style.padding = '8px 10px';
            
            // 显示器型号
            const modelName = document.createElement('div');
            
            const textColor = document.body.classList.contains('dark-mode') 
                ? 'text-white' 
                : 'text-gray-900';
                
            modelName.className = `${textColor} font-medium`;
            modelName.style.fontSize = '0.92rem';
            
            let displayName = monitor.model || '未知型号';
            if (displayName.length > 30) {
                displayName = displayName.substring(0, 28) + '...';
            }
            modelName.textContent = displayName;
            modelName.title = monitor.model || '';
            
            // 面板类型标签
            const panelBadge = document.createElement('span');
            
            const badgeClass = 'bg-yellow-600';
            const badgeText = monitor.panel_type || '未知面板';
            
            panelBadge.className = `${badgeClass} text-white text-xs font-medium rounded px-2 py-0.5`;
            panelBadge.style.fontSize = '0.7rem';
            panelBadge.textContent = badgeText;
            
            header.appendChild(modelName);
            header.appendChild(panelBadge);
            
            // 卡片内容区域
            const content = document.createElement('div');
            
            const contentBg = document.body.classList.contains('dark-mode')
                ? 'bg-gray-800' 
                : 'bg-white';
                
            content.className = `flex ${contentBg} monitor-card-body`;
            content.style.padding = '8px 10px';
            
            // 左侧图片
            const leftCol = document.createElement('div');
            leftCol.className = 'mr-3 flex-shrink-0 monitor-image-container';
            
            const imageSize = '45px';
            
            if (monitor.image_url) {
                const imgContainer = document.createElement('div');
                imgContainer.className = 'relative';
                imgContainer.style.width = imageSize;
                imgContainer.style.height = imageSize;
                
                const img = document.createElement('img');
                img.src = monitor.image_url || DEFAULT_MONITOR_IMAGE;
                img.alt = monitor.model || 'Monitor';
                img.className = 'rounded-md object-cover cursor-pointer js-preview-image';
                img.dataset.src = monitor.image_url || DEFAULT_MONITOR_IMAGE;
                img.style.width = imageSize;
                img.style.height = imageSize;
                
                // 添加图片悬停放大效果
                img.classList.add('monitor-img-hover');
                
                imgContainer.appendChild(img);
                leftCol.appendChild(imgContainer);
            } else {
                // 使用默认图片
                const imgContainer = document.createElement('div');
                imgContainer.className = 'relative';
                imgContainer.style.width = imageSize;
                imgContainer.style.height = imageSize;
                
                const img = document.createElement('img');
                img.src = DEFAULT_MONITOR_IMAGE;
                img.alt = monitor.model || 'Monitor';
                img.className = 'rounded-md object-cover cursor-pointer js-preview-image';
                img.dataset.src = DEFAULT_MONITOR_IMAGE;
                img.style.width = imageSize;
                img.style.height = imageSize;
                
                // 添加默认图片标记
                const defaultBadge = document.createElement('div');
                defaultBadge.className = 'absolute bottom-0 right-0 bg-gray-700 text-white text-xs px-0.5 rounded-tl-md';
                defaultBadge.style.fontSize = '0.6rem';
                defaultBadge.textContent = '默认';
                
                imgContainer.appendChild(img);
                imgContainer.appendChild(defaultBadge);
                leftCol.appendChild(imgContainer);
            }
            
            // 右侧信息列表
            const rightCol = document.createElement('div');
            rightCol.className = 'flex-1 min-w-0';
            
            // 品牌信息
            if (monitor.brand) {
                const brandText = document.createElement('div');
                brandText.className = 'font-medium text-red-600';
                brandText.style.fontSize = '0.9rem';
                brandText.style.marginBottom = '6px';
                brandText.textContent = monitor.brand;
                rightCol.appendChild(brandText);
            }
            
            // 规格信息区域
            const specsList = document.createElement('div');
            
            const specLabelColor = document.body.classList.contains('dark-mode')
                ? 'text-gray-400'
                : 'text-gray-500';
                
            const specValueColor = document.body.classList.contains('dark-mode')
                ? 'text-gray-200'
                : 'text-gray-700';
                
            specsList.className = `${specLabelColor}`;
            specsList.style.fontSize = '0.85rem';
            
            // 尺寸信息
            if (monitor.screen_size) {
                const sizeRow = document.createElement('div');
                sizeRow.className = 'mb-2 flex';
                
                const sizeLabel = document.createElement('span');
                sizeLabel.className = 'w-10 inline-block';
                sizeLabel.textContent = '尺寸:';
                
                const sizeValue = document.createElement('span');
                sizeValue.className = `font-medium ${specValueColor}`;
                sizeValue.style.fontSize = '0.88rem';
                sizeValue.textContent = `${monitor.screen_size}"`;
                
                sizeRow.appendChild(sizeLabel);
                sizeRow.appendChild(sizeValue);
                specsList.appendChild(sizeRow);
            }
            
            // 分辨率信息
            if (monitor.resolution) {
                const resRow = document.createElement('div');
                resRow.className = 'mb-2 flex';
                
                const resLabel = document.createElement('span');
                resLabel.className = 'w-10 inline-block';
                resLabel.textContent = '分辨率:';
                
                const resValue = document.createElement('span');
                resValue.className = `font-medium ${specValueColor}`;
                resValue.style.fontSize = '0.88rem';
                resValue.textContent = monitor.resolution;
                
                resRow.appendChild(resLabel);
                resRow.appendChild(resValue);
                specsList.appendChild(resRow);
            }
            
            // 刷新率信息
            if (monitor.refresh_rate) {
                const rateRow = document.createElement('div');
                rateRow.className = 'mb-2 flex';
                
                const rateLabel = document.createElement('span');
                rateLabel.className = 'w-10 inline-block';
                rateLabel.textContent = '刷新率:';
                
                const rateValue = document.createElement('span');
                rateValue.className = `font-medium ${specValueColor}`;
                rateValue.style.fontSize = '0.88rem';
                rateValue.textContent = `${monitor.refresh_rate}Hz`;
                
                rateRow.appendChild(rateLabel);
                rateRow.appendChild(rateValue);
                specsList.appendChild(rateRow);
            }
            
            // 标签行
            const tagsRow = document.createElement('div');
            tagsRow.className = 'flex flex-wrap gap-2 mt-3';
            
            // 添加标签函数
            const addTag = (text, bgClass, textClass) => {
                if (!text) return;
                
                const tag = document.createElement('span');
                
                if (document.body.classList.contains('dark-mode')) {
                    tag.className = `bg-gray-700 text-white px-2 py-1 rounded-sm border border-gray-600`;
                } else {
                    tag.className = `${bgClass} ${textClass} px-2 py-1 rounded-sm border`;
                    tag.style.borderColor = 'rgba(0,0,0,0.05)';
                }
                
                tag.style.fontSize = '0.8rem';
                tag.textContent = text;
                tagsRow.appendChild(tag);
            };
            
            // 添加尺寸标签
            if (monitor.screen_size) {
                addTag(`${monitor.screen_size}"`, 'bg-blue-50', 'text-blue-700');
            }
            
            // 添加面板标签
            if (monitor.panel_type) {
                addTag(monitor.panel_type, 'bg-yellow-50', 'text-yellow-700');
            }
            
            // 添加刷新率标签
            if (monitor.refresh_rate) {
                addTag(`${monitor.refresh_rate}Hz`, 'bg-red-50', 'text-red-700');
            }
            
            // 添加HDR标签
            if (monitor.hdr_support) {
                addTag(monitor.hdr_support, 'bg-purple-50', 'text-purple-700');
            }
            
            // 组装右侧列
            rightCol.appendChild(specsList);
            rightCol.appendChild(tagsRow);
            
            // 组装内容区域
            content.appendChild(leftCol);
            content.appendChild(rightCol);
            
            // 底部操作栏
            const footer = document.createElement('div');
            
            const footerBorder = document.body.classList.contains('dark-mode') 
                ? 'border-t border-gray-700' 
                : 'border-t border-gray-200';
                
            const footerBg = document.body.classList.contains('dark-mode')
                ? 'bg-gray-900'
                : 'bg-gray-50';
                
            footer.className = `${footerBorder} ${footerBg} p-2 flex justify-between items-center monitor-card-footer`;
            
            // 按钮函数
            const createButton = (icon, text, color, onClick) => {
                const btn = document.createElement('button');
                
                let buttonColor = color;
                if (document.body.classList.contains('dark-mode')) {
                    if (color === 'text-blue-600') buttonColor = 'text-blue-400';
                    if (color === 'text-green-600') buttonColor = 'text-green-400';
                    if (color === 'text-red-600') buttonColor = 'text-red-400';
                }
                
                btn.className = `${buttonColor} flex items-center justify-center px-3 py-1`;
                btn.style.fontSize = '0.8rem';
                btn.innerHTML = `<i class="fas ${icon} mr-1"></i>${text}`;
                btn.onclick = onClick;
                return btn;
            };
            
            // 添加操作按钮
            const viewBtn = createButton('fa-eye', '查看', 'text-blue-600', () => viewMonitorDetails(monitor.id));
            const editBtn = createButton('fa-edit', '编辑', 'text-green-600', () => editMonitor(monitor.id));
            const deleteBtn = createButton('fa-trash', '删除', 'text-red-600', () => confirmDeleteMonitor(monitor.id));
            
            footer.appendChild(viewBtn);
            if (isAdminUser) {
                footer.appendChild(editBtn);
                footer.appendChild(deleteBtn);
            }
            
            // 组装卡片
            card.appendChild(header);
            card.appendChild(content);
            card.appendChild(footer);
            
            // 添加到容器
            cardsContainer.appendChild(card);
        });
    }

    // 渲染PC端表格布局
    function renderDesktopTable(data, isAdminUser) {
        data.forEach((monitor, index) => {
            const row = document.createElement('tr');
            row.className = 'hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors';
            // 设置淡入效果
            row.style.animation = 'fadeIn 0.3s ease-in-out';
            row.style.animationDelay = `${index * 0.05}s`;
            row.style.animationFillMode = 'both';
            
            const imageUrl = monitor.image_url || DEFAULT_MONITOR_IMAGE;
            
            let actionButtonsHTML = `
                <button class="btn-view" title="查看详情"><i class="fas fa-eye"></i></button>
            `;
            if (isAdminUser) {
                actionButtonsHTML += `
                    <button class="btn-edit" title="编辑"><i class="fas fa-edit"></i></button>
                    <button class="btn-delete" title="删除"><i class="fas fa-trash"></i></button>
                `;
            }

            row.innerHTML = `
                <td class="px-3 py-3">
                    <div class="relative group monitor-image-container w-10 h-10 mx-auto">
                        <img src="${imageUrl}" alt="${monitor.model}" class="h-10 w-10 rounded-md object-cover cursor-pointer transition-transform group-hover:scale-110 js-preview-image" data-src="${imageUrl}">
                        <div class="img-overlay rounded-md absolute inset-0 flex items-center justify-center bg-black bg-opacity-20 opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none">
                            <i class="fas fa-search-plus text-white text-opacity-80"></i>
                        </div>
                    </div>
                </td>
                <td class="px-3 py-3">
                    <div class="text-sm font-medium text-gray-900 dark:text-white">${monitor.brand || '-'}</div>
                </td>
                <td class="px-3 py-3">
                    <div class="text-sm text-gray-800 dark:text-gray-300">${monitor.model || '-'}</div>
                </td>
                <td class="px-3 py-3">
                    <div class="text-sm">${monitor.screen_size ? `${monitor.screen_size}''` : '-'}</div>
                </td>
                <td class="px-3 py-3">
                    <div class="text-sm text-gray-800 dark:text-gray-300">${monitor.resolution || '-'}</div>
                </td>
                <td class="px-3 py-3 hidden md:table-cell">
                    <span class="panel-tag">${monitor.panel_type || '-'}</span>
                </td>
                <td class="px-3 py-3">
                    <span class="refresh-rate-tag badge">${monitor.refresh_rate ? `${monitor.refresh_rate}Hz` : '-'}</span>
                </td>
                <td class="px-3 py-3 text-center">
                    <div class="action-buttons">
                        ${actionButtonsHTML}
                    </div>
                </td>
            `;
            
            row.querySelector('.btn-view').addEventListener('click', () => viewMonitorDetails(monitor.id));
            if (isAdminUser) {
                row.querySelector('.btn-edit').addEventListener('click', () => editMonitor(monitor.id));
                row.querySelector('.btn-delete').addEventListener('click', () => confirmDeleteMonitor(monitor.id));
            }
            
            monitorTableBody.appendChild(row);
        });

        // 添加窗口大小变化处理
        window.addEventListener('resize', debounce(function() {
            if (window.innerWidth >= 641) { // 在PC模式下
                // 显示表格，移除移动端卡片
                const tableElement = monitorTableBody.closest('table');
                if (tableElement) tableElement.style.display = '';
                
                const mobileCards = document.getElementById('mobile-monitor-cards');
                if (mobileCards) mobileCards.style.display = 'none';
            } else { // 在移动模式下
                // 隐藏表格，显示移动端卡片
                const tableElement = monitorTableBody.closest('table');
                if (tableElement) tableElement.style.display = 'none';
                
                const mobileCards = document.getElementById('mobile-monitor-cards');
                if (mobileCards) mobileCards.style.display = 'flex';
                else {
                    // 如果卡片不存在，重新加载数据
                    loadMonitors();
                }
            }
        }, 250));
    }

    function updatePagination() {
        const currentPageDisplay = document.getElementById('currentPageDisplay');
        const totalPagesDisplay = document.getElementById('totalPagesDisplay');
        const pageNumbers = document.getElementById('pageNumbers');
        const firstPageBtn = document.getElementById('firstPage');
        const prevPageBtn = document.getElementById('prevPage');
        const nextPageBtn = document.getElementById('nextPage');
        const lastPageBtn = document.getElementById('lastPage');
        if (!currentPageDisplay || !totalPagesDisplay || !pageNumbers) return;
        
        const totalPages = Math.ceil(totalRecords / pageSize) || 1;
        currentPageDisplay.textContent = currentPage;
        totalPagesDisplay.textContent = totalPages;
        
        if (prevPageBtn) prevPageBtn.disabled = currentPage <= 1;
        if (nextPageBtn) nextPageBtn.disabled = currentPage >= totalPages;
        if (firstPageBtn) firstPageBtn.disabled = currentPage <= 1;
        if (lastPageBtn) lastPageBtn.disabled = currentPage >= totalPages;

            pageNumbers.innerHTML = '';
            let startPage = Math.max(1, currentPage - Math.floor(maxPageButtons / 2));
            let endPage = startPage + maxPageButtons - 1;
            if (endPage > totalPages) {
                endPage = totalPages;
                startPage = Math.max(1, endPage - maxPageButtons + 1);
            }
            for (let i = startPage; i <= endPage; i++) {
                const pageButton = document.createElement('button');
                pageButton.textContent = i;
            pageButton.className = `px-3 py-1 border rounded-md text-sm ${i === currentPage ? 'bg-pink-600 text-white border-pink-600' : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'}`;
                pageButton.addEventListener('click', () => changePage(i));
                pageNumbers.appendChild(pageButton);
        }
    }

    function changePage(newPage) {
        const totalPages = Math.ceil(totalRecords / pageSize) || 1;
        if (newPage >= 1 && newPage <= totalPages) {
        currentPage = newPage;
            loadMonitors();
        }
    }

    async function viewMonitorDetails(id) {
        try {
            const [monitorResponse, isAdminUser] = await Promise.all([
                fetchWithAuth(API_ENDPOINTS.MONITOR(id)),
                isAdmin()
            ]);

            if (!monitorResponse.ok) {
                throw new Error('获取显示器详情失败');
            }

            const monitor = await monitorResponse.json();
            currentMonitorId = monitor.id;
            renderMonitorDetails(monitor);

            detailEditBtn.style.display = isAdminUser ? 'inline-flex' : 'none';
            detailDeleteBtn.style.display = isAdminUser ? 'inline-flex' : 'none';

            monitorDetailModal.classList.remove('hidden');
        } catch (error) {
            showErrorMessage('获取显示器详情失败');
            console.error('获取显示器详情失败:', error);
        }
    }

    function renderMonitorDetails(monitor) {
        if (!monitor) return;
        const imageUrl = monitor.image_url || DEFAULT_MONITOR_IMAGE;
        const isDefaultImage = !monitor.image_url;

        const details = `
            <!-- 标题和价格区域 -->
            <div class="flex justify-between items-center border-b border-gray-200 dark:border-gray-700 pb-2 mb-3">
                <div>
                    <div class="text-lg font-bold text-gray-800 dark:text-gray-100">${monitor.brand || '-'}</div>
                    <div class="text-sm text-gray-600 dark:text-gray-400">${monitor.model || '未知型号'}</div>
                </div>
                <div class="text-lg font-bold text-red-600">¥${monitor.price || '0'}</div>
            </div>
            
            <!-- 主要规格展示 - 紧凑布局 -->
            <div class="grid grid-cols-2 gap-3 mb-3">
                <!-- 图片占左侧 -->
                <div class="bg-gray-50 dark:bg-gray-800 rounded-lg flex items-center justify-center relative">
                    <img src="${imageUrl}" alt="${monitor.model}" class="w-full h-auto rounded-lg js-preview-image" 
                        style="max-height: 160px; object-fit: contain;" data-src="${imageUrl}">
                    ${isDefaultImage ? `<div class="absolute bottom-1 right-1 bg-gray-700 text-white text-xs px-1 py-0.5 rounded opacity-80">默认</div>` : ''}
                </div>
                
                <!-- 核心参数占右侧 -->
                <div class="grid grid-cols-1 gap-2">
                    <!-- 尺寸规格 -->
                    <div class="bg-blue-50 dark:bg-blue-900/20 p-2 rounded-lg">
                        <h4 class="text-xs font-medium text-blue-700 dark:text-blue-400">屏幕尺寸</h4>
                        <div class="flex justify-between items-center">
                            <p class="text-lg font-bold text-blue-700 dark:text-blue-400">${monitor.screen_size ? `${monitor.screen_size}"` : '-'}</p>
                            <p class="text-xs text-gray-600 dark:text-gray-400">${monitor.aspect_ratio || '-'}</p>
                        </div>
                        <p class="text-xs text-gray-600 dark:text-gray-400">${monitor.resolution || '-'}</p>
                    </div>
                    
                    <!-- 刷新率 -->
                    <div class="bg-red-50 dark:bg-red-900/20 p-2 rounded-lg">
                        <h4 class="text-xs font-medium text-red-700 dark:text-red-400">刷新率</h4>
                        <div class="flex justify-between items-center">
                            <p class="text-lg font-bold text-red-700 dark:text-red-400">${monitor.refresh_rate ? `${monitor.refresh_rate}Hz` : '-'}</p>
                            <p class="text-xs text-gray-600 dark:text-gray-400">${monitor.response_time ? `${monitor.response_time}ms` : '-'}</p>
                        </div>
                        <p class="text-xs text-gray-600 dark:text-gray-400">${monitor.panel_type || '未知面板'}</p>
                    </div>
                </div>
            </div>
            
            <!-- 参数表格式布局 -->
            <div class="grid grid-cols-2 lg:grid-cols-3 gap-x-4 gap-y-1 mb-2 text-sm">
                <div class="flex justify-between py-1 border-b border-gray-100 dark:border-gray-700">
                    <span class="text-xs text-gray-500 dark:text-gray-400">亮度:</span>
                    <span class="font-medium">${monitor.brightness ? `${monitor.brightness} cd/m²` : '-'}</span>
                </div>
                <div class="flex justify-between py-1 border-b border-gray-100 dark:border-gray-700">
                    <span class="text-xs text-gray-500 dark:text-gray-400">对比度:</span>
                    <span class="font-medium">${monitor.contrast_ratio || '-'}</span>
                </div>
                <div class="flex justify-between py-1 border-b border-gray-100 dark:border-gray-700">
                    <span class="text-xs text-gray-500 dark:text-gray-400">色域:</span>
                    <span class="font-medium">${monitor.color_gamut || '-'}</span>
                </div>
                <div class="flex justify-between py-1 border-b border-gray-100 dark:border-gray-700">
                    <span class="text-xs text-gray-500 dark:text-gray-400">HDR支持:</span>
                    <span class="font-medium">${monitor.hdr_support || '-'}</span>
                </div>
                <div class="flex justify-between py-1 border-b border-gray-100 dark:border-gray-700">
                    <span class="text-xs text-gray-500 dark:text-gray-400">接口:</span>
                    <span class="font-medium">${monitor.connectivity || '-'}</span>
                </div>
                <div class="flex justify-between py-1 border-b border-gray-100 dark:border-gray-700">
                    <span class="text-xs text-gray-500 dark:text-gray-400">扬声器:</span>
                    <span class="font-medium">${monitor.speakers ? '是' : '否'}</span>
                </div>
                <div class="flex justify-between py-1 border-b border-gray-100 dark:border-gray-700">
                    <span class="text-xs text-gray-500 dark:text-gray-400">支架调节:</span>
                    <span class="font-medium">${monitor.adjustable_stand || '-'}</span>
                </div>
                <div class="flex justify-between py-1 border-b border-gray-100 dark:border-gray-700">
                    <span class="text-xs text-gray-500 dark:text-gray-400">VESA挂载:</span>
                    <span class="font-medium">${monitor.vesa_mount || '-'}</span>
                </div>
            </div>
            
            <!-- 备注区域 - 如果有的话 -->
            ${monitor.notes ? `
            <div class="mt-2 pt-1">
                <h4 class="text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">备注:</h4>
                <p class="text-xs text-gray-600 dark:text-gray-400">${monitor.notes}</p>
            </div>
            ` : ''}
        `;
        monitorDetailContent.innerHTML = details;
    }

    async function editMonitor(id) {
        const isAdminUser = await isAdmin();
        if (!isAdminUser) {
            return showErrorMessage('权限不足，无法编辑。');
        }

        fetchWithAuth(API_ENDPOINTS.MONITOR(id))
            .then(response => response.json())
            .then(monitor => {
                isEditing = true;
                currentMonitorId = monitor.id;
                
                // Populate all form fields from the monitor object
                for(const key in monitor) {
                    if(monitorForm.elements[key]) {
                        const element = monitorForm.elements[key];
                        if (element.type === 'radio' || element.type === 'checkbox') {
                            // This logic might be needed for checkbox/radio groups
                            // For now, we assume direct value setting works
                        } else {
                            element.value = monitor[key];
                        }
                    }
                }
                
                if (monitor.image_url) {
                    imagePreview.src = monitor.image_url;
                    imagePreviewContainer.classList.remove('hidden');

                    // 添加点击预览功能
                    imagePreview.onclick = () => {
                        openImageFullscreen(monitor.image_url);
                    };
                    imagePreview.style.cursor = 'pointer';
                    imagePreview.title = '点击查看大图';
                } else {
                    removeImage();
                }

                const submitBtn = monitorForm.querySelector('button[type="submit"]');
                submitBtn.innerHTML = '<i class="fas fa-save mr-1"></i> 更新显示器信息';
                submitBtn.classList.remove('highlight-bg');
                submitBtn.classList.add('bg-green-600', 'hover:bg-green-700');
                
                monitorDetailModal.classList.add('hidden');
                window.scrollTo({ top: 0, behavior: 'smooth' });
            })
            .catch(error => showErrorMessage('获取显示器信息失败'));
    }

    async function confirmDeleteMonitor(id) {
        const isAdminUser = await isAdmin();
        if (!isAdminUser) {
            return showErrorMessage('权限不足，无法删除。');
        }

        if (confirm('确定要删除这个显示器吗？此操作不可撤销。')) {
            deleteMonitor(id);
        }
    }

    async function deleteMonitor(id) {
        const isAdminUser = await isAdmin();
        if (!isAdminUser) {
            return showErrorMessage('权限不足，无法删除。');
        }

        fetchWithAuth(API_ENDPOINTS.MONITOR(id), { method: 'DELETE' })
            .then(response => {
                if (!response || !response.ok) {
                    // For failed responses, we assume a JSON body with an error message.
                    return response.json().then(data => {
                        throw new Error(data.message || '删除失败');
                    });
                }
                // For successful responses (like 200 OK or 204 No Content),
                // we don't need to parse a body, as it might be empty.
                // The request being `ok` is enough to proceed.
                return Promise.resolve();
            })
            .then(() => {
                showSuccessMessage('删除成功');
                loadMonitors();
            })
            .catch(error => {
                console.error('删除出错:', error);
                showErrorMessage(error.message || '删除失败');
            });
    }

    function showLoading(show) {
        const loadingEl = document.getElementById('loadingIndicator');
        if (loadingEl) {
            loadingEl.classList.toggle('hidden', !show);
        }
    }

    function showSuccessMessage(message) {
        showMessage(message, 'green');
    }

    function showErrorMessage(message) {
        showMessage(message, 'red');
    }

    function showMessage(message, color) {
        const alertElement = document.createElement('div');
        alertElement.className = `fixed top-4 right-4 bg-${color}-50 border-l-4 border-${color}-500 p-4 opacity-0 transition-opacity duration-300 shadow-md fade-in`;
        alertElement.innerHTML = `
            <div class="flex items-center">
                <div class="flex-shrink-0 text-${color}-500">
                    <i class="fas fa-${color === 'green' ? 'check' : 'exclamation'}-circle"></i>
                </div>
                <div class="ml-3">
                    <p class="text-sm text-${color}-700">${message}</p>
                </div>
            </div>
        `;
        document.body.appendChild(alertElement);
        setTimeout(() => alertElement.classList.add('opacity-100'), 10);
        setTimeout(() => {
            alertElement.classList.remove('opacity-100');
            setTimeout(() => document.body.removeChild(alertElement), 300);
        }, 3000);
    }

    function debounce(func, delay) {
        let timeout;
        return function () {
            const context = this;
            const args = arguments;
            clearTimeout(timeout);
            timeout = setTimeout(() => func.apply(context, args), delay);
        };
    }
    
function openImageFullscreen(src) {
        if (activeViewer) {
                activeViewer.destroy();
            }
        const container = document.createElement('div');
        const img = document.createElement('img');
        img.src = src;
        container.appendChild(img);
        activeViewer = new Viewer(container, {
            hidden() {
                activeViewer.destroy();
                activeViewer = null;
            },
        });
        activeViewer.show();
    }

    // 获取各组件数量的函数
    async function fetchComponentCounts() {
        try {
            const response = await fetchWithAuth('/api/components/stats');
            if (!response || !response.ok) {
                throw new Error('获取组件统计失败');
            }
            const data = await response.json();
            
            // 添加调试信息
            console.log("API返回数据:", data);
            console.log("显示器数量:", data.monitorCount);
            
            // 更新统计数字
            document.getElementById('cpu-count').textContent = data.cpuCount || 0;
            document.getElementById('ram-count').textContent = data.ramCount || 0;
            document.getElementById('storage-count').textContent = data.storageCount || 0;
            document.getElementById('gpu-count').textContent = data.gpuCount || 0;
            document.getElementById('cooler-count').textContent = data.coolerCount || 0;
            document.getElementById('fan-count').textContent = data.fanCount || 0;
            document.getElementById('psu-count').textContent = data.psuCount || 0;
            document.getElementById('case-count').textContent = data.caseCount || 0;
            document.getElementById('motherboard-count').textContent = data.motherboardCount || 0;
            document.getElementById('monitor-count').textContent = data.monitorCount || 0;
        } catch (error) {
            console.error('获取组件统计失败:', error);
            // 在出错时显示提示
            const statsContainer = document.getElementById('stats-container');
            statsContainer.innerHTML = `
                <div class="col-span-3 bg-red-100 p-4 rounded-md">
                    <p class="text-red-700">获取组件统计信息失败，请稍后再试</p>
                </div>
            `;
        }
    }

    // 调用函数获取数据
    fetchComponentCounts();

    function handleSmartParse() {
        const text = smartParseInput.value.trim();
        if (!text) {
            showErrorMessage('请先粘贴内容');
            return;
        }

        const data = parseMonitorText(text);

        // Populate form fields
        for (const key in data) {
            if (monitorForm.elements[key] && data[key]) {
                const element = monitorForm.elements[key];
                element.value = data[key];
            }
        }
        
        showSuccessMessage('智能解析完成，请检查并补全信息');
    }

    function parseMonitorText(originalText) {
        const data = {};
        
        let text = originalText.toUpperCase().replace(/\s+/g, ' ');
        text = text.replace(/（/g, '(').replace(/）/g, ')'); // Normalize parentheses

        // Screen Size (e.g., 27", 27寸, 27英寸)
        let match = text.match(/(\d{2}(\.\d)?)\s?("|\u540b\u5bf8|英寸|INCH)/);
        if (match) data.screen_size = parseFloat(match[1]);

        // Resolution (e.g., 3840x2160, 2560*1440, 4K, 2K)
        match = text.match(/(\d{3,4})\s?[*X×]\s?(\d{3,4})/);
        if (match) {
            data.resolution = `${match[1]}x${match[2]}`;
        } else if (text.includes('4K')) {
            data.resolution = '3840x2160';
        } else if (text.includes('2K') || text.includes('QHD')) {
            data.resolution = '2560x1440';
        } else if (text.includes('1080P') || text.includes('FHD')) {
            data.resolution = '1920x1080';
        }

        // Refresh Rate (e.g., 144Hz, 60HZ)
        match = text.match(/(\d{2,4})\s?HZ/);
        if (match) data.refresh_rate = parseInt(match[1], 10);

        // Panel Type (IPS, VA, TN, OLED, Mini-LED)
        if (text.includes('IPS')) data.panel_type = 'IPS';
        else if (text.includes('VA')) data.panel_type = 'VA';
        else if (text.includes('TN')) data.panel_type = 'TN';
        else if (text.includes('OLED')) data.panel_type = 'OLED';
        else if (text.includes('MINI-LED') || text.includes('MINILED')) data.panel_type = 'Mini-LED';

        // Response Time (e.g., 1ms, 0.5ms GTG)
        match = text.match(/(\d(\.\d)?)\s?MS/);
        if (match) data.response_time = parseFloat(match[1]);

        // Brightness (e.g., 400nits, 350cd/m2)
        match = text.match(/(\d{3,4})\s?(NITS|CD)/);
        if (match) data.brightness = parseInt(match[1], 10);
        
        // HDR (e.g., HDR400, HDR10, DisplayHDR 600)
        match = text.match(/(HDR\s?\d{3,4}|DISPLAYHDR\s?\d{3,4}|HDR10\+?)/);
        if (match) data.hdr_support = match[0].replace('DISPLAY', '').trim();

        // Color Gamut (e.g., 99% sRGB, 98% DCI-P3)
        match = text.match(/(\d{2,3}%\s?(\w+-?\w+|\w+))/);
        if (match) data.color_gamut = match[0];
        
        // VESA mount (100x100, 75*75)
        match = text.match(/(\d{2,3}[xX×]\d{2,3})/);
        if(match) data.vesa_mount = match[1].toLowerCase();

        // Price (e.g., ¥2999, ￥1999)
        match = originalText.match(/[¥￥]\s?(\d+(\.\d{1,2})?)/);
        if (match) data.price = parseFloat(match[1]);

        // Speakers (内置音箱, 带音箱, 自带喇叭)
        if (text.includes('SPEAKER') || originalText.includes('音箱') || originalText.includes('喇叭')) {
            data.speakers = '1';
        }

        // Brand and Model
        const brands = ["DELL", "AOC", "LG", "SAMSUNG", "ASUS", "BENQ", "ACER", "PHILIPS", "HP", "LENOVO", "HUAWEI", "XIAOMI", "HKC", "TITAN ARMY", "KOORUI", "INNOCN", "REDMI", "GIGABYTE", "MSI", "RAZER", "VIEWSONIC"];
        let foundBrand = '';
        for (const brand of brands) {
            if (text.startsWith(brand) || text.includes(` ${brand} `)) {
                data.brand = brand.charAt(0) + brand.slice(1).toLowerCase();
                foundBrand = brand;
                break;
            }
        }
        
        let modelText = originalText;
        if (foundBrand) {
             const brandIndex = modelText.toUpperCase().indexOf(foundBrand);
             if (brandIndex !== -1) {
                 modelText = modelText.substring(brandIndex + foundBrand.length).trim();
             }
        }
        
        match = modelText.match(/([A-Z0-9-]+\w)/);
        if (match) {
            let potentialModel = match[0].split(' ')[0];
            if(potentialModel.length > 3 && !/^\d+HZ$|^\d+P$|^[QFH]HD$|^4K$|^2K$/.test(potentialModel.toUpperCase())) {
                data.model = potentialModel;
            }
        }
        
        return data;
    }
}); 










// 初始化权限控制
function initPermissions() {
    console.log('初始化权限控制...');
    
    // 如果没有权限控制模块，发出警告
    if (typeof setupPermissionBasedUI !== 'function') {
        console.warn('警告：权限控制模块未加载！请确保已引入permission-helper.js');
        return;
    }
    
    // 执行权限控制初始化
    setupPermissionBasedUI().then(() => {
        // 检查编辑按钮
        const editButtons = document.querySelectorAll('.btn-edit, [data-action="edit"]');
        const deleteButtons = document.querySelectorAll('.btn-delete, [data-action="delete"]');
        
        // 根据用户角色处理按钮
        isAdmin().then(isAdminUser => {
            if (!isAdminUser) {
                // 禁用表单
                const form = document.querySelector('form');
                if (form) {
                    const inputs = form.querySelectorAll('input, select, textarea');
                    inputs.forEach(input => {
                        input.setAttribute('disabled', 'disabled');
                    });
                    
                    const submitBtn = form.querySelector('button[type="submit"]');
                    if (submitBtn) {
                        submitBtn.setAttribute('disabled', 'disabled');
                        submitBtn.title = '只有管理员可以添加或修改数据';
                    }
                }
                
                // 隐藏或禁用编辑、删除按钮
                editButtons.forEach(btn => {
                    btn.style.display = 'none';
                });
                
                deleteButtons.forEach(btn => {
                    btn.style.display = 'none';
                });
                
                // 为所有删除操作添加权限验证拦截器
                interceptDeleteOperations();
            } else {
                // 添加管理员标识
                const header = document.querySelector('h1, h2');
                if (header) {
                    const adminBadge = document.createElement('span');
                    adminBadge.className = 'bg-green-500 text-white text-xs px-2 py-1 rounded ml-2';
                    adminBadge.innerText = '管理员';
                    header.appendChild(adminBadge);
                }
            }
        });
    });
}

// 拦截所有删除操作的请求
function interceptDeleteOperations() {
    // 保存原始的fetch函数
    const originalFetch = window.fetch;
    
    // 重写fetch函数以拦截删除请求
    window.fetch = async function(url, options) {
        // 检查是否为删除操作
        if (options && options.method === 'DELETE') {
            console.log('拦截到删除操作请求:', url);
            
            // 检查用户权限
            const isAdminUser = await isAdmin();
            if (!isAdminUser) {
                console.warn('权限不足，普通用户无法执行删除操作');
                // 返回权限错误响应
                return Promise.resolve(new Response(JSON.stringify({
                    error: '权限不足',
                    message: '只有管理员可以删除数据'
                }), {
                    status: 403,
                    headers: {
                        'Content-Type': 'application/json'
                    }
                }));
            }
        }
        
        // 对于非删除请求或管理员的删除请求，正常执行
        return originalFetch.apply(this, arguments);
    };
    
    // 添加全局点击事件拦截
    document.addEventListener('click', async function(event) {
        // 查找可能触发删除操作的元素
        const deleteButton = event.target.closest('.btn-delete, [data-action="delete"]');
        if (deleteButton) {
            const isAdminUser = await isAdmin();
            if (!isAdminUser) {
                // 阻止事件传播
                event.preventDefault();
                event.stopPropagation();
                console.warn('权限不足，普通用户无法执行删除操作');
                
                // 可选：显示提示消息（使用toast或alert）
                if (typeof showToast === 'function') {
                    showToast('只有管理员可以删除数据', 'error');
                } else {
                    console.error('权限不足，只有管理员可以删除数据');
                }
                
                return false;
            }
        }
    }, true);
}

// 进度条相关函数
function showUploadProgress() {
    const progressContainer = document.getElementById('uploadProgressContainer');
    const progressBar = document.getElementById('uploadProgressBar');
    const progressText = document.getElementById('uploadProgressText');
    const progressPercent = document.getElementById('uploadProgressPercent');

    if (progressContainer) {
        progressContainer.classList.remove('hidden');
        progressBar.style.width = '0%';
        if (progressText) {
            progressText.innerHTML = '<i id="uploadProgressIcon" class="fas fa-clock mr-1"></i>准备上传...';
        }
        if (progressPercent) {
            progressPercent.textContent = '0%';
        }
    }
}

function hideUploadProgress() {
    const progressContainer = document.getElementById('uploadProgressContainer');
    if (progressContainer) {
        setTimeout(() => {
            progressContainer.classList.add('hidden');
        }, 1000); // 延迟1秒隐藏，让用户看到完成状态
    }
}

function updateUploadProgress(percent, text, icon = 'fa-upload') {
    const progressBar = document.getElementById('uploadProgressBar');
    const progressText = document.getElementById('uploadProgressText');
    const progressPercent = document.getElementById('uploadProgressPercent');

    if (progressBar) {
        progressBar.style.width = percent + '%';
    }
    if (progressText && text) {
        const iconElement = `<i id="uploadProgressIcon" class="fas ${icon} mr-1"></i>`;
        progressText.innerHTML = iconElement + text;
    }
    if (progressPercent) {
        progressPercent.textContent = Math.round(percent) + '%';
    }
}

function disableSubmitButton(disabled) {
    const submitBtn = document.querySelector('button[type="submit"]');
    if (submitBtn) {
        submitBtn.disabled = disabled;
        if (disabled) {
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i> 正在处理...';
            submitBtn.classList.add('opacity-50', 'cursor-not-allowed');
        } else {
            const isEditingMode = submitBtn.classList.contains('bg-green-600');
            if (isEditingMode) {
                submitBtn.innerHTML = '<i class="fas fa-save mr-1"></i> 更新显示器信息';
            } else {
                submitBtn.innerHTML = '<i class="fas fa-save mr-1"></i> 保存';
            }
            submitBtn.classList.remove('opacity-50', 'cursor-not-allowed');
        }
    }
}

// 带进度的上传函数
function uploadWithProgress(url, method, formData) {
    return new Promise((resolve, reject) => {
        const xhr = new XMLHttpRequest();

        // 设置超时时间（30秒）
        xhr.timeout = 30000;

        // 上传进度监听
        xhr.upload.addEventListener('progress', (e) => {
            if (e.lengthComputable) {
                const percentComplete = (e.loaded / e.total) * 70; // 上传占70%
                const uploadedMB = (e.loaded / 1024 / 1024).toFixed(1);
                const totalMB = (e.total / 1024 / 1024).toFixed(1);
                updateUploadProgress(percentComplete, `正在上传图片... (${uploadedMB}MB / ${totalMB}MB)`, 'fa-upload');
            }
        });

        // 请求状态变化监听
        xhr.addEventListener('readystatechange', () => {
            if (xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {
                updateUploadProgress(75, '上传完成，正在处理数据...', 'fa-check-circle');
            } else if (xhr.readyState === XMLHttpRequest.LOADING) {
                updateUploadProgress(85, '正在转换为WebP格式...', 'fa-sync fa-spin');
            }
        });

        // 请求完成监听
        xhr.addEventListener('load', () => {
            updateUploadProgress(100, '处理完成！', 'fa-check-circle');

            if (xhr.status >= 200 && xhr.status < 300) {
                try {
                    const response = JSON.parse(xhr.responseText);
                    resolve(response);
                } catch (e) {
                    reject(new Error('响应解析失败'));
                }
            } else {
                try {
                    const errorResponse = JSON.parse(xhr.responseText);
                    reject(new Error(errorResponse.message || '操作失败'));
                } catch (e) {
                    reject(new Error(`HTTP ${xhr.status}: ${xhr.statusText}`));
                }
            }
        });

        // 错误监听
        xhr.addEventListener('error', () => {
            reject(new Error('网络错误'));
        });

        // 超时监听
        xhr.addEventListener('timeout', () => {
            reject(new Error('请求超时'));
        });

        // 打开请求
        xhr.open(method, url);

        // 设置请求头（必须在open之后）
        const token = localStorage.getItem('token');
        if (token) {
            xhr.setRequestHeader('Authorization', `Bearer ${token}`);
        }

        // 发送请求
        xhr.send(formData);
    });
}

// 页面加载完成后执行权限初始化
document.addEventListener('DOMContentLoaded', initPermissions);
